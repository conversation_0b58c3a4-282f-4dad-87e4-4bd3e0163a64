#!/usr/bin/env python3
"""
分析微调阶段帧和标注的使用情况
"""

def analyze_frame_usage():
    """分析帧使用情况的具体例子"""
    
    print("=== 微调阶段帧和标注使用分析 ===\n")
    
    # 假设一个序列有100帧
    total_frames = 100
    interval = 10  # 隔10取1
    frames_per_video = 5  # 每5帧一个视频
    
    print(f"📊 序列信息:")
    print(f"   总帧数: {total_frames}")
    print(f"   采样间隔: 隔{interval}取1")
    print(f"   每视频帧数: {frames_per_video}")
    print()
    
    # 1. 隔10取1的帧选择
    selected_indices = list(range(0, total_frames, interval))
    print(f"🎯 隔{interval}取1后选中的帧:")
    print(f"   选中帧索引: {selected_indices}")
    print(f"   选中帧数: {len(selected_indices)}")
    print()
    
    # 2. 被跳过的帧
    all_frames = set(range(total_frames))
    selected_frames = set(selected_indices)
    skipped_frames = all_frames - selected_frames
    
    print(f"❌ 被跳过的帧:")
    print(f"   跳过帧数: {len(skipped_frames)}")
    print(f"   跳过帧示例: {sorted(list(skipped_frames))[:20]}...")
    print()
    
    # 3. 5帧视频片段划分
    print(f"📹 5帧视频片段划分:")
    video_segments = []
    for start_idx in range(0, len(selected_indices), frames_per_video):
        end_idx = min(start_idx + frames_per_video, len(selected_indices))
        segment_indices = selected_indices[start_idx:end_idx]
        video_segments.append(segment_indices)
        print(f"   视频片段 {len(video_segments)}: 帧 {segment_indices}")
    print()
    
    # 4. 标注使用情况
    print(f"🏷️  标注使用情况:")
    print(f"   ✅ 被使用的标注: 只有选中帧的标注")
    print(f"      - 帧 {selected_indices}")
    print(f"   ❌ 被忽略的标注: 跳过帧的标注")
    print(f"      - 帧 1,2,3,4,5,6,7,8,9,11,12,13,14,15,16,17,18,19,21...")
    print()
    
    # 5. 具体例子
    print(f"📝 具体例子 (前30帧):")
    for i in range(min(30, total_frames)):
        if i in selected_frames:
            # 找到这一帧属于哪个视频片段
            video_idx = None
            frame_idx_in_video = None
            for v_idx, segment in enumerate(video_segments):
                if i in segment:
                    video_idx = v_idx + 1
                    frame_idx_in_video = segment.index(i)
                    break
            
            print(f"   帧 {i:2d}: ✅ 选中 -> 视频{video_idx}, 帧{frame_idx_in_video} -> 标注被使用")
        else:
            print(f"   帧 {i:2d}: ❌ 跳过 -> 标注被忽略")
    
    print()
    print(f"📊 总结:")
    print(f"   使用的标注: {len(selected_frames)} 个帧的标注")
    print(f"   忽略的标注: {len(skipped_frames)} 个帧的标注")
    print(f"   标注利用率: {len(selected_frames)/total_frames*100:.1f}%")

if __name__ == "__main__":
    analyze_frame_usage()
