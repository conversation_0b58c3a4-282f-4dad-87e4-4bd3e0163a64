[{"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/0.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 0\n- 采样间隔: 每隔10帧\n- 时序上下文: 当前帧0 (检测目标), 后10帧: 帧10, 后20帧: 帧20\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 0,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 0, \"detections\": [{\"bbox\": [92, 123, 97, 128], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/10.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 10\n- 采样间隔: 每隔10帧\n- 时序上下文: 前10帧: 帧0, 当前帧10 (检测目标), 后10帧: 帧20, 后20帧: 帧30\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 10,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 10, \"detections\": [{\"bbox\": [90, 122, 95, 127], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/20.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 20\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧0, 前10帧: 帧10, 当前帧20 (检测目标), 后10帧: 帧30, 后20帧: 帧40\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 20,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 20, \"detections\": [{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/30.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 30\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧10, 前10帧: 帧20, 当前帧30 (检测目标), 后10帧: 帧40, 后20帧: 帧50\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 30,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 30, \"detections\": [{\"bbox\": [91, 123, 96, 128], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/40.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 40\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧20, 前10帧: 帧30, 当前帧40 (检测目标), 后10帧: 帧50, 后20帧: 帧60\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 40,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 40, \"detections\": [{\"bbox\": [90, 121, 95, 126], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/50.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 50\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧30, 前10帧: 帧40, 当前帧50 (检测目标), 后10帧: 帧60, 后20帧: 帧70\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 50,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 50, \"detections\": [{\"bbox\": [93, 121, 98, 126], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/60.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 60\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧40, 前10帧: 帧50, 当前帧60 (检测目标), 后10帧: 帧70, 后20帧: 帧80\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 60,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 60, \"detections\": [{\"bbox\": [19, 162, 24, 167], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/70.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 70\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧50, 前10帧: 帧60, 当前帧70 (检测目标), 后10帧: 帧80, 后20帧: 帧90\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 70,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 70, \"detections\": [{\"bbox\": [95, 120, 100, 125], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/80.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 80\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧60, 前10帧: 帧70, 当前帧80 (检测目标), 后10帧: 帧90, 后20帧: 帧100\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 80,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 80, \"detections\": [{\"bbox\": [94, 122, 99, 127], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/90.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 90\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧70, 前10帧: 帧80, 当前帧90 (检测目标), 后10帧: 帧100, 后20帧: 帧110\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 90,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 90, \"detections\": [{\"bbox\": [95, 124, 100, 129], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/100.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 100\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧80, 前10帧: 帧90, 当前帧100 (检测目标), 后10帧: 帧110, 后20帧: 帧120\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 100,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 100, \"detections\": [{\"bbox\": [92, 123, 97, 128], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/110.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 110\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧90, 前10帧: 帧100, 当前帧110 (检测目标), 后10帧: 帧120, 后20帧: 帧130\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 110,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 110, \"detections\": [{\"bbox\": [89, 124, 94, 129], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/120.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 120\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧100, 前10帧: 帧110, 当前帧120 (检测目标), 后10帧: 帧130, 后20帧: 帧140\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 120,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 120, \"detections\": [{\"bbox\": [91, 124, 96, 129], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/130.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 130\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧110, 前10帧: 帧120, 当前帧130 (检测目标), 后10帧: 帧140, 后20帧: 帧150\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 130,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 130, \"detections\": [{\"bbox\": [88, 124, 93, 129], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/140.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 140\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧120, 前10帧: 帧130, 当前帧140 (检测目标), 后10帧: 帧150, 后20帧: 帧160\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 140,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 140, \"detections\": [{\"bbox\": [90, 125, 95, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/150.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 150\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧130, 前10帧: 帧140, 当前帧150 (检测目标), 后10帧: 帧160, 后20帧: 帧170\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 150,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 150, \"detections\": [{\"bbox\": [91, 125, 96, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/160.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 160\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧140, 前10帧: 帧150, 当前帧160 (检测目标), 后10帧: 帧170, 后20帧: 帧180\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 160,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 160, \"detections\": [{\"bbox\": [91, 127, 96, 132], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/170.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 170\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧150, 前10帧: 帧160, 当前帧170 (检测目标), 后10帧: 帧180, 后20帧: 帧190\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 170,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 170, \"detections\": [{\"bbox\": [92, 126, 97, 131], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/180.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 180\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧160, 前10帧: 帧170, 当前帧180 (检测目标), 后10帧: 帧190, 后20帧: 帧200\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 180,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 180, \"detections\": [{\"bbox\": [90, 124, 95, 129], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/190.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 190\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧170, 前10帧: 帧180, 当前帧190 (检测目标), 后10帧: 帧200, 后20帧: 帧210\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 190,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 190, \"detections\": [{\"bbox\": [89, 125, 94, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/200.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 200\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧180, 前10帧: 帧190, 当前帧200 (检测目标), 后10帧: 帧210, 后20帧: 帧220\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 200,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 200, \"detections\": [{\"bbox\": [89, 125, 94, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/210.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 210\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧190, 前10帧: 帧200, 当前帧210 (检测目标), 后10帧: 帧220, 后20帧: 帧230\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 210,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 210, \"detections\": [{\"bbox\": [85, 125, 90, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/220.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 220\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧200, 前10帧: 帧210, 当前帧220 (检测目标), 后10帧: 帧230, 后20帧: 帧240\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 220,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 220, \"detections\": [{\"bbox\": [88, 125, 93, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/230.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 230\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧210, 前10帧: 帧220, 当前帧230 (检测目标), 后10帧: 帧240, 后20帧: 帧250\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 230,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 230, \"detections\": [{\"bbox\": [93, 124, 98, 129], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/240.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 240\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧220, 前10帧: 帧230, 当前帧240 (检测目标), 后10帧: 帧250, 后20帧: 帧260\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 240,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 240, \"detections\": [{\"bbox\": [91, 127, 96, 132], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/250.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 250\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧230, 前10帧: 帧240, 当前帧250 (检测目标), 后10帧: 帧260, 后20帧: 帧270\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 250,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 250, \"detections\": [{\"bbox\": [86, 125, 91, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/260.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 260\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧240, 前10帧: 帧250, 当前帧260 (检测目标), 后10帧: 帧270, 后20帧: 帧280\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 260,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 260, \"detections\": [{\"bbox\": [86, 125, 91, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/270.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 270\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧250, 前10帧: 帧260, 当前帧270 (检测目标), 后10帧: 帧280, 后20帧: 帧290\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 270,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 270, \"detections\": [{\"bbox\": [87, 127, 92, 132], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/280.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 280\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧260, 前10帧: 帧270, 当前帧280 (检测目标), 后10帧: 帧290, 后20帧: 帧300\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 280,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 280, \"detections\": [{\"bbox\": [92, 126, 97, 131], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/290.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 290\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧270, 前10帧: 帧280, 当前帧290 (检测目标), 后10帧: 帧300, 后20帧: 帧310\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 290,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 290, \"detections\": [{\"bbox\": [88, 126, 93, 131], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/300.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 300\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧280, 前10帧: 帧290, 当前帧300 (检测目标), 后10帧: 帧310, 后20帧: 帧320\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 300,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 300, \"detections\": [{\"bbox\": [88, 127, 93, 132], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/310.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 310\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧290, 前10帧: 帧300, 当前帧310 (检测目标), 后10帧: 帧320, 后20帧: 帧330\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 310,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 310, \"detections\": [{\"bbox\": [94, 126, 99, 131], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/320.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 320\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧300, 前10帧: 帧310, 当前帧320 (检测目标), 后10帧: 帧330, 后20帧: 帧340\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 320,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 320, \"detections\": [{\"bbox\": [90, 129, 95, 134], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/330.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 330\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧310, 前10帧: 帧320, 当前帧330 (检测目标), 后10帧: 帧340, 后20帧: 帧350\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 330,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 330, \"detections\": [{\"bbox\": [91, 128, 96, 133], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/340.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 340\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧320, 前10帧: 帧330, 当前帧340 (检测目标), 后10帧: 帧350, 后20帧: 帧360\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 340,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 340, \"detections\": [{\"bbox\": [92, 125, 97, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/350.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 350\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧330, 前10帧: 帧340, 当前帧350 (检测目标), 后10帧: 帧360, 后20帧: 帧370\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 350,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 350, \"detections\": [{\"bbox\": [91, 128, 96, 133], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/360.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 360\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧340, 前10帧: 帧350, 当前帧360 (检测目标), 后10帧: 帧370, 后20帧: 帧380\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 360,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 360, \"detections\": [{\"bbox\": [89, 125, 94, 130], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/370.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 370\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧350, 前10帧: 帧360, 当前帧370 (检测目标), 后10帧: 帧380, 后20帧: 帧390\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 370,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 370, \"detections\": [{\"bbox\": [92, 126, 97, 131], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/380.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 380\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧360, 前10帧: 帧370, 当前帧380 (检测目标), 后10帧: 帧390\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 380,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 380, \"detections\": [{\"bbox\": [89, 129, 94, 134], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data06/390.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data06\n- 图像尺寸: (256, 256)\n- 当前帧: 390\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧370, 前10帧: 帧380, 当前帧390 (检测目标)\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 390,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 390, \"detections\": [{\"bbox\": [93, 127, 98, 132], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/0.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 0\n- 采样间隔: 每隔10帧\n- 时序上下文: 当前帧0 (检测目标), 后10帧: 帧10, 后20帧: 帧20\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 0,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 0, \"detections\": [{\"bbox\": [164, 137, 194, 168], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/10.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 10\n- 采样间隔: 每隔10帧\n- 时序上下文: 前10帧: 帧0, 当前帧10 (检测目标), 后10帧: 帧20, 后20帧: 帧30\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 10,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 10, \"detections\": [{\"bbox\": [148, 117, 179, 148], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/20.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 20\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧0, 前10帧: 帧10, 当前帧20 (检测目标), 后10帧: 帧30, 后20帧: 帧40\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 20,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 20, \"detections\": [{\"bbox\": [143, 98, 173, 128], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/30.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 30\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧10, 前10帧: 帧20, 当前帧30 (检测目标), 后10帧: 帧40, 后20帧: 帧50\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 30,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 30, \"detections\": [{\"bbox\": [136, 80, 167, 110], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/40.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 40\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧20, 前10帧: 帧30, 当前帧40 (检测目标), 后10帧: 帧50, 后20帧: 帧60\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 40,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 40, \"detections\": [{\"bbox\": [128, 60, 159, 91], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/50.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 50\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧30, 前10帧: 帧40, 当前帧50 (检测目标), 后10帧: 帧60, 后20帧: 帧70\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 50,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 50, \"detections\": [{\"bbox\": [119, 42, 149, 72], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/60.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 60\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧40, 前10帧: 帧50, 当前帧60 (检测目标), 后10帧: 帧70, 后20帧: 帧80\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 60,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 60, \"detections\": [{\"bbox\": [108, 24, 138, 55], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/70.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 70\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧50, 前10帧: 帧60, 当前帧70 (检测目标), 后10帧: 帧80, 后20帧: 帧90\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 70,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 70, \"detections\": [{\"bbox\": [100, 24, 130, 55], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/80.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 80\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧60, 前10帧: 帧70, 当前帧80 (检测目标), 后10帧: 帧90, 后20帧: 帧100\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 80,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 80, \"detections\": [{\"bbox\": [92, 15, 123, 45], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/90.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 90\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧70, 前10帧: 帧80, 当前帧90 (检测目标), 后10帧: 帧100, 后20帧: 帧110\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 90,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 90, \"detections\": [{\"bbox\": [89, 24, 120, 55], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/100.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 100\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧80, 前10帧: 帧90, 当前帧100 (检测目标), 后10帧: 帧110, 后20帧: 帧120\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 100,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 100, \"detections\": [{\"bbox\": [77, 7, 108, 37], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/110.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 110\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧90, 前10帧: 帧100, 当前帧110 (检测目标), 后10帧: 帧120, 后20帧: 帧130\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 110,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 110, \"detections\": [{\"bbox\": [80, 23, 110, 53], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/120.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 120\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧100, 前10帧: 帧110, 当前帧120 (检测目标), 后10帧: 帧130, 后20帧: 帧140\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 120,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 120, \"detections\": [{\"bbox\": [88, 33, 119, 64], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/130.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 130\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧110, 前10帧: 帧120, 当前帧130 (检测目标), 后10帧: 帧140, 后20帧: 帧150\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 130,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 130, \"detections\": [{\"bbox\": [76, 18, 106, 48], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/140.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 140\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧120, 前10帧: 帧130, 当前帧140 (检测目标), 后10帧: 帧150, 后20帧: 帧160\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 140,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 140, \"detections\": [{\"bbox\": [63, 37, 94, 68], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/150.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 150\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧130, 前10帧: 帧140, 当前帧150 (检测目标), 后10帧: 帧160, 后20帧: 帧170\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 150,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 150, \"detections\": [{\"bbox\": [79, 50, 109, 80], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/160.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 160\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧140, 前10帧: 帧150, 当前帧160 (检测目标), 后10帧: 帧170, 后20帧: 帧180\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 160,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 160, \"detections\": [{\"bbox\": [64, 40, 95, 70], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/170.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 170\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧150, 前10帧: 帧160, 当前帧170 (检测目标), 后10帧: 帧180, 后20帧: 帧190\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 170,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 170, \"detections\": [{\"bbox\": [88, 55, 119, 85], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/180.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 180\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧160, 前10帧: 帧170, 当前帧180 (检测目标), 后10帧: 帧190, 后20帧: 帧200\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 180,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 180, \"detections\": [{\"bbox\": [76, 44, 106, 75], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/190.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 190\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧170, 前10帧: 帧180, 当前帧190 (检测目标), 后10帧: 帧200, 后20帧: 帧210\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 190,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 190, \"detections\": [{\"bbox\": [76, 53, 107, 84], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/200.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 200\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧180, 前10帧: 帧190, 当前帧200 (检测目标), 后10帧: 帧210, 后20帧: 帧220\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 200,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 200, \"detections\": [{\"bbox\": [80, 61, 111, 92], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/210.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 210\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧190, 前10帧: 帧200, 当前帧210 (检测目标), 后10帧: 帧220, 后20帧: 帧230\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 210,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 210, \"detections\": [{\"bbox\": [69, 52, 100, 83], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/220.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 220\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧200, 前10帧: 帧210, 当前帧220 (检测目标), 后10帧: 帧230, 后20帧: 帧240\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 220,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 220, \"detections\": [{\"bbox\": [79, 63, 109, 93], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/230.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 230\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧210, 前10帧: 帧220, 当前帧230 (检测目标), 后10帧: 帧240, 后20帧: 帧250\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 230,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 230, \"detections\": [{\"bbox\": [73, 59, 104, 90], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/240.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 240\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧220, 前10帧: 帧230, 当前帧240 (检测目标), 后10帧: 帧250, 后20帧: 帧260\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 240,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 240, \"detections\": [{\"bbox\": [79, 56, 109, 87], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/250.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 250\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧230, 前10帧: 帧240, 当前帧250 (检测目标), 后10帧: 帧260, 后20帧: 帧270\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 250,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 250, \"detections\": [{\"bbox\": [76, 61, 106, 92], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/260.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 260\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧240, 前10帧: 帧250, 当前帧260 (检测目标), 后10帧: 帧270, 后20帧: 帧280\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 260,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 260, \"detections\": [{\"bbox\": [64, 53, 95, 84], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/270.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 270\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧250, 前10帧: 帧260, 当前帧270 (检测目标), 后10帧: 帧280, 后20帧: 帧290\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 270,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 270, \"detections\": [{\"bbox\": [76, 58, 106, 88], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/280.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 280\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧260, 前10帧: 帧270, 当前帧280 (检测目标), 后10帧: 帧290, 后20帧: 帧300\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 280,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 280, \"detections\": [{\"bbox\": [82, 63, 112, 93], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/290.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 290\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧270, 前10帧: 帧280, 当前帧290 (检测目标), 后10帧: 帧300, 后20帧: 帧310\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 290,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 290, \"detections\": [{\"bbox\": [79, 57, 109, 88], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/300.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 300\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧280, 前10帧: 帧290, 当前帧300 (检测目标), 后10帧: 帧310, 后20帧: 帧320\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 300,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 300, \"detections\": [{\"bbox\": [84, 64, 114, 95], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/310.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 310\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧290, 前10帧: 帧300, 当前帧310 (检测目标), 后10帧: 帧320, 后20帧: 帧330\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 310,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 310, \"detections\": [{\"bbox\": [74, 56, 104, 87], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/320.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 320\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧300, 前10帧: 帧310, 当前帧320 (检测目标), 后10帧: 帧330, 后20帧: 帧340\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 320,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 320, \"detections\": [{\"bbox\": [80, 58, 111, 88], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/330.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 330\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧310, 前10帧: 帧320, 当前帧330 (检测目标), 后10帧: 帧340, 后20帧: 帧350\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 330,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 330, \"detections\": [{\"bbox\": [76, 61, 106, 92], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/340.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 340\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧320, 前10帧: 帧330, 当前帧340 (检测目标), 后10帧: 帧350, 后20帧: 帧360\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 340,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 340, \"detections\": [{\"bbox\": [72, 59, 103, 90], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/350.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 350\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧330, 前10帧: 帧340, 当前帧350 (检测目标), 后10帧: 帧360, 后20帧: 帧370\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 350,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 350, \"detections\": [{\"bbox\": [80, 61, 111, 92], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/360.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 360\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧340, 前10帧: 帧350, 当前帧360 (检测目标), 后10帧: 帧370, 后20帧: 帧380\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 360,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 360, \"detections\": [{\"bbox\": [72, 55, 102, 85], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/370.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 370\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧350, 前10帧: 帧360, 当前帧370 (检测目标), 后10帧: 帧380, 后20帧: 帧390\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 370,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 370, \"detections\": [{\"bbox\": [84, 66, 115, 96], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/380.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 380\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧360, 前10帧: 帧370, 当前帧380 (检测目标), 后10帧: 帧390\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 380,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 380, \"detections\": [{\"bbox\": [88, 63, 119, 94], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}, {"messages": [{"role": "user", "content": [{"type": "image", "image": "dataset/images/data07/390.bmp"}, {"type": "text", "text": "这是一个间隔采样的弱小目标检测任务。\n\n序列信息：\n- 序列ID: data07\n- 图像尺寸: (256, 256)\n- 当前帧: 390\n- 采样间隔: 每隔10帧\n- 时序上下文: 前20帧: 帧370, 前10帧: 帧380, 当前帧390 (检测目标)\n- 目标特征: 3-80像素的弱小目标\n\n任务要求：\n1. 检测当前帧中的弱小目标\n2. 理解这是间隔采样序列中的一帧\n3. 学习时序上下文对检测的影响\n4. 为后续的时序插值提供基础\n\n输出格式：\n{\n  \"frame_id\": 390,\n  \"detections\": [{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别\"}],\n  \"temporal_context\": \"interval_sampling\",\n  \"interpolation_ready\": true\n}\n\n开始检测当前帧："}]}, {"role": "assistant", "content": "{\"frame_id\": 390, \"detections\": [{\"bbox\": [82, 56, 112, 87], \"label\": \"drone\"}], \"temporal_context\": \"interval_sampling\", \"interpolation_ready\": true}"}]}]