[{"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/correct_videos/data06/data06_interval10_complete_sequence.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个完整视频序列中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 采样方式: 每隔10帧抽取1帧\n- 总帧数: 40帧\n- 这是完整的序列，不是片段\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nframe_idx class_name x_center y_center width height confidence\n\n注意：\n1. frame_idx为视频中的帧索引（从0开始）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用完整视频序列的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.371094 0.492188 0.019531 0.019531 1.0\n1 drone 0.363281 0.488281 0.019531 0.019531 1.0\n2 drone 0.363281 0.496094 0.019531 0.019531 1.0\n3 drone 0.367188 0.492188 0.019531 0.019531 1.0\n4 drone 0.363281 0.484375 0.019531 0.019531 1.0\n5 drone 0.375000 0.484375 0.019531 0.019531 1.0\n6 drone 0.085938 0.644531 0.019531 0.019531 1.0\n7 drone 0.382812 0.480469 0.019531 0.019531 1.0\n8 drone 0.378906 0.488281 0.019531 0.019531 1.0\n9 drone 0.382812 0.496094 0.019531 0.019531 1.0\n10 drone 0.371094 0.492188 0.019531 0.019531 1.0\n11 drone 0.359375 0.496094 0.019531 0.019531 1.0\n12 drone 0.367188 0.496094 0.019531 0.019531 1.0\n13 drone 0.355469 0.496094 0.019531 0.019531 1.0\n14 drone 0.363281 0.500000 0.019531 0.019531 1.0\n15 drone 0.367188 0.500000 0.019531 0.019531 1.0\n16 drone 0.367188 0.507812 0.019531 0.019531 1.0\n17 drone 0.371094 0.503906 0.019531 0.019531 1.0\n18 drone 0.363281 0.496094 0.019531 0.019531 1.0\n19 drone 0.359375 0.500000 0.019531 0.019531 1.0\n20 drone 0.359375 0.500000 0.019531 0.019531 1.0\n21 drone 0.343750 0.500000 0.019531 0.019531 1.0\n22 drone 0.355469 0.500000 0.019531 0.019531 1.0\n23 drone 0.375000 0.496094 0.019531 0.019531 1.0\n24 drone 0.367188 0.507812 0.019531 0.019531 1.0\n25 drone 0.347656 0.500000 0.019531 0.019531 1.0\n26 drone 0.347656 0.500000 0.019531 0.019531 1.0\n27 drone 0.351562 0.507812 0.019531 0.019531 1.0\n28 drone 0.371094 0.503906 0.019531 0.019531 1.0\n29 drone 0.355469 0.503906 0.019531 0.019531 1.0\n30 drone 0.355469 0.507812 0.019531 0.019531 1.0\n31 drone 0.378906 0.503906 0.019531 0.019531 1.0\n32 drone 0.363281 0.515625 0.019531 0.019531 1.0\n33 drone 0.367188 0.511719 0.019531 0.019531 1.0\n34 drone 0.371094 0.500000 0.019531 0.019531 1.0\n35 drone 0.367188 0.511719 0.019531 0.019531 1.0\n36 drone 0.359375 0.500000 0.019531 0.019531 1.0\n37 drone 0.371094 0.503906 0.019531 0.019531 1.0\n38 drone 0.359375 0.515625 0.019531 0.019531 1.0\n39 drone 0.375000 0.507812 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/correct_videos/data01/data01_interval10_complete_sequence.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个完整视频序列中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 每隔10帧抽取1帧\n- 总帧数: 150帧\n- 这是完整的序列，不是片段\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nframe_idx class_name x_center y_center width height confidence\n\n注意：\n1. frame_idx为视频中的帧索引（从0开始）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用完整视频序列的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.277344 0.425781 0.019531 0.019531 1.0\n1 drone 0.253906 0.425781 0.019531 0.019531 1.0\n2 drone 0.273438 0.425781 0.019531 0.019531 1.0\n3 drone 0.277344 0.425781 0.019531 0.019531 1.0\n4 drone 0.261719 0.425781 0.019531 0.019531 1.0\n5 drone 0.226562 0.429688 0.019531 0.019531 1.0\n6 drone 0.242188 0.425781 0.019531 0.019531 1.0\n7 drone 0.273438 0.429688 0.019531 0.019531 1.0\n8 drone 0.261719 0.437500 0.019531 0.019531 1.0\n9 drone 0.253906 0.437500 0.019531 0.019531 1.0\n10 drone 0.277344 0.441406 0.019531 0.019531 1.0\n11 drone 0.250000 0.437500 0.019531 0.019531 1.0\n12 drone 0.265625 0.437500 0.019531 0.019531 1.0\n13 drone 0.289062 0.441406 0.019531 0.019531 1.0\n14 drone 0.265625 0.437500 0.019531 0.019531 1.0\n15 drone 0.273438 0.441406 0.019531 0.019531 1.0\n16 drone 0.285156 0.441406 0.019531 0.019531 1.0\n17 drone 0.269531 0.445312 0.019531 0.019531 1.0\n18 drone 0.285156 0.445312 0.019531 0.019531 1.0\n19 drone 0.265625 0.445312 0.019531 0.019531 1.0\n20 drone 0.277344 0.445312 0.019531 0.019531 1.0\n21 drone 0.308594 0.445312 0.019531 0.019531 1.0\n22 drone 0.277344 0.445312 0.019531 0.019531 1.0\n23 drone 0.285156 0.445312 0.019531 0.019531 1.0\n24 drone 0.289062 0.445312 0.019531 0.019531 1.0\n25 drone 0.277344 0.445312 0.019531 0.019531 1.0\n26 drone 0.296875 0.449219 0.019531 0.019531 1.0\n27 drone 0.277344 0.449219 0.019531 0.019531 1.0\n28 drone 0.289062 0.445312 0.019531 0.019531 1.0\n29 drone 0.304688 0.449219 0.019531 0.019531 1.0\n30 drone 0.289062 0.449219 0.019531 0.019531 1.0\n31 drone 0.304688 0.445312 0.019531 0.019531 1.0\n32 drone 0.304688 0.445312 0.019531 0.019531 1.0\n33 drone 0.300781 0.445312 0.019531 0.019531 1.0\n34 drone 0.316406 0.445312 0.019531 0.019531 1.0\n35 drone 0.304688 0.453125 0.019531 0.019531 1.0\n36 drone 0.339844 0.453125 0.019531 0.019531 1.0\n37 drone 0.328125 0.453125 0.019531 0.019531 1.0\n38 drone 0.328125 0.449219 0.019531 0.019531 1.0\n39 drone 0.347656 0.449219 0.019531 0.019531 1.0\n40 drone 0.335938 0.449219 0.019531 0.019531 1.0\n41 drone 0.347656 0.445312 0.019531 0.019531 1.0\n42 drone 0.339844 0.445312 0.019531 0.019531 1.0\n43 drone 0.347656 0.445312 0.019531 0.019531 1.0\n44 drone 0.355469 0.449219 0.019531 0.019531 1.0\n45 drone 0.355469 0.449219 0.019531 0.019531 1.0\n46 drone 0.363281 0.449219 0.019531 0.019531 1.0\n47 drone 0.363281 0.449219 0.019531 0.019531 1.0\n48 drone 0.371094 0.449219 0.019531 0.019531 1.0\n49 drone 0.375000 0.453125 0.019531 0.019531 1.0\n50 drone 0.375000 0.453125 0.019531 0.019531 1.0\n51 drone 0.390625 0.457031 0.019531 0.019531 1.0\n52 drone 0.394531 0.453125 0.019531 0.019531 1.0\n53 drone 0.398438 0.453125 0.019531 0.019531 1.0\n54 drone 0.402344 0.453125 0.019531 0.019531 1.0\n55 drone 0.410156 0.453125 0.019531 0.019531 1.0\n56 drone 0.410156 0.453125 0.019531 0.019531 1.0\n57 drone 0.414062 0.453125 0.019531 0.019531 1.0\n58 drone 0.417969 0.457031 0.019531 0.019531 1.0\n59 drone 0.417969 0.457031 0.019531 0.019531 1.0\n60 drone 0.421875 0.460938 0.019531 0.019531 1.0\n61 drone 0.425781 0.464844 0.019531 0.019531 1.0\n62 drone 0.425781 0.460938 0.019531 0.019531 1.0\n63 drone 0.429688 0.460938 0.019531 0.019531 1.0\n64 drone 0.433594 0.472656 0.019531 0.019531 1.0\n65 drone 0.429688 0.472656 0.019531 0.019531 1.0\n66 drone 0.433594 0.476562 0.019531 0.019531 1.0\n67 drone 0.425781 0.472656 0.019531 0.019531 1.0\n68 drone 0.425781 0.472656 0.019531 0.019531 1.0\n69 drone 0.433594 0.476562 0.019531 0.019531 1.0\n70 drone 0.433594 0.472656 0.019531 0.019531 1.0\n71 drone 0.425781 0.472656 0.019531 0.019531 1.0\n72 drone 0.421875 0.472656 0.019531 0.019531 1.0\n73 drone 0.421875 0.468750 0.019531 0.019531 1.0\n74 drone 0.429688 0.472656 0.019531 0.019531 1.0\n75 drone 0.433594 0.472656 0.019531 0.019531 1.0\n76 drone 0.429688 0.468750 0.019531 0.019531 1.0\n77 drone 0.429688 0.472656 0.019531 0.019531 1.0\n78 drone 0.425781 0.472656 0.019531 0.019531 1.0\n79 drone 0.421875 0.460938 0.019531 0.019531 1.0\n80 drone 0.425781 0.464844 0.019531 0.019531 1.0\n81 drone 0.425781 0.453125 0.019531 0.019531 1.0\n82 drone 0.421875 0.457031 0.019531 0.019531 1.0\n83 drone 0.410156 0.460938 0.019531 0.019531 1.0\n84 drone 0.417969 0.457031 0.019531 0.019531 1.0\n85 drone 0.421875 0.460938 0.019531 0.019531 1.0\n86 drone 0.421875 0.457031 0.019531 0.019531 1.0\n87 drone 0.421875 0.457031 0.019531 0.019531 1.0\n88 drone 0.421875 0.460938 0.019531 0.019531 1.0\n89 drone 0.421875 0.453125 0.019531 0.019531 1.0\n90 drone 0.421875 0.460938 0.019531 0.019531 1.0\n91 drone 0.414062 0.460938 0.019531 0.019531 1.0\n92 drone 0.410156 0.457031 0.019531 0.019531 1.0\n93 drone 0.410156 0.460938 0.019531 0.019531 1.0\n94 drone 0.410156 0.460938 0.019531 0.019531 1.0\n95 drone 0.414062 0.460938 0.019531 0.019531 1.0\n96 drone 0.410156 0.460938 0.019531 0.019531 1.0\n97 drone 0.410156 0.460938 0.019531 0.019531 1.0\n98 drone 0.410156 0.464844 0.019531 0.019531 1.0\n99 drone 0.410156 0.464844 0.019531 0.019531 1.0\n100 drone 0.410156 0.464844 0.019531 0.019531 1.0\n101 drone 0.410156 0.464844 0.019531 0.019531 1.0\n102 drone 0.410156 0.464844 0.019531 0.019531 1.0\n103 drone 0.410156 0.468750 0.019531 0.019531 1.0\n104 drone 0.406250 0.464844 0.019531 0.019531 1.0\n105 drone 0.398438 0.464844 0.019531 0.019531 1.0\n106 drone 0.398438 0.468750 0.019531 0.019531 1.0\n107 drone 0.402344 0.468750 0.019531 0.019531 1.0\n108 drone 0.398438 0.460938 0.019531 0.019531 1.0\n109 drone 0.398438 0.460938 0.019531 0.019531 1.0\n110 drone 0.398438 0.460938 0.019531 0.019531 1.0\n111 drone 0.398438 0.464844 0.019531 0.019531 1.0\n112 drone 0.398438 0.464844 0.019531 0.019531 1.0\n113 drone 0.394531 0.464844 0.019531 0.019531 1.0\n114 drone 0.394531 0.468750 0.019531 0.019531 1.0\n115 drone 0.386719 0.460938 0.019531 0.019531 1.0\n116 drone 0.386719 0.464844 0.019531 0.019531 1.0\n117 drone 0.398438 0.464844 0.019531 0.019531 1.0\n118 drone 0.398438 0.464844 0.019531 0.019531 1.0\n119 drone 0.394531 0.468750 0.019531 0.019531 1.0\n120 drone 0.394531 0.464844 0.019531 0.019531 1.0\n121 drone 0.390625 0.464844 0.019531 0.019531 1.0\n122 drone 0.390625 0.468750 0.019531 0.019531 1.0\n123 drone 0.394531 0.457031 0.019531 0.019531 1.0\n124 drone 0.386719 0.460938 0.019531 0.019531 1.0\n125 drone 0.386719 0.460938 0.019531 0.019531 1.0\n126 drone 0.386719 0.460938 0.019531 0.019531 1.0\n127 drone 0.386719 0.460938 0.019531 0.019531 1.0\n128 drone 0.386719 0.460938 0.019531 0.019531 1.0\n129 drone 0.386719 0.460938 0.019531 0.019531 1.0\n130 drone 0.386719 0.460938 0.019531 0.019531 1.0\n131 drone 0.386719 0.460938 0.019531 0.019531 1.0\n132 drone 0.390625 0.464844 0.019531 0.019531 1.0\n133 drone 0.394531 0.464844 0.019531 0.019531 1.0\n134 drone 0.386719 0.464844 0.019531 0.019531 1.0\n135 drone 0.390625 0.464844 0.019531 0.019531 1.0\n136 drone 0.378906 0.464844 0.019531 0.019531 1.0\n137 drone 0.382812 0.464844 0.019531 0.019531 1.0\n138 drone 0.386719 0.460938 0.019531 0.019531 1.0\n139 drone 0.382812 0.464844 0.019531 0.019531 1.0\n140 drone 0.378906 0.464844 0.019531 0.019531 1.0\n141 drone 0.386719 0.464844 0.019531 0.019531 1.0\n142 drone 0.386719 0.464844 0.019531 0.019531 1.0\n143 drone 0.386719 0.464844 0.019531 0.019531 1.0\n144 drone 0.382812 0.464844 0.019531 0.019531 1.0\n145 drone 0.382812 0.468750 0.019531 0.019531 1.0\n146 drone 0.382812 0.468750 0.019531 0.019531 1.0\n147 drone 0.375000 0.464844 0.019531 0.019531 1.0\n148 drone 0.382812 0.468750 0.019531 0.019531 1.0\n149 drone 0.378906 0.472656 0.019531 0.019531 1.0"}]}]