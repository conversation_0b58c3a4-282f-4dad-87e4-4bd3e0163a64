[{"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.390625 0.019531 0.019531 0.92\n0 1 drone 0.398438 0.382812 0.019531 0.019531 0.86\n0 2 drone 0.394531 0.394531 0.019531 0.019531 0.93\n0 3 drone 0.390625 0.386719 0.019531 0.019531 0.93\n0 4 drone 0.390625 0.394531 0.019531 0.019531 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.316406 0.019531 0.019531 0.85\n0 1 drone 0.417969 0.320312 0.019531 0.019531 0.74\n0 2 drone 0.406250 0.343750 0.019531 0.019531 0.78\n0 3 drone 0.402344 0.324219 0.019531 0.019531 1.0\n0 4 drone 0.417969 0.335938 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1050_1090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1100_1140.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.464844 0.019531 0.019531 0.88\n0 1 drone 0.398438 0.468750 0.019531 0.019531 0.91\n0 2 drone 0.402344 0.468750 0.019531 0.019531 0.72\n0 3 drone 0.398438 0.460938 0.019531 0.019531 0.82\n0 4 drone 0.398438 0.460938 0.019531 0.019531 0.72\n1 0 drone 0.398438 0.460938 0.019531 0.019531 0.74\n1 1 drone 0.398438 0.464844 0.019531 0.019531 0.82\n1 2 drone 0.398438 0.464844 0.019531 0.019531 0.76\n1 3 drone 0.394531 0.464844 0.019531 0.019531 0.94\n1 4 drone 0.394531 0.468750 0.019531 0.019531 0.78\n2 0 drone 0.386719 0.460938 0.019531 0.019531 0.9\n2 1 drone 0.386719 0.464844 0.019531 0.019531 0.78\n2 2 drone 0.398438 0.464844 0.019531 0.019531 0.71\n2 3 drone 0.398438 0.464844 0.019531 0.019531 0.8\n2 4 drone 0.394531 0.468750 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.382812 0.019531 0.019531 0.76\n0 1 drone 0.378906 0.378906 0.019531 0.019531 0.72\n0 2 drone 0.414062 0.382812 0.019531 0.019531 0.79\n0 3 drone 0.394531 0.390625 0.019531 0.019531 0.96\n0 4 drone 0.382812 0.394531 0.019531 0.019531 0.85\n1 0 drone 0.378906 0.390625 0.019531 0.019531 0.76\n1 1 drone 0.378906 0.386719 0.019531 0.019531 0.95\n1 2 drone 0.375000 0.378906 0.019531 0.019531 0.83\n1 3 drone 0.378906 0.382812 0.019531 0.019531 0.91\n1 4 drone 0.382812 0.390625 0.019531 0.019531 0.78\n2 0 drone 0.390625 0.386719 0.019531 0.019531 0.79\n2 1 drone 0.386719 0.390625 0.019531 0.019531 0.95\n2 2 drone 0.386719 0.394531 0.019531 0.019531 0.72\n2 3 drone 0.382812 0.390625 0.019531 0.019531 0.9\n2 4 drone 0.386719 0.394531 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0192_0232.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.360614 0.281307 0.117188 0.117188 0.89\n0 1 drone 0.352882 0.306275 0.117188 0.117188 0.84\n0 2 drone 0.370497 0.293847 0.117188 0.117188 0.75\n0 3 drone 0.365094 0.284734 0.117188 0.117188 0.91\n0 4 drone 0.387205 0.318930 0.117188 0.117188 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.94\n0 1 drone 0.425781 0.464844 0.019531 0.019531 0.86\n0 2 drone 0.425781 0.460938 0.019531 0.019531 0.78\n0 3 drone 0.429688 0.460938 0.019531 0.019531 0.76\n0 4 drone 0.433594 0.472656 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.382812 0.378906 0.019531 0.019531 0.78\n0 1 drone 0.382812 0.367188 0.019531 0.019531 0.86\n0 2 drone 0.394531 0.371094 0.019531 0.019531 0.87\n0 3 drone 0.406250 0.367188 0.019531 0.019531 0.89\n0 4 drone 0.386719 0.367188 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1053_1093.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.389650 0.464638 0.019531 0.019531 0.92\n0 1 drone 0.393635 0.484915 0.019531 0.019531 0.83\n0 2 drone 0.416108 0.476533 0.019531 0.019531 0.72\n0 3 drone 0.410513 0.470169 0.019531 0.019531 0.73\n0 4 drone 0.413526 0.453154 0.019531 0.019531 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1200_1240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1250_1290.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.464844 0.019531 0.019531 0.91\n0 1 drone 0.390625 0.464844 0.019531 0.019531 0.94\n0 2 drone 0.390625 0.468750 0.019531 0.019531 0.76\n0 3 drone 0.394531 0.457031 0.019531 0.019531 0.99\n0 4 drone 0.386719 0.460938 0.019531 0.019531 0.74\n1 0 drone 0.386719 0.460938 0.019531 0.019531 0.82\n1 1 drone 0.386719 0.460938 0.019531 0.019531 0.84\n1 2 drone 0.386719 0.460938 0.019531 0.019531 0.73\n1 3 drone 0.386719 0.460938 0.019531 0.019531 0.87\n1 4 drone 0.386719 0.460938 0.019531 0.019531 0.74\n2 0 drone 0.386719 0.460938 0.019531 0.019531 0.93\n2 1 drone 0.386719 0.460938 0.019531 0.019531 0.85\n2 2 drone 0.390625 0.464844 0.019531 0.019531 0.78\n2 3 drone 0.394531 0.464844 0.019531 0.019531 0.92\n2 4 drone 0.386719 0.464844 0.019531 0.019531 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0234_0274.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.399275 0.313192 0.117188 0.117188 0.88\n0 1 drone 0.355564 0.288114 0.117188 0.117188 0.7\n0 2 drone 0.353489 0.283408 0.117188 0.117188 0.9\n0 3 drone 0.383469 0.309584 0.117188 0.117188 0.93\n0 4 drone 0.353416 0.287864 0.117188 0.117188 0.71"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1200_1240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.464844 0.019531 0.019531 0.92\n0 1 drone 0.390625 0.464844 0.019531 0.019531 0.72\n0 2 drone 0.390625 0.468750 0.019531 0.019531 0.78\n0 3 drone 0.394531 0.457031 0.019531 0.019531 0.91\n0 4 drone 0.386719 0.460938 0.019531 0.019531 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0027_0067.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.422047 0.337963 0.019531 0.019531 0.87\n0 1 drone 0.429051 0.326991 0.019531 0.019531 0.83\n0 2 drone 0.405717 0.320266 0.019531 0.019531 0.93\n0 3 drone 0.404254 0.315572 0.019531 0.019531 0.78\n0 4 drone 0.425989 0.363072 0.019531 0.019531 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.445312 0.019531 0.019531 0.94\n0 1 drone 0.308594 0.445312 0.019531 0.019531 0.88\n0 2 drone 0.277344 0.445312 0.019531 0.019531 1.0\n0 3 drone 0.285156 0.445312 0.019531 0.019531 0.73\n0 4 drone 0.289062 0.445312 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0600_0640.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0650_0690.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.402344 0.019531 0.019531 0.89\n0 1 drone 0.386719 0.394531 0.019531 0.019531 0.9\n0 2 drone 0.386719 0.394531 0.019531 0.019531 0.95\n0 3 drone 0.394531 0.398438 0.019531 0.019531 0.8\n0 4 drone 0.390625 0.394531 0.019531 0.019531 0.8\n1 0 drone 0.386719 0.398438 0.019531 0.019531 0.71\n1 1 drone 0.390625 0.394531 0.019531 0.019531 0.9\n1 2 drone 0.390625 0.394531 0.019531 0.019531 0.83\n1 3 drone 0.386719 0.398438 0.019531 0.019531 0.85\n1 4 drone 0.386719 0.390625 0.019531 0.019531 0.73\n2 0 drone 0.386719 0.394531 0.019531 0.019531 0.77\n2 1 drone 0.386719 0.394531 0.019531 0.019531 0.73\n2 2 drone 0.394531 0.390625 0.019531 0.019531 0.94\n2 3 drone 0.386719 0.394531 0.019531 0.019531 0.73\n2 4 drone 0.386719 0.390625 0.019531 0.019531 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.460938 0.019531 0.019531 0.82\n0 1 drone 0.386719 0.464844 0.019531 0.019531 0.77\n0 2 drone 0.398438 0.464844 0.019531 0.019531 0.88\n0 3 drone 0.398438 0.464844 0.019531 0.019531 0.81\n0 4 drone 0.394531 0.468750 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0582_0622.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.409387 0.379375 0.019531 0.019531 0.94\n0 1 drone 0.374614 0.389887 0.019531 0.019531 0.87\n0 2 drone 0.371003 0.408054 0.019531 0.019531 0.76\n0 3 drone 0.367226 0.375396 0.019531 0.019531 0.99\n0 4 drone 0.376732 0.381064 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_1320_1360.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0900_0940.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.95\n0 1 drone 0.414062 0.460938 0.019531 0.019531 0.78\n0 2 drone 0.410156 0.457031 0.019531 0.019531 0.83\n0 3 drone 0.410156 0.460938 0.019531 0.019531 0.91\n0 4 drone 0.410156 0.460938 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.359375 0.019531 0.019531 0.74\n0 1 drone 0.398438 0.367188 0.019531 0.019531 0.99\n0 2 drone 0.398438 0.359375 0.019531 0.019531 0.94\n0 3 drone 0.398438 0.355469 0.019531 0.019531 0.75\n0 4 drone 0.398438 0.371094 0.019531 0.019531 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.406250 0.335938 0.019531 0.019531 0.7\n0 1 drone 0.414062 0.324219 0.019531 0.019531 0.98\n0 2 drone 0.410156 0.339844 0.019531 0.019531 0.92\n0 3 drone 0.402344 0.324219 0.019531 0.019531 0.86\n0 4 drone 0.398438 0.320312 0.019531 0.019531 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1050_1090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.464844 0.019531 0.019531 0.74\n0 1 drone 0.398438 0.468750 0.019531 0.019531 0.84\n0 2 drone 0.402344 0.468750 0.019531 0.019531 0.95\n0 3 drone 0.398438 0.460938 0.019531 0.019531 0.79\n0 4 drone 0.398438 0.460938 0.019531 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.289062 0.449219 0.019531 0.019531 0.71\n0 1 drone 0.304688 0.445312 0.019531 0.019531 0.78\n0 2 drone 0.304688 0.445312 0.019531 0.019531 0.9\n0 3 drone 0.300781 0.445312 0.019531 0.019531 0.7\n0 4 drone 0.316406 0.445312 0.019531 0.019531 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.355469 0.019531 0.019531 0.98\n0 1 drone 0.386719 0.363281 0.019531 0.019531 0.96\n0 2 drone 0.386719 0.359375 0.019531 0.019531 0.71\n0 3 drone 0.390625 0.359375 0.019531 0.019531 0.84\n0 4 drone 0.386719 0.355469 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.429688 0.472656 0.019531 0.019531 0.93\n0 1 drone 0.433594 0.476562 0.019531 0.019531 0.9\n0 2 drone 0.425781 0.472656 0.019531 0.019531 1.0\n0 3 drone 0.425781 0.472656 0.019531 0.019531 0.81\n0 4 drone 0.433594 0.476562 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1250_1290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.460938 0.019531 0.019531 0.77\n0 1 drone 0.386719 0.460938 0.019531 0.019531 0.86\n0 2 drone 0.386719 0.460938 0.019531 0.019531 0.84\n0 3 drone 0.386719 0.460938 0.019531 0.019531 0.82\n0 4 drone 0.386719 0.460938 0.019531 0.019531 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.414062 0.328125 0.019531 0.019531 0.79\n0 1 drone 0.406250 0.339844 0.019531 0.019531 0.81\n0 2 drone 0.398438 0.367188 0.019531 0.019531 0.73\n0 3 drone 0.414062 0.351562 0.019531 0.019531 0.91\n0 4 drone 0.406250 0.359375 0.019531 0.019531 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.699219 0.597656 0.117188 0.117188 0.9\n0 1 drone 0.640625 0.519531 0.117188 0.117188 0.95\n0 2 drone 0.617188 0.445312 0.117188 0.117188 0.75\n0 3 drone 0.593750 0.371094 0.117188 0.117188 0.71\n0 4 drone 0.562500 0.296875 0.117188 0.117188 0.91\n1 0 drone 0.523438 0.226562 0.117188 0.117188 0.97\n1 1 drone 0.480469 0.156250 0.117188 0.117188 0.74\n1 2 drone 0.449219 0.156250 0.117188 0.117188 0.77\n1 3 drone 0.421875 0.117188 0.117188 0.117188 0.8\n1 4 drone 0.410156 0.156250 0.117188 0.117188 0.87\n2 0 drone 0.363281 0.085938 0.117188 0.117188 0.74\n2 1 drone 0.371094 0.148438 0.117188 0.117188 0.83\n2 2 drone 0.406250 0.191406 0.117188 0.117188 0.77\n2 3 drone 0.355469 0.132812 0.117188 0.117188 0.85\n2 4 drone 0.308594 0.207031 0.117188 0.117188 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.433594 0.472656 0.019531 0.019531 0.99\n0 1 drone 0.429688 0.468750 0.019531 0.019531 0.75\n0 2 drone 0.429688 0.472656 0.019531 0.019531 0.94\n0 3 drone 0.425781 0.472656 0.019531 0.019531 0.96\n0 4 drone 0.421875 0.460938 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.300781 0.117188 0.117188 0.89\n0 1 drone 0.312500 0.269531 0.117188 0.117188 0.91\n0 2 drone 0.355469 0.289062 0.117188 0.117188 0.91\n0 3 drone 0.382812 0.304688 0.117188 0.117188 0.92\n0 4 drone 0.367188 0.285156 0.117188 0.117188 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0450_0490.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0500_0540.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.390625 0.019531 0.019531 0.99\n0 1 drone 0.398438 0.382812 0.019531 0.019531 0.9\n0 2 drone 0.394531 0.394531 0.019531 0.019531 0.97\n0 3 drone 0.390625 0.386719 0.019531 0.019531 0.95\n0 4 drone 0.390625 0.394531 0.019531 0.019531 0.74\n1 0 drone 0.386719 0.390625 0.019531 0.019531 0.81\n1 1 drone 0.386719 0.394531 0.019531 0.019531 0.96\n1 2 drone 0.386719 0.394531 0.019531 0.019531 0.98\n1 3 drone 0.453125 0.375000 0.019531 0.019531 0.73\n1 4 drone 0.441406 0.390625 0.019531 0.019531 0.92\n2 0 drone 0.402344 0.394531 0.019531 0.019531 0.7\n2 1 drone 0.386719 0.390625 0.019531 0.019531 0.89\n2 2 drone 0.386719 0.390625 0.019531 0.019531 0.76\n2 3 drone 0.390625 0.382812 0.019531 0.019531 0.88\n2 4 drone 0.386719 0.394531 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.699219 0.597656 0.117188 0.117188 0.84\n0 1 drone 0.640625 0.519531 0.117188 0.117188 0.87\n0 2 drone 0.617188 0.445312 0.117188 0.117188 0.81\n0 3 drone 0.593750 0.371094 0.117188 0.117188 0.93\n0 4 drone 0.562500 0.296875 0.117188 0.117188 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1388_1428.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.391694 0.461141 0.019531 0.019531 0.81\n0 1 drone 0.370892 0.447744 0.019531 0.019531 0.83\n0 2 drone 0.374194 0.481152 0.019531 0.019531 1.0\n0 3 drone 0.369941 0.481174 0.019531 0.019531 0.75\n0 4 drone 0.404034 0.471989 0.019531 0.019531 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.363281 0.085938 0.117188 0.117188 0.85\n0 1 drone 0.371094 0.148438 0.117188 0.117188 0.9\n0 2 drone 0.406250 0.191406 0.117188 0.117188 0.78\n0 3 drone 0.355469 0.132812 0.117188 0.117188 0.96\n0 4 drone 0.308594 0.207031 0.117188 0.117188 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.406250 0.347656 0.019531 0.019531 0.95\n0 1 drone 0.398438 0.316406 0.019531 0.019531 0.71\n0 2 drone 0.398438 0.339844 0.019531 0.019531 0.91\n0 3 drone 0.402344 0.339844 0.019531 0.019531 0.93\n0 4 drone 0.414062 0.339844 0.019531 0.019531 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.402344 0.394531 0.019531 0.019531 0.82\n0 1 drone 0.386719 0.390625 0.019531 0.019531 1.0\n0 2 drone 0.386719 0.390625 0.019531 0.019531 0.89\n0 3 drone 0.390625 0.382812 0.019531 0.019531 0.74\n0 4 drone 0.386719 0.394531 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.441406 0.378906 0.019531 0.019531 0.92\n0 1 drone 0.417969 0.394531 0.019531 0.019531 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0600_0640.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0650_0690.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.92\n0 1 drone 0.425781 0.464844 0.019531 0.019531 0.89\n0 2 drone 0.425781 0.460938 0.019531 0.019531 0.71\n0 3 drone 0.429688 0.460938 0.019531 0.019531 0.92\n0 4 drone 0.433594 0.472656 0.019531 0.019531 0.83\n1 0 drone 0.429688 0.472656 0.019531 0.019531 0.89\n1 1 drone 0.433594 0.476562 0.019531 0.019531 0.8\n1 2 drone 0.425781 0.472656 0.019531 0.019531 0.76\n1 3 drone 0.425781 0.472656 0.019531 0.019531 0.88\n1 4 drone 0.433594 0.476562 0.019531 0.019531 0.97\n2 0 drone 0.433594 0.472656 0.019531 0.019531 0.8\n2 1 drone 0.425781 0.472656 0.019531 0.019531 0.75\n2 2 drone 0.421875 0.472656 0.019531 0.019531 0.82\n2 3 drone 0.421875 0.468750 0.019531 0.019531 1.0\n2 4 drone 0.429688 0.472656 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0450_0490.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0500_0540.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.449219 0.019531 0.019531 0.76\n0 1 drone 0.363281 0.449219 0.019531 0.019531 0.91\n0 2 drone 0.363281 0.449219 0.019531 0.019531 0.92\n0 3 drone 0.371094 0.449219 0.019531 0.019531 0.88\n0 4 drone 0.375000 0.453125 0.019531 0.019531 0.83\n1 0 drone 0.375000 0.453125 0.019531 0.019531 0.92\n1 1 drone 0.390625 0.457031 0.019531 0.019531 0.83\n1 2 drone 0.394531 0.453125 0.019531 0.019531 0.89\n1 3 drone 0.398438 0.453125 0.019531 0.019531 0.75\n1 4 drone 0.402344 0.453125 0.019531 0.019531 0.99\n2 0 drone 0.410156 0.453125 0.019531 0.019531 0.9\n2 1 drone 0.410156 0.453125 0.019531 0.019531 0.85\n2 2 drone 0.414062 0.453125 0.019531 0.019531 0.85\n2 3 drone 0.417969 0.457031 0.019531 0.019531 0.98\n2 4 drone 0.417969 0.457031 0.019531 0.019531 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1400_1440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.378906 0.464844 0.019531 0.019531 0.86\n0 1 drone 0.386719 0.464844 0.019531 0.019531 0.87\n0 2 drone 0.386719 0.464844 0.019531 0.019531 0.85\n0 3 drone 0.386719 0.464844 0.019531 0.019531 0.73\n0 4 drone 0.382812 0.464844 0.019531 0.019531 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0750_0790.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0800_0840.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.441406 0.378906 0.019531 0.019531 0.73\n0 1 drone 0.417969 0.394531 0.019531 0.019531 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0510_0550.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.398438 0.019531 0.019531 0.92\n0 1 drone 0.390625 0.394531 0.019531 0.019531 0.73\n0 2 drone 0.390625 0.394531 0.019531 0.019531 0.72\n0 3 drone 0.386719 0.398438 0.019531 0.019531 0.81\n0 4 drone 0.386719 0.390625 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.406250 0.371094 0.019531 0.019531 0.73\n0 1 drone 0.394531 0.347656 0.019531 0.019531 0.96\n0 2 drone 0.386719 0.363281 0.019531 0.019531 0.84\n0 3 drone 0.382812 0.359375 0.019531 0.019531 0.76\n0 4 drone 0.398438 0.355469 0.019531 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.367188 0.019531 0.019531 0.83\n0 1 drone 0.390625 0.359375 0.019531 0.019531 0.71\n0 2 drone 0.382812 0.359375 0.019531 0.019531 0.92\n0 3 drone 0.382812 0.371094 0.019531 0.019531 0.75\n0 4 drone 0.398438 0.359375 0.019531 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.289062 0.449219 0.019531 0.019531 0.77\n0 1 drone 0.304688 0.445312 0.019531 0.019531 0.73\n0 2 drone 0.304688 0.445312 0.019531 0.019531 0.91\n0 3 drone 0.300781 0.445312 0.019531 0.019531 0.7\n0 4 drone 0.316406 0.445312 0.019531 0.019531 0.95\n1 0 drone 0.304688 0.453125 0.019531 0.019531 0.81\n1 1 drone 0.339844 0.453125 0.019531 0.019531 0.73\n1 2 drone 0.328125 0.453125 0.019531 0.019531 0.85\n1 3 drone 0.328125 0.449219 0.019531 0.019531 0.98\n1 4 drone 0.347656 0.449219 0.019531 0.019531 0.89\n2 0 drone 0.335938 0.449219 0.019531 0.019531 0.94\n2 1 drone 0.347656 0.445312 0.019531 0.019531 0.87\n2 2 drone 0.339844 0.445312 0.019531 0.019531 0.89\n2 3 drone 0.347656 0.445312 0.019531 0.019531 0.79\n2 4 drone 0.355469 0.449219 0.019531 0.019531 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.363281 0.019531 0.019531 0.76\n0 1 drone 0.398438 0.363281 0.019531 0.019531 0.87\n0 2 drone 0.394531 0.359375 0.019531 0.019531 0.95\n0 3 drone 0.394531 0.367188 0.019531 0.019531 0.96\n0 4 drone 0.394531 0.355469 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0268_0308.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.368394 0.502655 0.019531 0.019531 0.96\n0 1 drone 0.379656 0.487985 0.019531 0.019531 0.74\n0 2 drone 0.336664 0.493653 0.019531 0.019531 0.81\n0 3 drone 0.368933 0.493922 0.019531 0.019531 0.9\n0 4 drone 0.355343 0.518069 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.304688 0.453125 0.019531 0.019531 0.89\n0 1 drone 0.339844 0.453125 0.019531 0.019531 0.83\n0 2 drone 0.328125 0.453125 0.019531 0.019531 0.94\n0 3 drone 0.328125 0.449219 0.019531 0.019531 0.95\n0 4 drone 0.347656 0.449219 0.019531 0.019531 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0940_0980.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0104_0144.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.411195 0.353348 0.019531 0.019531 0.96\n0 1 drone 0.375646 0.319387 0.019531 0.019531 0.95\n0 2 drone 0.416980 0.336322 0.019531 0.019531 0.81\n0 3 drone 0.416811 0.345021 0.019531 0.019531 0.77\n0 4 drone 0.411659 0.345504 0.019531 0.019531 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.375000 0.300781 0.117188 0.117188 0.73\n0 1 drone 0.339844 0.273438 0.117188 0.117188 0.83\n0 2 drone 0.390625 0.320312 0.117188 0.117188 0.72\n0 3 drone 0.406250 0.308594 0.117188 0.117188 0.74\n0 4 drone 0.382812 0.281250 0.117188 0.117188 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.390625 0.019531 0.019531 0.81\n0 1 drone 0.386719 0.394531 0.019531 0.019531 0.95\n0 2 drone 0.386719 0.394531 0.019531 0.019531 0.87\n0 3 drone 0.453125 0.375000 0.019531 0.019531 0.99\n0 4 drone 0.441406 0.390625 0.019531 0.019531 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.441406 0.019531 0.019531 0.92\n0 1 drone 0.250000 0.437500 0.019531 0.019531 0.95\n0 2 drone 0.265625 0.437500 0.019531 0.019531 0.96\n0 3 drone 0.289062 0.441406 0.019531 0.019531 0.75\n0 4 drone 0.265625 0.437500 0.019531 0.019531 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.507812 0.019531 0.019531 0.91\n0 1 drone 0.378906 0.503906 0.019531 0.019531 0.95\n0 2 drone 0.363281 0.515625 0.019531 0.019531 0.92\n0 3 drone 0.367188 0.511719 0.019531 0.019531 0.73\n0 4 drone 0.371094 0.500000 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.355469 0.019531 0.019531 0.7\n0 1 drone 0.386719 0.363281 0.019531 0.019531 0.83\n0 2 drone 0.386719 0.359375 0.019531 0.019531 0.74\n0 3 drone 0.390625 0.359375 0.019531 0.019531 0.82\n0 4 drone 0.386719 0.355469 0.019531 0.019531 0.87\n1 0 drone 0.394531 0.359375 0.019531 0.019531 0.83\n1 1 drone 0.398438 0.367188 0.019531 0.019531 0.83\n1 2 drone 0.398438 0.359375 0.019531 0.019531 0.99\n1 3 drone 0.398438 0.355469 0.019531 0.019531 0.8\n1 4 drone 0.398438 0.371094 0.019531 0.019531 0.97\n2 0 drone 0.394531 0.371094 0.019531 0.019531 0.91\n2 1 drone 0.386719 0.367188 0.019531 0.019531 0.95\n2 2 drone 0.386719 0.375000 0.019531 0.019531 0.77\n2 3 drone 0.382812 0.371094 0.019531 0.019531 0.78\n2 4 drone 0.378906 0.363281 0.019531 0.019531 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.312500 0.117188 0.117188 0.73\n0 1 drone 0.351562 0.281250 0.117188 0.117188 0.86\n0 2 drone 0.375000 0.289062 0.117188 0.117188 0.86\n0 3 drone 0.355469 0.300781 0.117188 0.117188 0.72\n0 4 drone 0.343750 0.292969 0.117188 0.117188 0.96\n1 0 drone 0.375000 0.300781 0.117188 0.117188 0.9\n1 1 drone 0.339844 0.273438 0.117188 0.117188 0.93\n1 2 drone 0.390625 0.320312 0.117188 0.117188 0.74\n1 3 drone 0.406250 0.308594 0.117188 0.117188 0.88\n1 4 drone 0.382812 0.281250 0.117188 0.117188 0.73"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1350_1390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1400_1440.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.464844 0.019531 0.019531 0.96\n0 1 drone 0.378906 0.464844 0.019531 0.019531 0.86\n0 2 drone 0.382812 0.464844 0.019531 0.019531 0.93\n0 3 drone 0.386719 0.460938 0.019531 0.019531 0.85\n0 4 drone 0.382812 0.464844 0.019531 0.019531 0.94\n1 0 drone 0.378906 0.464844 0.019531 0.019531 0.89\n1 1 drone 0.386719 0.464844 0.019531 0.019531 0.83\n1 2 drone 0.386719 0.464844 0.019531 0.019531 0.85\n1 3 drone 0.386719 0.464844 0.019531 0.019531 0.99\n1 4 drone 0.382812 0.464844 0.019531 0.019531 0.72\n2 0 drone 0.382812 0.468750 0.019531 0.019531 0.76\n2 1 drone 0.382812 0.468750 0.019531 0.019531 0.91\n2 2 drone 0.375000 0.464844 0.019531 0.019531 0.93\n2 3 drone 0.382812 0.468750 0.019531 0.019531 0.87\n2 4 drone 0.378906 0.472656 0.019531 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.382812 0.019531 0.019531 0.84\n0 1 drone 0.378906 0.378906 0.019531 0.019531 0.76\n0 2 drone 0.414062 0.382812 0.019531 0.019531 0.84\n0 3 drone 0.394531 0.390625 0.019531 0.019531 0.84\n0 4 drone 0.382812 0.394531 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1350_1390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.464844 0.019531 0.019531 0.71\n0 1 drone 0.378906 0.464844 0.019531 0.019531 0.72\n0 2 drone 0.382812 0.464844 0.019531 0.019531 0.96\n0 3 drone 0.386719 0.460938 0.019531 0.019531 0.98\n0 4 drone 0.382812 0.464844 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.500000 0.019531 0.019531 0.74\n0 1 drone 0.367188 0.507812 0.019531 0.019531 0.89\n0 2 drone 0.371094 0.503906 0.019531 0.019531 0.94\n0 3 drone 0.363281 0.496094 0.019531 0.019531 0.87\n0 4 drone 0.359375 0.500000 0.019531 0.019531 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.394531 0.019531 0.019531 0.79\n0 1 drone 0.386719 0.394531 0.019531 0.019531 0.93\n0 2 drone 0.394531 0.390625 0.019531 0.019531 0.9\n0 3 drone 0.386719 0.394531 0.019531 0.019531 0.74\n0 4 drone 0.386719 0.390625 0.019531 0.019531 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.425781 0.019531 0.019531 0.86\n0 1 drone 0.253906 0.425781 0.019531 0.019531 0.71\n0 2 drone 0.273438 0.425781 0.019531 0.019531 0.85\n0 3 drone 0.277344 0.425781 0.019531 0.019531 0.81\n0 4 drone 0.261719 0.425781 0.019531 0.019531 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.507812 0.019531 0.019531 0.94\n0 1 drone 0.378906 0.503906 0.019531 0.019531 0.97\n0 2 drone 0.363281 0.515625 0.019531 0.019531 0.82\n0 3 drone 0.367188 0.511719 0.019531 0.019531 0.82\n0 4 drone 0.371094 0.500000 0.019531 0.019531 0.86\n1 0 drone 0.367188 0.511719 0.019531 0.019531 0.77\n1 1 drone 0.359375 0.500000 0.019531 0.019531 0.85\n1 2 drone 0.371094 0.503906 0.019531 0.019531 0.82\n1 3 drone 0.359375 0.515625 0.019531 0.019531 0.72\n1 4 drone 0.375000 0.507812 0.019531 0.019531 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.433594 0.472656 0.019531 0.019531 0.98\n0 1 drone 0.425781 0.472656 0.019531 0.019531 0.89\n0 2 drone 0.421875 0.472656 0.019531 0.019531 0.78\n0 3 drone 0.421875 0.468750 0.019531 0.019531 0.78\n0 4 drone 0.429688 0.472656 0.019531 0.019531 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.257812 0.117188 0.117188 0.7\n0 1 drone 0.312500 0.214844 0.117188 0.117188 0.72\n0 2 drone 0.406250 0.273438 0.117188 0.117188 0.71\n0 3 drone 0.355469 0.234375 0.117188 0.117188 0.74\n0 4 drone 0.359375 0.269531 0.117188 0.117188 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.410156 0.464844 0.019531 0.019531 0.78\n0 1 drone 0.410156 0.464844 0.019531 0.019531 0.88\n0 2 drone 0.410156 0.464844 0.019531 0.019531 0.81\n0 3 drone 0.410156 0.468750 0.019531 0.019531 0.83\n0 4 drone 0.406250 0.464844 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.355469 0.019531 0.019531 0.96\n0 1 drone 0.382812 0.351562 0.019531 0.019531 0.87\n0 2 drone 0.378906 0.371094 0.019531 0.019531 0.88\n0 3 drone 0.394531 0.355469 0.019531 0.019531 0.79\n0 4 drone 0.398438 0.359375 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.273438 0.441406 0.019531 0.019531 0.89\n0 1 drone 0.285156 0.441406 0.019531 0.019531 0.92\n0 2 drone 0.269531 0.445312 0.019531 0.019531 0.78\n0 3 drone 0.285156 0.445312 0.019531 0.019531 0.81\n0 4 drone 0.265625 0.445312 0.019531 0.019531 0.71"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.257812 0.117188 0.117188 0.87\n0 1 drone 0.312500 0.214844 0.117188 0.117188 0.77\n0 2 drone 0.406250 0.273438 0.117188 0.117188 0.73\n0 3 drone 0.355469 0.234375 0.117188 0.117188 0.79\n0 4 drone 0.359375 0.269531 0.117188 0.117188 0.88\n1 0 drone 0.375000 0.300781 0.117188 0.117188 0.79\n1 1 drone 0.332031 0.265625 0.117188 0.117188 0.72\n1 2 drone 0.367188 0.304688 0.117188 0.117188 0.81\n1 3 drone 0.347656 0.292969 0.117188 0.117188 0.97\n1 4 drone 0.367188 0.281250 0.117188 0.117188 0.97\n2 0 drone 0.355469 0.300781 0.117188 0.117188 0.78\n2 1 drone 0.312500 0.269531 0.117188 0.117188 0.97\n2 2 drone 0.355469 0.289062 0.117188 0.117188 0.73\n2 3 drone 0.382812 0.304688 0.117188 0.117188 0.9\n2 4 drone 0.367188 0.285156 0.117188 0.117188 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.425781 0.464844 0.019531 0.019531 0.89\n0 1 drone 0.425781 0.453125 0.019531 0.019531 0.71\n0 2 drone 0.421875 0.457031 0.019531 0.019531 0.92\n0 3 drone 0.410156 0.460938 0.019531 0.019531 0.81\n0 4 drone 0.417969 0.457031 0.019531 0.019531 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.375000 0.484375 0.019531 0.019531 0.9\n0 1 drone 0.085938 0.644531 0.019531 0.019531 0.8\n0 2 drone 0.382812 0.480469 0.019531 0.019531 0.89\n0 3 drone 0.378906 0.488281 0.019531 0.019531 0.78\n0 4 drone 0.382812 0.496094 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.449219 0.019531 0.019531 0.75\n0 1 drone 0.363281 0.449219 0.019531 0.019531 0.96\n0 2 drone 0.363281 0.449219 0.019531 0.019531 0.89\n0 3 drone 0.371094 0.449219 0.019531 0.019531 0.79\n0 4 drone 0.375000 0.453125 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.410156 0.453125 0.019531 0.019531 0.94\n0 1 drone 0.410156 0.453125 0.019531 0.019531 0.96\n0 2 drone 0.414062 0.453125 0.019531 0.019531 0.74\n0 3 drone 0.417969 0.457031 0.019531 0.019531 0.99\n0 4 drone 0.417969 0.457031 0.019531 0.019531 0.73"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.382812 0.378906 0.019531 0.019531 0.85\n0 1 drone 0.382812 0.367188 0.019531 0.019531 0.82\n0 2 drone 0.394531 0.371094 0.019531 0.019531 0.81\n0 3 drone 0.406250 0.367188 0.019531 0.019531 0.79\n0 4 drone 0.386719 0.367188 0.019531 0.019531 0.84\n1 0 drone 0.386719 0.367188 0.019531 0.019531 0.8\n1 1 drone 0.378906 0.367188 0.019531 0.019531 0.74\n1 2 drone 0.386719 0.382812 0.019531 0.019531 0.89\n1 3 drone 0.386719 0.371094 0.019531 0.019531 0.76\n1 4 drone 0.382812 0.375000 0.019531 0.019531 0.76\n2 0 drone 0.386719 0.382812 0.019531 0.019531 0.87\n2 1 drone 0.394531 0.386719 0.019531 0.019531 0.98\n2 2 drone 0.390625 0.382812 0.019531 0.019531 0.8\n2 3 drone 0.386719 0.375000 0.019531 0.019531 0.98\n2 4 drone 0.390625 0.390625 0.019531 0.019531 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1338_1378.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.406607 0.471598 0.019531 0.019531 0.88\n0 1 drone 0.378190 0.474419 0.019531 0.019531 0.88\n0 2 drone 0.366702 0.448342 0.019531 0.019531 0.98\n0 3 drone 0.381457 0.480203 0.019531 0.019531 0.95\n0 4 drone 0.371084 0.460481 0.019531 0.019531 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.425781 0.019531 0.019531 0.76\n0 1 drone 0.253906 0.425781 0.019531 0.019531 0.9\n0 2 drone 0.273438 0.425781 0.019531 0.019531 0.74\n0 3 drone 0.277344 0.425781 0.019531 0.019531 0.73\n0 4 drone 0.261719 0.425781 0.019531 0.019531 1.0\n1 0 drone 0.226562 0.429688 0.019531 0.019531 0.87\n1 1 drone 0.242188 0.425781 0.019531 0.019531 0.87\n1 2 drone 0.273438 0.429688 0.019531 0.019531 0.87\n1 3 drone 0.261719 0.437500 0.019531 0.019531 0.91\n1 4 drone 0.253906 0.437500 0.019531 0.019531 0.75\n2 0 drone 0.277344 0.441406 0.019531 0.019531 0.87\n2 1 drone 0.250000 0.437500 0.019531 0.019531 0.85\n2 2 drone 0.265625 0.437500 0.019531 0.019531 0.93\n2 3 drone 0.289062 0.441406 0.019531 0.019531 0.88\n2 4 drone 0.265625 0.437500 0.019531 0.019531 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.378906 0.390625 0.019531 0.019531 0.88\n0 1 drone 0.378906 0.386719 0.019531 0.019531 0.76\n0 2 drone 0.375000 0.378906 0.019531 0.019531 0.73\n0 3 drone 0.378906 0.382812 0.019531 0.019531 0.96\n0 4 drone 0.382812 0.390625 0.019531 0.019531 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.445312 0.019531 0.019531 0.74\n0 1 drone 0.296875 0.449219 0.019531 0.019531 0.74\n0 2 drone 0.277344 0.449219 0.019531 0.019531 0.73\n0 3 drone 0.289062 0.445312 0.019531 0.019531 0.94\n0 4 drone 0.304688 0.449219 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.371094 0.492188 0.019531 0.019531 0.88\n0 1 drone 0.363281 0.488281 0.019531 0.019531 0.86\n0 2 drone 0.363281 0.496094 0.019531 0.019531 0.76\n0 3 drone 0.367188 0.492188 0.019531 0.019531 0.95\n0 4 drone 0.363281 0.484375 0.019531 0.019531 0.81\n1 0 drone 0.375000 0.484375 0.019531 0.019531 0.79\n1 1 drone 0.085938 0.644531 0.019531 0.019531 0.9\n1 2 drone 0.382812 0.480469 0.019531 0.019531 0.72\n1 3 drone 0.378906 0.488281 0.019531 0.019531 0.86\n1 4 drone 0.382812 0.496094 0.019531 0.019531 0.83\n2 0 drone 0.371094 0.492188 0.019531 0.019531 0.81\n2 1 drone 0.359375 0.496094 0.019531 0.019531 0.87\n2 2 drone 0.367188 0.496094 0.019531 0.019531 0.96\n2 3 drone 0.355469 0.496094 0.019531 0.019531 0.95\n2 4 drone 0.363281 0.500000 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.371094 0.019531 0.019531 0.79\n0 1 drone 0.386719 0.367188 0.019531 0.019531 0.73\n0 2 drone 0.386719 0.375000 0.019531 0.019531 0.97\n0 3 drone 0.382812 0.371094 0.019531 0.019531 0.9\n0 4 drone 0.378906 0.363281 0.019531 0.019531 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.402344 0.019531 0.019531 0.75\n0 1 drone 0.386719 0.394531 0.019531 0.019531 0.95\n0 2 drone 0.386719 0.394531 0.019531 0.019531 0.94\n0 3 drone 0.394531 0.398438 0.019531 0.019531 0.83\n0 4 drone 0.390625 0.394531 0.019531 0.019531 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.375000 0.453125 0.019531 0.019531 0.82\n0 1 drone 0.390625 0.457031 0.019531 0.019531 0.91\n0 2 drone 0.394531 0.453125 0.019531 0.019531 0.99\n0 3 drone 0.398438 0.453125 0.019531 0.019531 0.83\n0 4 drone 0.402344 0.453125 0.019531 0.019531 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0950_0990.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.414062 0.460938 0.019531 0.019531 0.91\n0 1 drone 0.410156 0.460938 0.019531 0.019531 0.99\n0 2 drone 0.410156 0.460938 0.019531 0.019531 0.88\n0 3 drone 0.410156 0.464844 0.019531 0.019531 0.76\n0 4 drone 0.410156 0.464844 0.019531 0.019531 0.7"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.273438 0.441406 0.019531 0.019531 0.75\n0 1 drone 0.285156 0.441406 0.019531 0.019531 0.91\n0 2 drone 0.269531 0.445312 0.019531 0.019531 0.93\n0 3 drone 0.285156 0.445312 0.019531 0.019531 0.92\n0 4 drone 0.265625 0.445312 0.019531 0.019531 0.9\n1 0 drone 0.277344 0.445312 0.019531 0.019531 0.7\n1 1 drone 0.308594 0.445312 0.019531 0.019531 0.77\n1 2 drone 0.277344 0.445312 0.019531 0.019531 0.75\n1 3 drone 0.285156 0.445312 0.019531 0.019531 0.97\n1 4 drone 0.289062 0.445312 0.019531 0.019531 0.76\n2 0 drone 0.277344 0.445312 0.019531 0.019531 0.85\n2 1 drone 0.296875 0.449219 0.019531 0.019531 0.76\n2 2 drone 0.277344 0.449219 0.019531 0.019531 0.76\n2 3 drone 0.289062 0.445312 0.019531 0.019531 0.99\n2 4 drone 0.304688 0.449219 0.019531 0.019531 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_1240_1280.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.347656 0.500000 0.019531 0.019531 0.98\n0 1 drone 0.347656 0.500000 0.019531 0.019531 0.99\n0 2 drone 0.351562 0.507812 0.019531 0.019531 0.83\n0 3 drone 0.371094 0.503906 0.019531 0.019531 0.77\n0 4 drone 0.355469 0.503906 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.371094 0.492188 0.019531 0.019531 0.74\n0 1 drone 0.359375 0.496094 0.019531 0.019531 0.86\n0 2 drone 0.367188 0.496094 0.019531 0.019531 0.75\n0 3 drone 0.355469 0.496094 0.019531 0.019531 0.76\n0 4 drone 0.363281 0.500000 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1100_1140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.460938 0.019531 0.019531 0.92\n0 1 drone 0.398438 0.464844 0.019531 0.019531 0.74\n0 2 drone 0.398438 0.464844 0.019531 0.019531 0.76\n0 3 drone 0.394531 0.464844 0.019531 0.019531 0.77\n0 4 drone 0.394531 0.468750 0.019531 0.019531 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0198_0238.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.349451 0.512885 0.019531 0.019531 0.83\n0 1 drone 0.339852 0.513448 0.019531 0.019531 0.72\n0 2 drone 0.378433 0.509070 0.019531 0.019531 0.85\n0 3 drone 0.382405 0.514582 0.019531 0.019531 0.9\n0 4 drone 0.375760 0.512819 0.019531 0.019531 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0489_0529.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.399423 0.390175 0.019531 0.019531 0.85\n0 1 drone 0.374898 0.377445 0.019531 0.019531 0.98\n0 2 drone 0.406648 0.386363 0.019531 0.019531 0.89\n0 3 drone 0.390522 0.399744 0.019531 0.019531 0.94\n0 4 drone 0.380293 0.381770 0.019531 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0337_0377.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.345310 0.512568 0.019531 0.019531 0.73\n0 1 drone 0.353556 0.503072 0.019531 0.019531 0.72\n0 2 drone 0.343208 0.483746 0.019531 0.019531 0.92\n0 3 drone 0.336793 0.504435 0.019531 0.019531 0.76\n0 4 drone 0.380380 0.492591 0.019531 0.019531 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0215_0255.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.410123 0.318395 0.117188 0.117188 0.76\n0 1 drone 0.347082 0.274716 0.117188 0.117188 0.75\n0 2 drone 0.385720 0.303701 0.117188 0.117188 0.91\n0 3 drone 0.378717 0.316878 0.117188 0.117188 0.89\n0 4 drone 0.351536 0.273346 0.117188 0.117188 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.382812 0.468750 0.019531 0.019531 0.73\n0 1 drone 0.382812 0.468750 0.019531 0.019531 0.7\n0 2 drone 0.375000 0.464844 0.019531 0.019531 0.7\n0 3 drone 0.382812 0.468750 0.019531 0.019531 0.79\n0 4 drone 0.378906 0.472656 0.019531 0.019531 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.523438 0.226562 0.117188 0.117188 0.76\n0 1 drone 0.480469 0.156250 0.117188 0.117188 0.86\n0 2 drone 0.449219 0.156250 0.117188 0.117188 0.79\n0 3 drone 0.421875 0.117188 0.117188 0.117188 0.7\n0 4 drone 0.410156 0.156250 0.117188 0.117188 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.316406 0.019531 0.019531 0.98\n0 1 drone 0.417969 0.320312 0.019531 0.019531 0.76\n0 2 drone 0.406250 0.343750 0.019531 0.019531 0.98\n0 3 drone 0.402344 0.324219 0.019531 0.019531 0.94\n0 4 drone 0.417969 0.335938 0.019531 0.019531 0.8\n1 0 drone 0.406250 0.335938 0.019531 0.019531 0.95\n1 1 drone 0.414062 0.324219 0.019531 0.019531 0.91\n1 2 drone 0.410156 0.339844 0.019531 0.019531 0.91\n1 3 drone 0.402344 0.324219 0.019531 0.019531 0.72\n1 4 drone 0.398438 0.320312 0.019531 0.019531 0.79\n2 0 drone 0.406250 0.347656 0.019531 0.019531 0.86\n2 1 drone 0.398438 0.316406 0.019531 0.019531 0.83\n2 2 drone 0.398438 0.339844 0.019531 0.019531 0.84\n2 3 drone 0.402344 0.339844 0.019531 0.019531 0.96\n2 4 drone 0.414062 0.339844 0.019531 0.019531 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.367188 0.019531 0.019531 0.86\n0 1 drone 0.390625 0.359375 0.019531 0.019531 0.74\n0 2 drone 0.382812 0.359375 0.019531 0.019531 0.97\n0 3 drone 0.382812 0.371094 0.019531 0.019531 0.77\n0 4 drone 0.398438 0.359375 0.019531 0.019531 0.83\n1 0 drone 0.398438 0.363281 0.019531 0.019531 0.8\n1 1 drone 0.398438 0.363281 0.019531 0.019531 0.93\n1 2 drone 0.394531 0.359375 0.019531 0.019531 0.72\n1 3 drone 0.394531 0.367188 0.019531 0.019531 0.84\n1 4 drone 0.394531 0.355469 0.019531 0.019531 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.460938 0.019531 0.019531 0.83\n0 1 drone 0.386719 0.460938 0.019531 0.019531 0.84\n0 2 drone 0.390625 0.464844 0.019531 0.019531 0.96\n0 3 drone 0.394531 0.464844 0.019531 0.019531 0.93\n0 4 drone 0.386719 0.464844 0.019531 0.019531 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0750_0790.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0800_0840.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.433594 0.472656 0.019531 0.019531 0.92\n0 1 drone 0.429688 0.468750 0.019531 0.019531 0.78\n0 2 drone 0.429688 0.472656 0.019531 0.019531 0.93\n0 3 drone 0.425781 0.472656 0.019531 0.019531 0.78\n0 4 drone 0.421875 0.460938 0.019531 0.019531 0.88\n1 0 drone 0.425781 0.464844 0.019531 0.019531 0.9\n1 1 drone 0.425781 0.453125 0.019531 0.019531 0.83\n1 2 drone 0.421875 0.457031 0.019531 0.019531 0.73\n1 3 drone 0.410156 0.460938 0.019531 0.019531 0.77\n1 4 drone 0.417969 0.457031 0.019531 0.019531 0.92\n2 0 drone 0.421875 0.460938 0.019531 0.019531 0.85\n2 1 drone 0.421875 0.457031 0.019531 0.019531 0.73\n2 2 drone 0.421875 0.457031 0.019531 0.019531 0.9\n2 3 drone 0.421875 0.460938 0.019531 0.019531 0.96\n2 4 drone 0.421875 0.453125 0.019531 0.019531 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0900_0940.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0950_0990.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.95\n0 1 drone 0.414062 0.460938 0.019531 0.019531 0.88\n0 2 drone 0.410156 0.457031 0.019531 0.019531 0.78\n0 3 drone 0.410156 0.460938 0.019531 0.019531 0.93\n0 4 drone 0.410156 0.460938 0.019531 0.019531 0.86\n1 0 drone 0.414062 0.460938 0.019531 0.019531 0.91\n1 1 drone 0.410156 0.460938 0.019531 0.019531 0.72\n1 2 drone 0.410156 0.460938 0.019531 0.019531 0.82\n1 3 drone 0.410156 0.464844 0.019531 0.019531 0.96\n1 4 drone 0.410156 0.464844 0.019531 0.019531 0.79\n2 0 drone 0.410156 0.464844 0.019531 0.019531 0.71\n2 1 drone 0.410156 0.464844 0.019531 0.019531 0.92\n2 2 drone 0.410156 0.464844 0.019531 0.019531 0.98\n2 3 drone 0.410156 0.468750 0.019531 0.019531 0.81\n2 4 drone 0.406250 0.464844 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.500000 0.019531 0.019531 0.79\n0 1 drone 0.367188 0.507812 0.019531 0.019531 0.9\n0 2 drone 0.371094 0.503906 0.019531 0.019531 1.0\n0 3 drone 0.363281 0.496094 0.019531 0.019531 0.72\n0 4 drone 0.359375 0.500000 0.019531 0.019531 0.99\n1 0 drone 0.359375 0.500000 0.019531 0.019531 0.92\n1 1 drone 0.343750 0.500000 0.019531 0.019531 0.76\n1 2 drone 0.355469 0.500000 0.019531 0.019531 0.98\n1 3 drone 0.375000 0.496094 0.019531 0.019531 0.74\n1 4 drone 0.367188 0.507812 0.019531 0.019531 0.74\n2 0 drone 0.347656 0.500000 0.019531 0.019531 0.71\n2 1 drone 0.347656 0.500000 0.019531 0.019531 0.71\n2 2 drone 0.351562 0.507812 0.019531 0.019531 0.85\n2 3 drone 0.371094 0.503906 0.019531 0.019531 0.98\n2 4 drone 0.355469 0.503906 0.019531 0.019531 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.414062 0.328125 0.019531 0.019531 0.73\n0 1 drone 0.406250 0.339844 0.019531 0.019531 0.92\n0 2 drone 0.398438 0.367188 0.019531 0.019531 0.98\n0 3 drone 0.414062 0.351562 0.019531 0.019531 0.96\n0 4 drone 0.406250 0.359375 0.019531 0.019531 0.99\n1 0 drone 0.394531 0.355469 0.019531 0.019531 0.79\n1 1 drone 0.382812 0.351562 0.019531 0.019531 0.84\n1 2 drone 0.378906 0.371094 0.019531 0.019531 0.97\n1 3 drone 0.394531 0.355469 0.019531 0.019531 0.92\n1 4 drone 0.398438 0.359375 0.019531 0.019531 0.92\n2 0 drone 0.406250 0.371094 0.019531 0.019531 0.85\n2 1 drone 0.394531 0.347656 0.019531 0.019531 0.71\n2 2 drone 0.386719 0.363281 0.019531 0.019531 0.74\n2 3 drone 0.382812 0.359375 0.019531 0.019531 0.71\n2 4 drone 0.398438 0.355469 0.019531 0.019531 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0131_0171.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398657 0.370569 0.019531 0.019531 0.72\n0 1 drone 0.374601 0.343976 0.019531 0.019531 0.77\n0 2 drone 0.377970 0.365880 0.019531 0.019531 0.82\n0 3 drone 0.375286 0.369058 0.019531 0.019531 0.77\n0 4 drone 0.400724 0.361143 0.019531 0.019531 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.511719 0.019531 0.019531 0.73\n0 1 drone 0.359375 0.500000 0.019531 0.019531 0.98\n0 2 drone 0.371094 0.503906 0.019531 0.019531 0.77\n0 3 drone 0.359375 0.515625 0.019531 0.019531 0.81\n0 4 drone 0.375000 0.507812 0.019531 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.226562 0.429688 0.019531 0.019531 0.9\n0 1 drone 0.242188 0.425781 0.019531 0.019531 0.92\n0 2 drone 0.273438 0.429688 0.019531 0.019531 0.88\n0 3 drone 0.261719 0.437500 0.019531 0.019531 0.87\n0 4 drone 0.253906 0.437500 0.019531 0.019531 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.386719 0.019531 0.019531 0.9\n0 1 drone 0.386719 0.390625 0.019531 0.019531 0.99\n0 2 drone 0.386719 0.394531 0.019531 0.019531 0.79\n0 3 drone 0.382812 0.390625 0.019531 0.019531 0.81\n0 4 drone 0.386719 0.394531 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.367188 0.019531 0.019531 0.72\n0 1 drone 0.378906 0.367188 0.019531 0.019531 0.8\n0 2 drone 0.386719 0.382812 0.019531 0.019531 0.94\n0 3 drone 0.386719 0.371094 0.019531 0.019531 0.81\n0 4 drone 0.382812 0.375000 0.019531 0.019531 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.371094 0.492188 0.019531 0.019531 0.73\n0 1 drone 0.363281 0.488281 0.019531 0.019531 0.81\n0 2 drone 0.363281 0.496094 0.019531 0.019531 0.78\n0 3 drone 0.367188 0.492188 0.019531 0.019531 0.92\n0 4 drone 0.363281 0.484375 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.375000 0.300781 0.117188 0.117188 0.9\n0 1 drone 0.332031 0.265625 0.117188 0.117188 0.85\n0 2 drone 0.367188 0.304688 0.117188 0.117188 0.7\n0 3 drone 0.347656 0.292969 0.117188 0.117188 0.96\n0 4 drone 0.367188 0.281250 0.117188 0.117188 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.335938 0.449219 0.019531 0.019531 0.89\n0 1 drone 0.347656 0.445312 0.019531 0.019531 0.72\n0 2 drone 0.339844 0.445312 0.019531 0.019531 0.92\n0 3 drone 0.347656 0.445312 0.019531 0.019531 0.83\n0 4 drone 0.355469 0.449219 0.019531 0.019531 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0235_0275.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.393725 0.367299 0.019531 0.019531 0.82\n0 1 drone 0.415051 0.357665 0.019531 0.019531 0.84\n0 2 drone 0.390923 0.345580 0.019531 0.019531 0.81\n0 3 drone 0.393378 0.358466 0.019531 0.019531 0.97\n0 4 drone 0.393144 0.346218 0.019531 0.019531 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.382812 0.019531 0.019531 0.98\n0 1 drone 0.394531 0.386719 0.019531 0.019531 0.94\n0 2 drone 0.390625 0.382812 0.019531 0.019531 0.8\n0 3 drone 0.386719 0.375000 0.019531 0.019531 0.72\n0 4 drone 0.390625 0.390625 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.99\n0 1 drone 0.421875 0.457031 0.019531 0.019531 0.93\n0 2 drone 0.421875 0.457031 0.019531 0.019531 0.91\n0 3 drone 0.421875 0.460938 0.019531 0.019531 0.8\n0 4 drone 0.421875 0.453125 0.019531 0.019531 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.312500 0.117188 0.117188 0.77\n0 1 drone 0.351562 0.281250 0.117188 0.117188 0.77\n0 2 drone 0.375000 0.289062 0.117188 0.117188 0.87\n0 3 drone 0.355469 0.300781 0.117188 0.117188 0.71\n0 4 drone 0.343750 0.292969 0.117188 0.117188 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0510_0550.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.359375 0.500000 0.019531 0.019531 0.93\n0 1 drone 0.343750 0.500000 0.019531 0.019531 0.9\n0 2 drone 0.355469 0.500000 0.019531 0.019531 0.97\n0 3 drone 0.375000 0.496094 0.019531 0.019531 0.86\n0 4 drone 0.367188 0.507812 0.019531 0.019531 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}]