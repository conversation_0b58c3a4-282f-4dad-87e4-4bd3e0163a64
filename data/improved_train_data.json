[{"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.355469 0.019531 0.019531 0.77\n0 1 drone 0.382812 0.351562 0.019531 0.019531 0.77\n0 2 drone 0.378906 0.371094 0.019531 0.019531 0.73\n0 3 drone 0.394531 0.355469 0.019531 0.019531 0.83\n0 4 drone 0.398438 0.359375 0.019531 0.019531 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.441406 0.129883 0.020313 0.029297 0.9\n0 1 drone 0.439844 0.127930 0.023438 0.033203 0.95\n0 2 drone 0.443750 0.128906 0.025000 0.031250 0.89\n0 3 drone 0.443750 0.129883 0.021875 0.029297 0.81\n0 4 drone 0.442969 0.130859 0.023438 0.031250 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.502344 0.539062 0.017188 0.019531 0.87\n0 1 drone 0.502344 0.540039 0.017188 0.021484 0.78\n0 2 drone 0.494531 0.539062 0.017188 0.023438 0.94\n0 3 drone 0.496875 0.533203 0.018750 0.023438 0.97\n0 4 drone 0.500000 0.539062 0.018750 0.019531 0.94\n1 0 drone 0.500781 0.541016 0.017188 0.019531 0.95\n1 1 drone 0.499219 0.541016 0.017188 0.019531 0.87\n1 2 drone 0.494531 0.538086 0.017188 0.017578 0.93\n1 3 drone 0.497656 0.533203 0.017188 0.015625 0.93\n1 4 drone 0.500781 0.541992 0.017188 0.017578 0.93\n2 0 drone 0.497656 0.539062 0.020313 0.019531 0.76\n2 1 drone 0.496875 0.532227 0.015625 0.017578 0.82\n2 2 drone 0.496875 0.536133 0.015625 0.017578 0.98\n2 3 drone 0.496094 0.540039 0.017188 0.017578 0.72\n2 4 drone 0.495312 0.536133 0.015625 0.017578 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1250_1290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.432812 0.125000 0.021875 0.035156 0.73\n0 1 drone 0.429688 0.117188 0.021875 0.031250 0.74\n0 2 drone 0.432812 0.121094 0.021875 0.027344 0.88\n0 3 drone 0.437500 0.123047 0.021875 0.031250 0.95\n0 4 drone 0.442969 0.127930 0.023438 0.029297 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.507812 0.019531 0.019531 0.93\n0 1 drone 0.378906 0.503906 0.019531 0.019531 0.82\n0 2 drone 0.363281 0.515625 0.019531 0.019531 0.93\n0 3 drone 0.367188 0.511719 0.019531 0.019531 0.71\n0 4 drone 0.371094 0.500000 0.019531 0.019531 0.73\n1 0 drone 0.367188 0.511719 0.019531 0.019531 0.92\n1 1 drone 0.359375 0.500000 0.019531 0.019531 0.74\n1 2 drone 0.371094 0.503906 0.019531 0.019531 0.99\n1 3 drone 0.359375 0.515625 0.019531 0.019531 0.87\n1 4 drone 0.375000 0.507812 0.019531 0.019531 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.445312 0.019531 0.019531 0.78\n0 1 drone 0.308594 0.445312 0.019531 0.019531 0.74\n0 2 drone 0.277344 0.445312 0.019531 0.019531 0.79\n0 3 drone 0.285156 0.445312 0.019531 0.019531 0.8\n0 4 drone 0.289062 0.445312 0.019531 0.019531 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.699219 0.597656 0.117188 0.117188 0.9\n0 1 drone 0.640625 0.519531 0.117188 0.117188 0.94\n0 2 drone 0.617188 0.445312 0.117188 0.117188 0.87\n0 3 drone 0.593750 0.371094 0.117188 0.117188 0.72\n0 4 drone 0.562500 0.296875 0.117188 0.117188 0.89\n1 0 drone 0.523438 0.226562 0.117188 0.117188 0.8\n1 1 drone 0.480469 0.156250 0.117188 0.117188 0.74\n1 2 drone 0.449219 0.156250 0.117188 0.117188 0.86\n1 3 drone 0.421875 0.117188 0.117188 0.117188 0.9\n1 4 drone 0.410156 0.156250 0.117188 0.117188 0.8\n2 0 drone 0.363281 0.085938 0.117188 0.117188 0.9\n2 1 drone 0.371094 0.148438 0.117188 0.117188 0.84\n2 2 drone 0.406250 0.191406 0.117188 0.117188 0.82\n2 3 drone 0.355469 0.132812 0.117188 0.117188 0.89\n2 4 drone 0.308594 0.207031 0.117188 0.117188 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1050_1090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.464844 0.019531 0.019531 0.86\n0 1 drone 0.398438 0.468750 0.019531 0.019531 0.71\n0 2 drone 0.402344 0.468750 0.019531 0.019531 0.98\n0 3 drone 0.398438 0.460938 0.019531 0.019531 0.92\n0 4 drone 0.398438 0.460938 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.403125 0.347656 0.021875 0.011719 0.77\n0 1 drone 0.417187 0.336914 0.021875 0.009766 0.7\n0 2 drone 0.435937 0.323242 0.018750 0.009766 0.8\n0 3 drone 0.450781 0.311523 0.023438 0.009766 0.93\n0 4 drone 0.463281 0.298828 0.023438 0.011719 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.371094 0.492188 0.019531 0.019531 0.78\n0 1 drone 0.363281 0.488281 0.019531 0.019531 0.95\n0 2 drone 0.363281 0.496094 0.019531 0.019531 0.99\n0 3 drone 0.367188 0.492188 0.019531 0.019531 0.74\n0 4 drone 0.363281 0.484375 0.019531 0.019531 0.71\n1 0 drone 0.375000 0.484375 0.019531 0.019531 0.96\n1 1 drone 0.085938 0.644531 0.019531 0.019531 0.73\n1 2 drone 0.382812 0.480469 0.019531 0.019531 0.73\n1 3 drone 0.378906 0.488281 0.019531 0.019531 0.73\n1 4 drone 0.382812 0.496094 0.019531 0.019531 0.99\n2 0 drone 0.371094 0.492188 0.019531 0.019531 0.86\n2 1 drone 0.359375 0.496094 0.019531 0.019531 0.84\n2 2 drone 0.367188 0.496094 0.019531 0.019531 0.73\n2 3 drone 0.355469 0.496094 0.019531 0.019531 0.7\n2 4 drone 0.363281 0.500000 0.019531 0.019531 0.71"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.489063 0.538086 0.021875 0.021484 0.77\n0 1 drone 0.546094 0.536133 0.020313 0.021484 0.98\n0 2 drone 0.513281 0.538086 0.020313 0.021484 0.95\n0 3 drone 0.505469 0.538086 0.020313 0.025391 0.81\n0 4 drone 0.482812 0.539062 0.018750 0.023438 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.300781 0.117188 0.117188 0.95\n0 1 drone 0.312500 0.269531 0.117188 0.117188 0.98\n0 2 drone 0.355469 0.289062 0.117188 0.117188 0.9\n0 3 drone 0.382812 0.304688 0.117188 0.117188 0.75\n0 4 drone 0.367188 0.285156 0.117188 0.117188 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.390625 0.019531 0.019531 0.8\n0 1 drone 0.398438 0.382812 0.019531 0.019531 0.83\n0 2 drone 0.394531 0.394531 0.019531 0.019531 0.8\n0 3 drone 0.390625 0.386719 0.019531 0.019531 0.77\n0 4 drone 0.390625 0.394531 0.019531 0.019531 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.485156 0.540039 0.020313 0.021484 0.75\n0 1 drone 0.527344 0.537109 0.020313 0.023438 0.73\n0 2 drone 0.535156 0.538086 0.020313 0.021484 0.71\n0 3 drone 0.485938 0.539062 0.018750 0.023438 0.95\n0 4 drone 0.474219 0.537109 0.020313 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1200_1240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1250_1290.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.455469 0.144531 0.020313 0.027344 0.93\n0 1 drone 0.452344 0.151367 0.020313 0.029297 0.95\n0 2 drone 0.450000 0.154297 0.025000 0.031250 0.9\n0 3 drone 0.442188 0.158203 0.025000 0.031250 0.94\n0 4 drone 0.438281 0.159180 0.023438 0.029297 0.99\n1 0 drone 0.432812 0.125000 0.021875 0.035156 0.98\n1 1 drone 0.429688 0.117188 0.021875 0.031250 0.77\n1 2 drone 0.432812 0.121094 0.021875 0.027344 0.75\n1 3 drone 0.437500 0.123047 0.021875 0.031250 0.84\n1 4 drone 0.442969 0.127930 0.023438 0.029297 0.75\n2 0 drone 0.441406 0.129883 0.020313 0.029297 0.78\n2 1 drone 0.439844 0.127930 0.023438 0.033203 0.99\n2 2 drone 0.443750 0.128906 0.025000 0.031250 0.85\n2 3 drone 0.443750 0.129883 0.021875 0.029297 0.92\n2 4 drone 0.442969 0.130859 0.023438 0.031250 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.445312 0.295898 0.018750 0.009766 0.78\n0 1 drone 0.444531 0.303711 0.020313 0.009766 0.83\n0 2 drone 0.446875 0.309570 0.018750 0.005859 0.71\n0 3 drone 0.451562 0.316406 0.012500 0.007812 0.87\n0 4 drone 0.454688 0.329102 0.012500 0.009766 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0601_0641.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.427515 0.454070 0.019531 0.019531 0.81\n0 1 drone 0.410010 0.469656 0.019531 0.019531 0.87\n0 2 drone 0.445119 0.471855 0.019531 0.019531 0.84\n0 3 drone 0.431104 0.474395 0.019531 0.019531 0.79\n0 4 drone 0.418387 0.490882 0.019531 0.019531 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.375000 0.453125 0.019531 0.019531 0.89\n0 1 drone 0.390625 0.457031 0.019531 0.019531 1.0\n0 2 drone 0.394531 0.453125 0.019531 0.019531 0.94\n0 3 drone 0.398438 0.453125 0.019531 0.019531 0.82\n0 4 drone 0.402344 0.453125 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.441406 0.378906 0.019531 0.019531 0.97\n0 1 drone 0.417969 0.394531 0.019531 0.019531 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.499219 0.538086 0.014063 0.017578 0.76\n0 1 drone 0.497656 0.535156 0.014063 0.019531 0.98\n0 2 drone 0.495312 0.537109 0.015625 0.019531 0.72\n0 3 drone 0.493750 0.540039 0.015625 0.017578 0.79\n0 4 drone 0.499219 0.536133 0.017188 0.017578 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.339062 0.086914 0.012500 0.013672 0.99\n0 1 drone 0.330469 0.123047 0.010937 0.015625 0.75\n0 2 drone 0.319531 0.241211 0.014063 0.021484 0.86\n0 3 drone 0.307031 0.378906 0.010937 0.019531 0.9\n0 4 drone 0.292969 0.514648 0.014063 0.021484 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.273438 0.441406 0.019531 0.019531 0.85\n0 1 drone 0.285156 0.441406 0.019531 0.019531 0.74\n0 2 drone 0.269531 0.445312 0.019531 0.019531 0.93\n0 3 drone 0.285156 0.445312 0.019531 0.019531 0.75\n0 4 drone 0.265625 0.445312 0.019531 0.019531 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.507812 0.019531 0.019531 0.76\n0 1 drone 0.378906 0.503906 0.019531 0.019531 0.77\n0 2 drone 0.363281 0.515625 0.019531 0.019531 0.71\n0 3 drone 0.367188 0.511719 0.019531 0.019531 0.93\n0 4 drone 0.371094 0.500000 0.019531 0.019531 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0900_0940.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0950_0990.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.685156 0.225586 0.014063 0.009766 0.93\n0 1 drone 0.682813 0.224609 0.015625 0.011719 0.92\n0 2 drone 0.679688 0.222656 0.015625 0.011719 0.71\n0 3 drone 0.676562 0.222656 0.015625 0.011719 0.84\n0 4 drone 0.673438 0.216797 0.015625 0.011719 0.89\n1 0 drone 0.673438 0.210938 0.015625 0.011719 0.94\n1 1 drone 0.670312 0.207031 0.015625 0.011719 0.96\n1 2 drone 0.670312 0.207031 0.015625 0.011719 0.93\n1 3 drone 0.667188 0.204102 0.015625 0.009766 0.93\n1 4 drone 0.664062 0.193359 0.015625 0.011719 0.87\n2 0 drone 0.662500 0.193359 0.015625 0.011719 0.72\n2 1 drone 0.660937 0.201172 0.015625 0.011719 0.91\n2 2 drone 0.660937 0.201172 0.015625 0.011719 0.89\n2 3 drone 0.660156 0.201172 0.017188 0.011719 0.91\n2 4 drone 0.660937 0.202148 0.015625 0.009766 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.403906 0.517578 0.023438 0.011719 0.87\n0 1 drone 0.403906 0.517578 0.023438 0.011719 1.0\n0 2 drone 0.405469 0.514648 0.023438 0.009766 0.9\n0 3 drone 0.405469 0.517578 0.023438 0.011719 0.9\n0 4 drone 0.407031 0.517578 0.023438 0.011719 0.73"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.511719 0.019531 0.019531 0.73\n0 1 drone 0.359375 0.500000 0.019531 0.019531 0.73\n0 2 drone 0.371094 0.503906 0.019531 0.019531 0.97\n0 3 drone 0.359375 0.515625 0.019531 0.019531 0.71\n0 4 drone 0.375000 0.507812 0.019531 0.019531 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0620_0660.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.404687 0.499023 0.021875 0.009766 0.8\n0 1 drone 0.403125 0.502930 0.021875 0.009766 0.92\n0 2 drone 0.403125 0.507812 0.021875 0.011719 0.85\n0 3 drone 0.403125 0.510742 0.021875 0.009766 0.84\n0 4 drone 0.403906 0.513672 0.023438 0.011719 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0676_0716.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.348694 0.501014 0.010937 0.013672 0.92\n0 1 drone 0.328410 0.499591 0.012500 0.015625 0.86\n0 2 drone 0.346365 0.485157 0.010937 0.013672 0.81\n0 3 drone 0.354605 0.473311 0.010937 0.013672 0.92\n0 4 drone 0.358402 0.472714 0.012500 0.013672 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0900_0940.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.685156 0.225586 0.014063 0.009766 0.79\n0 1 drone 0.682813 0.224609 0.015625 0.011719 0.79\n0 2 drone 0.679688 0.222656 0.015625 0.011719 0.97\n0 3 drone 0.676562 0.222656 0.015625 0.011719 0.84\n0 4 drone 0.673438 0.216797 0.015625 0.011719 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.497656 0.539062 0.020313 0.019531 0.85\n0 1 drone 0.496875 0.532227 0.015625 0.017578 0.8\n0 2 drone 0.496875 0.536133 0.015625 0.017578 0.94\n0 3 drone 0.496094 0.540039 0.017188 0.017578 0.95\n0 4 drone 0.495312 0.536133 0.015625 0.017578 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.400000 0.520508 0.021875 0.009766 0.97\n0 1 drone 0.400000 0.519531 0.021875 0.007812 0.76\n0 2 drone 0.400781 0.519531 0.017188 0.007812 0.93\n0 3 drone 0.400000 0.515625 0.021875 0.011719 0.94\n0 4 drone 0.398438 0.510742 0.021875 0.009766 0.73"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.257812 0.117188 0.117188 0.89\n0 1 drone 0.312500 0.214844 0.117188 0.117188 0.89\n0 2 drone 0.406250 0.273438 0.117188 0.117188 0.82\n0 3 drone 0.355469 0.234375 0.117188 0.117188 0.96\n0 4 drone 0.359375 0.269531 0.117188 0.117188 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.753125 0.229492 0.021875 0.021484 0.79\n0 2 drone 0.752344 0.213867 0.023438 0.017578 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1350_1390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1400_1440.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.342969 0.209961 0.004687 0.005859 0.96\n0 1 drone 0.337500 0.211914 0.012500 0.009766 0.92\n0 2 drone 0.346875 0.212891 0.006250 0.007812 0.74\n0 3 drone 0.348438 0.212891 0.006250 0.007812 0.9\n0 4 drone 0.320312 0.211914 0.006250 0.009766 0.83\n1 0 drone 0.288281 0.211914 0.014063 0.009766 0.94\n1 1 drone 0.270313 0.208984 0.015625 0.015625 0.9\n1 2 drone 0.275000 0.207031 0.015625 0.011719 1.0\n1 3 drone 0.291406 0.206055 0.017188 0.009766 0.74\n1 4 drone 0.309375 0.204102 0.015625 0.009766 0.94\n2 0 drone 0.313281 0.205078 0.014063 0.011719 0.84\n2 1 drone 0.310937 0.207031 0.012500 0.011719 0.87\n2 2 drone 0.314844 0.207031 0.010937 0.011719 0.88\n2 3 drone 0.346094 0.211914 0.004687 0.005859 0.9\n2 4 drone 0.381250 0.213867 0.006250 0.005859 0.73"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0275_0315.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.494006 0.536866 0.020313 0.019531 0.82\n0 1 drone 0.466980 0.528904 0.018750 0.019531 0.98\n0 2 drone 0.518925 0.553125 0.017188 0.021484 0.86\n0 3 drone 0.512612 0.538636 0.018750 0.021484 0.73\n0 4 drone 0.494259 0.545483 0.020313 0.021484 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.355469 0.019531 0.019531 0.8\n0 1 drone 0.386719 0.363281 0.019531 0.019531 0.94\n0 2 drone 0.386719 0.359375 0.019531 0.019531 0.75\n0 3 drone 0.390625 0.359375 0.019531 0.019531 0.73\n0 4 drone 0.386719 0.355469 0.019531 0.019531 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.414062 0.328125 0.019531 0.019531 0.83\n0 1 drone 0.406250 0.339844 0.019531 0.019531 0.71\n0 2 drone 0.398438 0.367188 0.019531 0.019531 0.89\n0 3 drone 0.414062 0.351562 0.019531 0.019531 0.84\n0 4 drone 0.406250 0.359375 0.019531 0.019531 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.500000 0.019531 0.019531 0.73\n0 1 drone 0.367188 0.507812 0.019531 0.019531 0.81\n0 2 drone 0.371094 0.503906 0.019531 0.019531 0.94\n0 3 drone 0.363281 0.496094 0.019531 0.019531 0.81\n0 4 drone 0.359375 0.500000 0.019531 0.019531 0.76\n1 0 drone 0.359375 0.500000 0.019531 0.019531 0.93\n1 1 drone 0.343750 0.500000 0.019531 0.019531 0.76\n1 2 drone 0.355469 0.500000 0.019531 0.019531 0.83\n1 3 drone 0.375000 0.496094 0.019531 0.019531 0.83\n1 4 drone 0.367188 0.507812 0.019531 0.019531 0.77\n2 0 drone 0.347656 0.500000 0.019531 0.019531 0.91\n2 1 drone 0.347656 0.500000 0.019531 0.019531 0.88\n2 2 drone 0.351562 0.507812 0.019531 0.019531 0.83\n2 3 drone 0.371094 0.503906 0.019531 0.019531 0.71\n2 4 drone 0.355469 0.503906 0.019531 0.019531 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.413281 0.346680 0.023438 0.013672 0.88\n0 1 drone 0.414062 0.346680 0.021875 0.013672 1.0\n0 2 drone 0.414062 0.346680 0.021875 0.013672 0.74\n0 3 drone 0.415625 0.344727 0.021875 0.013672 0.95\n0 4 drone 0.418750 0.342773 0.021875 0.013672 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1050_1090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1100_1140.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.464844 0.019531 0.019531 0.73\n0 1 drone 0.398438 0.468750 0.019531 0.019531 0.78\n0 2 drone 0.402344 0.468750 0.019531 0.019531 0.77\n0 3 drone 0.398438 0.460938 0.019531 0.019531 0.76\n0 4 drone 0.398438 0.460938 0.019531 0.019531 0.78\n1 0 drone 0.398438 0.460938 0.019531 0.019531 0.71\n1 1 drone 0.398438 0.464844 0.019531 0.019531 0.88\n1 2 drone 0.398438 0.464844 0.019531 0.019531 1.0\n1 3 drone 0.394531 0.464844 0.019531 0.019531 0.74\n1 4 drone 0.394531 0.468750 0.019531 0.019531 0.81\n2 0 drone 0.386719 0.460938 0.019531 0.019531 0.88\n2 1 drone 0.386719 0.464844 0.019531 0.019531 0.83\n2 2 drone 0.398438 0.464844 0.019531 0.019531 0.93\n2 3 drone 0.398438 0.464844 0.019531 0.019531 0.72\n2 4 drone 0.394531 0.468750 0.019531 0.019531 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1200_1240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.669531 0.222656 0.017188 0.011719 1.0\n0 1 drone 0.675000 0.225586 0.015625 0.013672 0.91\n0 2 drone 0.680469 0.225586 0.017188 0.013672 0.92\n0 3 drone 0.684375 0.225586 0.015625 0.009766 0.8\n0 4 drone 0.685937 0.226562 0.015625 0.007812 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1350_1390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1400_1440.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.443750 0.132812 0.021875 0.031250 0.75\n0 1 drone 0.442969 0.134766 0.023438 0.031250 0.96\n0 2 drone 0.442969 0.136719 0.023438 0.031250 0.91\n0 3 drone 0.447656 0.133789 0.023438 0.033203 0.88\n0 4 drone 0.447656 0.130859 0.023438 0.031250 0.94\n1 0 drone 0.448437 0.129883 0.025000 0.033203 0.82\n1 1 drone 0.453125 0.128906 0.021875 0.031250 0.87\n1 2 drone 0.460156 0.123047 0.023438 0.031250 1.0\n1 3 drone 0.464062 0.112305 0.025000 0.033203 0.74\n1 4 drone 0.470313 0.105469 0.021875 0.031250 0.78\n2 0 drone 0.475781 0.099609 0.020313 0.031250 0.91\n2 1 drone 0.478125 0.099609 0.021875 0.031250 0.78\n2 2 drone 0.480469 0.087891 0.017188 0.023438 0.73\n2 3 drone 0.480469 0.026367 0.020313 0.025391 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0750_0790.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0800_0840.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.365625 0.374023 0.009375 0.013672 0.98\n0 1 drone 0.361719 0.326172 0.010937 0.011719 1.0\n0 2 drone 0.356250 0.275391 0.009375 0.011719 0.93\n0 3 drone 0.352344 0.216797 0.010937 0.011719 0.71\n0 4 drone 0.345313 0.153320 0.012500 0.013672 0.92\n1 0 drone 0.339062 0.086914 0.012500 0.013672 0.93\n1 1 drone 0.330469 0.123047 0.010937 0.015625 0.86\n1 2 drone 0.319531 0.241211 0.014063 0.021484 0.83\n1 3 drone 0.307031 0.378906 0.010937 0.019531 0.92\n1 4 drone 0.292969 0.514648 0.014063 0.021484 0.96\n2 0 drone 0.281250 0.541016 0.012500 0.011719 0.98\n2 1 drone 0.268750 0.495117 0.012500 0.013672 0.8\n2 2 drone 0.254688 0.435547 0.012500 0.015625 0.89\n2 3 drone 0.241406 0.360352 0.014063 0.017578 0.73\n2 4 drone 0.342969 0.343750 0.023438 0.015625 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.343750 0.483398 0.012500 0.013672 0.74\n0 1 drone 0.343750 0.480469 0.012500 0.015625 0.86\n0 2 drone 0.343750 0.485352 0.012500 0.013672 0.81\n0 3 drone 0.344531 0.496094 0.014063 0.015625 0.74\n0 4 drone 0.344531 0.513672 0.014063 0.015625 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.410156 0.453125 0.019531 0.019531 0.79\n0 1 drone 0.410156 0.453125 0.019531 0.019531 0.88\n0 2 drone 0.414062 0.453125 0.019531 0.019531 0.9\n0 3 drone 0.417969 0.457031 0.019531 0.019531 0.72\n0 4 drone 0.417969 0.457031 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0750_0790.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0800_0840.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.433594 0.472656 0.019531 0.019531 0.93\n0 1 drone 0.429688 0.468750 0.019531 0.019531 0.75\n0 2 drone 0.429688 0.472656 0.019531 0.019531 0.72\n0 3 drone 0.425781 0.472656 0.019531 0.019531 0.73\n0 4 drone 0.421875 0.460938 0.019531 0.019531 0.88\n1 0 drone 0.425781 0.464844 0.019531 0.019531 0.75\n1 1 drone 0.425781 0.453125 0.019531 0.019531 0.93\n1 2 drone 0.421875 0.457031 0.019531 0.019531 0.94\n1 3 drone 0.410156 0.460938 0.019531 0.019531 0.91\n1 4 drone 0.417969 0.457031 0.019531 0.019531 0.73\n2 0 drone 0.421875 0.460938 0.019531 0.019531 0.91\n2 1 drone 0.421875 0.457031 0.019531 0.019531 0.91\n2 2 drone 0.421875 0.457031 0.019531 0.019531 0.96\n2 3 drone 0.421875 0.460938 0.019531 0.019531 0.93\n2 4 drone 0.421875 0.453125 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.289062 0.449219 0.019531 0.019531 0.93\n0 1 drone 0.304688 0.445312 0.019531 0.019531 0.72\n0 2 drone 0.304688 0.445312 0.019531 0.019531 0.93\n0 3 drone 0.300781 0.445312 0.019531 0.019531 0.79\n0 4 drone 0.316406 0.445312 0.019531 0.019531 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0950_0990.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.414062 0.460938 0.019531 0.019531 0.83\n0 1 drone 0.410156 0.460938 0.019531 0.019531 0.9\n0 2 drone 0.410156 0.460938 0.019531 0.019531 0.84\n0 3 drone 0.410156 0.464844 0.019531 0.019531 0.91\n0 4 drone 0.410156 0.464844 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.761719 0.339844 0.020313 0.023438 0.87\n0 1 drone 0.785156 0.332031 0.023438 0.019531 0.88\n0 2 drone 0.816406 0.327148 0.023438 0.017578 0.8\n0 3 drone 0.817187 0.318359 0.018750 0.011719 0.73\n0 4 drone 0.770312 0.310547 0.021875 0.011719 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.371094 0.492188 0.019531 0.019531 0.82\n0 1 drone 0.359375 0.496094 0.019531 0.019531 0.74\n0 2 drone 0.367188 0.496094 0.019531 0.019531 0.88\n0 3 drone 0.355469 0.496094 0.019531 0.019531 0.74\n0 4 drone 0.363281 0.500000 0.019531 0.019531 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.553125 0.487305 0.012500 0.013672 0.84\n0 1 drone 0.553906 0.487305 0.010937 0.013672 0.81\n0 2 drone 0.551562 0.488281 0.012500 0.015625 0.7\n0 3 drone 0.539062 0.491211 0.012500 0.013672 0.79\n0 4 drone 0.525781 0.490234 0.014063 0.015625 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.441406 0.019531 0.019531 0.99\n0 1 drone 0.250000 0.437500 0.019531 0.019531 0.79\n0 2 drone 0.265625 0.437500 0.019531 0.019531 0.95\n0 3 drone 0.289062 0.441406 0.019531 0.019531 0.92\n0 4 drone 0.265625 0.437500 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.313281 0.205078 0.014063 0.011719 0.92\n0 1 drone 0.310937 0.207031 0.012500 0.011719 0.89\n0 2 drone 0.314844 0.207031 0.010937 0.011719 0.86\n0 3 drone 0.346094 0.211914 0.004687 0.005859 0.73\n0 4 drone 0.381250 0.213867 0.006250 0.005859 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0006_0046.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.553134 0.467352 0.009375 0.009766 0.92\n0 1 drone 0.558724 0.492280 0.007812 0.009766 0.96\n0 2 drone 0.532296 0.495872 0.010937 0.011719 0.81\n0 3 drone 0.529873 0.488088 0.010937 0.011719 0.77\n0 4 drone 0.541148 0.490824 0.012500 0.013672 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.553125 0.485352 0.015625 0.017578 0.95\n0 1 drone 0.550781 0.476562 0.017188 0.015625 0.82\n0 2 drone 0.551562 0.485352 0.015625 0.017578 0.9\n0 3 drone 0.553906 0.484375 0.014063 0.015625 0.78\n0 4 drone 0.551562 0.486328 0.012500 0.015625 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.383594 0.457031 0.023438 0.011719 0.94\n0 1 drone 0.384375 0.432617 0.021875 0.013672 0.95\n0 2 drone 0.384375 0.407227 0.021875 0.013672 0.77\n0 3 drone 0.386719 0.379883 0.023438 0.013672 0.82\n0 4 drone 0.392188 0.359375 0.021875 0.011719 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.707812 0.529297 0.015625 0.015625 0.78\n0 1 drone 0.721094 0.559570 0.014063 0.013672 0.99\n0 2 drone 0.718750 0.441406 0.015625 0.019531 0.81\n0 3 drone 0.724219 0.461914 0.014063 0.013672 0.96\n0 4 drone 0.728906 0.505859 0.010937 0.015625 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.417969 0.348633 0.020313 0.009766 0.77\n0 1 drone 0.416406 0.347656 0.020313 0.007812 0.88\n0 2 drone 0.416406 0.349609 0.020313 0.007812 0.8\n0 3 drone 0.415625 0.348633 0.018750 0.009766 0.74\n0 4 drone 0.415625 0.350586 0.021875 0.009766 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.375000 0.300781 0.117188 0.117188 0.95\n0 1 drone 0.332031 0.265625 0.117188 0.117188 0.75\n0 2 drone 0.367188 0.304688 0.117188 0.117188 0.87\n0 3 drone 0.347656 0.292969 0.117188 0.117188 0.86\n0 4 drone 0.367188 0.281250 0.117188 0.117188 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1350_1390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.348633 0.020313 0.017578 0.75\n0 1 drone 0.394531 0.347656 0.020313 0.011719 0.86\n0 2 drone 0.395313 0.348633 0.021875 0.013672 0.83\n0 3 drone 0.395313 0.348633 0.021875 0.013672 0.8\n0 4 drone 0.396094 0.350586 0.020313 0.013672 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0900_0940.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.544531 0.427734 0.020313 0.015625 0.99\n0 1 drone 0.531250 0.426758 0.015625 0.017578 0.87\n0 2 drone 0.485938 0.315430 0.015625 0.021484 0.82\n0 3 drone 0.438281 0.180664 0.017188 0.021484 0.74\n0 4 drone 0.393750 0.031250 0.018750 0.023438 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.699219 0.597656 0.117188 0.117188 0.93\n0 1 drone 0.640625 0.519531 0.117188 0.117188 0.81\n0 2 drone 0.617188 0.445312 0.117188 0.117188 0.73\n0 3 drone 0.593750 0.371094 0.117188 0.117188 0.92\n0 4 drone 0.562500 0.296875 0.117188 0.117188 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.748437 0.279297 0.021875 0.015625 0.85\n0 1 drone 0.667969 0.279297 0.020313 0.011719 0.8\n0 2 drone 0.636719 0.284180 0.017188 0.017578 0.92\n0 3 drone 0.637500 0.284180 0.021875 0.013672 0.91\n0 4 drone 0.645312 0.281250 0.018750 0.011719 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.605469 0.291016 0.007812 0.007812 0.98\n0 1 drone 0.606250 0.292969 0.009375 0.007812 0.78\n0 2 drone 0.604688 0.292969 0.009375 0.007812 1.0\n0 3 drone 0.604688 0.294922 0.009375 0.007812 0.9\n0 4 drone 0.603125 0.296875 0.009375 0.007812 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.839063 0.166016 0.006250 0.007812 0.87\n0 1 drone 0.833594 0.163086 0.004687 0.005859 0.7\n0 2 drone 0.832031 0.163086 0.004687 0.005859 0.7\n0 3 drone 0.832031 0.161133 0.004687 0.005859 0.87\n0 4 drone 0.833594 0.161133 0.004687 0.005859 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.550000 0.477539 0.012500 0.013672 0.85\n0 1 drone 0.549219 0.481445 0.010937 0.013672 0.79\n0 2 drone 0.552344 0.484375 0.014063 0.015625 0.72\n0 3 drone 0.548438 0.485352 0.012500 0.013672 0.86\n0 4 drone 0.552344 0.488281 0.017188 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.485156 0.348633 0.004687 0.005859 0.93\n0 1 drone 0.464844 0.344727 0.004687 0.005859 0.76\n0 2 drone 0.448437 0.347656 0.006250 0.007812 0.9\n0 3 drone 0.426563 0.344727 0.006250 0.005859 0.76\n0 4 drone 0.393750 0.344727 0.006250 0.005859 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.316406 0.019531 0.019531 0.89\n0 1 drone 0.417969 0.320312 0.019531 0.019531 0.75\n0 2 drone 0.406250 0.343750 0.019531 0.019531 0.76\n0 3 drone 0.402344 0.324219 0.019531 0.019531 0.7\n0 4 drone 0.417969 0.335938 0.019531 0.019531 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1250_1290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.410156 0.348633 0.020313 0.009766 0.77\n0 1 drone 0.409375 0.348633 0.021875 0.009766 0.91\n0 2 drone 0.410156 0.348633 0.020313 0.009766 0.88\n0 3 drone 0.410156 0.346680 0.020313 0.009766 0.94\n0 4 drone 0.410938 0.346680 0.021875 0.009766 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0636_0676.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.406501 0.403754 0.019531 0.019531 0.73\n0 1 drone 0.382282 0.381180 0.019531 0.019531 1.0\n0 2 drone 0.392845 0.388707 0.019531 0.019531 0.83\n0 3 drone 0.396075 0.392436 0.019531 0.019531 0.98\n0 4 drone 0.377627 0.403885 0.019531 0.019531 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.506250 0.533203 0.018750 0.019531 0.98\n0 1 drone 0.476562 0.540039 0.018750 0.021484 0.96\n0 2 drone 0.524219 0.541016 0.020313 0.019531 0.98\n0 3 drone 0.515625 0.537109 0.018750 0.019531 0.88\n0 4 drone 0.482031 0.539062 0.017188 0.019531 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.456250 0.341797 0.012500 0.007812 0.88\n0 1 drone 0.456250 0.358398 0.018750 0.009766 0.92\n0 2 drone 0.457031 0.366211 0.017188 0.009766 0.8\n0 3 drone 0.457031 0.368164 0.017188 0.009766 0.97\n0 4 drone 0.460156 0.370117 0.020313 0.009766 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.455469 0.369141 0.020313 0.011719 0.97\n0 1 drone 0.449219 0.370117 0.020313 0.013672 0.83\n0 2 drone 0.438281 0.370117 0.020313 0.009766 0.88\n0 3 drone 0.423438 0.370117 0.021875 0.009766 0.91\n0 4 drone 0.408594 0.372070 0.020313 0.009766 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0600_0640.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0650_0690.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.621875 0.470703 0.012500 0.015625 0.98\n0 1 drone 0.617969 0.473633 0.010937 0.013672 0.92\n0 2 drone 0.343750 0.473633 0.012500 0.013672 0.75\n0 3 drone 0.345313 0.474609 0.012500 0.011719 0.82\n0 4 drone 0.343750 0.479492 0.012500 0.013672 0.97\n1 0 drone 0.345313 0.477539 0.012500 0.013672 0.8\n1 1 drone 0.345313 0.479492 0.012500 0.013672 0.84\n1 2 drone 0.345313 0.481445 0.012500 0.013672 0.93\n1 3 drone 0.345313 0.481445 0.012500 0.013672 0.93\n1 4 drone 0.345313 0.481445 0.012500 0.013672 0.8\n2 0 drone 0.343750 0.483398 0.012500 0.013672 0.82\n2 1 drone 0.343750 0.480469 0.012500 0.015625 0.95\n2 2 drone 0.343750 0.485352 0.012500 0.013672 0.74\n2 3 drone 0.344531 0.496094 0.014063 0.015625 0.7\n2 4 drone 0.344531 0.513672 0.014063 0.015625 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.500781 0.541016 0.017188 0.019531 0.81\n0 1 drone 0.499219 0.541016 0.017188 0.019531 0.85\n0 2 drone 0.494531 0.538086 0.017188 0.017578 0.89\n0 3 drone 0.497656 0.533203 0.017188 0.015625 0.75\n0 4 drone 0.500781 0.541992 0.017188 0.017578 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.492188 0.536133 0.018750 0.021484 0.94\n0 1 drone 0.524219 0.534180 0.020313 0.021484 0.79\n0 2 drone 0.492188 0.538086 0.018750 0.021484 0.92\n0 3 drone 0.482031 0.539062 0.020313 0.019531 0.91\n0 4 drone 0.493750 0.537109 0.018750 0.019531 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 3 drone 0.676562 0.252930 0.015625 0.009766 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0916_0956.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398932 0.459884 0.019531 0.019531 0.97\n0 1 drone 0.402159 0.460101 0.019531 0.019531 0.87\n0 2 drone 0.423086 0.456206 0.019531 0.019531 0.71\n0 3 drone 0.391992 0.476376 0.019531 0.019531 0.82\n0 4 drone 0.420652 0.441174 0.019531 0.019531 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1350_1390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1400_1440.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.348633 0.020313 0.017578 0.81\n0 1 drone 0.394531 0.347656 0.020313 0.011719 0.86\n0 2 drone 0.395313 0.348633 0.021875 0.013672 0.86\n0 3 drone 0.395313 0.348633 0.021875 0.013672 0.77\n0 4 drone 0.396094 0.350586 0.020313 0.013672 0.87\n1 0 drone 0.397656 0.348633 0.020313 0.013672 0.88\n1 1 drone 0.399219 0.348633 0.020313 0.013672 0.9\n1 2 drone 0.403906 0.347656 0.020313 0.011719 0.76\n1 3 drone 0.407813 0.346680 0.021875 0.013672 0.97\n1 4 drone 0.410156 0.345703 0.023438 0.015625 0.77\n2 0 drone 0.413281 0.346680 0.023438 0.013672 0.79\n2 1 drone 0.414062 0.346680 0.021875 0.013672 0.92\n2 2 drone 0.414062 0.346680 0.021875 0.013672 0.71\n2 3 drone 0.415625 0.344727 0.021875 0.013672 0.93\n2 4 drone 0.418750 0.342773 0.021875 0.013672 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.502344 0.539062 0.017188 0.019531 0.73\n0 1 drone 0.502344 0.540039 0.017188 0.021484 0.78\n0 2 drone 0.494531 0.539062 0.017188 0.023438 0.88\n0 3 drone 0.496875 0.533203 0.018750 0.023438 0.73\n0 4 drone 0.500000 0.539062 0.018750 0.019531 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0321_0361.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.368026 0.365275 0.019531 0.019531 0.75\n0 1 drone 0.399547 0.357213 0.019531 0.019531 0.88\n0 2 drone 0.381112 0.341679 0.019531 0.019531 0.9\n0 3 drone 0.406359 0.365528 0.019531 0.019531 0.71\n0 4 drone 0.395159 0.368858 0.019531 0.019531 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1350_1390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.342969 0.209961 0.004687 0.005859 0.93\n0 1 drone 0.337500 0.211914 0.012500 0.009766 0.82\n0 2 drone 0.346875 0.212891 0.006250 0.007812 0.84\n0 3 drone 0.348438 0.212891 0.006250 0.007812 0.87\n0 4 drone 0.320312 0.211914 0.006250 0.009766 0.7"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0600_0640.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0650_0690.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.603906 0.277344 0.007812 0.007812 0.82\n0 1 drone 0.603906 0.279297 0.007812 0.007812 0.96\n0 2 drone 0.603906 0.281250 0.007812 0.007812 0.71\n0 3 drone 0.603906 0.283203 0.007812 0.007812 0.92\n0 4 drone 0.606250 0.288086 0.006250 0.005859 0.9\n1 0 drone 0.605469 0.291016 0.007812 0.007812 0.75\n1 1 drone 0.606250 0.292969 0.009375 0.007812 0.88\n1 2 drone 0.604688 0.292969 0.009375 0.007812 0.82\n1 3 drone 0.604688 0.294922 0.009375 0.007812 0.96\n1 4 drone 0.603125 0.296875 0.009375 0.007812 0.83\n2 0 drone 0.615625 0.292969 0.009375 0.007812 0.72\n2 1 drone 0.633594 0.288086 0.010937 0.009766 0.71\n2 2 drone 0.658594 0.276367 0.014063 0.009766 0.75\n2 3 drone 0.699219 0.256836 0.020313 0.009766 0.91\n2 4 drone 0.749219 0.232422 0.023438 0.015625 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0450_0490.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0500_0540.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.449219 0.019531 0.019531 0.96\n0 1 drone 0.363281 0.449219 0.019531 0.019531 0.74\n0 2 drone 0.363281 0.449219 0.019531 0.019531 0.7\n0 3 drone 0.371094 0.449219 0.019531 0.019531 0.92\n0 4 drone 0.375000 0.453125 0.019531 0.019531 1.0\n1 0 drone 0.375000 0.453125 0.019531 0.019531 0.98\n1 1 drone 0.390625 0.457031 0.019531 0.019531 0.82\n1 2 drone 0.394531 0.453125 0.019531 0.019531 0.73\n1 3 drone 0.398438 0.453125 0.019531 0.019531 0.95\n1 4 drone 0.402344 0.453125 0.019531 0.019531 0.71\n2 0 drone 0.410156 0.453125 0.019531 0.019531 0.97\n2 1 drone 0.410156 0.453125 0.019531 0.019531 0.83\n2 2 drone 0.414062 0.453125 0.019531 0.019531 0.99\n2 3 drone 0.417969 0.457031 0.019531 0.019531 0.83\n2 4 drone 0.417969 0.457031 0.019531 0.019531 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.787500 0.304688 0.021875 0.019531 0.79\n0 1 drone 0.734375 0.299805 0.018750 0.017578 0.88\n0 2 drone 0.710938 0.290039 0.021875 0.013672 0.81\n0 3 drone 0.715625 0.285156 0.021875 0.011719 0.97\n0 4 drone 0.732031 0.279297 0.017188 0.011719 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.406250 0.515625 0.021875 0.011719 0.9\n0 1 drone 0.405469 0.513672 0.023438 0.011719 0.79\n0 2 drone 0.405469 0.513672 0.023438 0.011719 0.8\n0 3 drone 0.403906 0.512695 0.023438 0.009766 0.96\n0 4 drone 0.405469 0.512695 0.023438 0.009766 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.546875 0.478516 0.012500 0.015625 0.86\n0 1 drone 0.546094 0.476562 0.010937 0.015625 0.71\n0 2 drone 0.573438 0.475586 0.012500 0.013672 0.98\n0 3 drone 0.617969 0.473633 0.010937 0.013672 0.88\n0 4 drone 0.651563 0.469727 0.012500 0.013672 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355469 0.449219 0.019531 0.019531 0.91\n0 1 drone 0.363281 0.449219 0.019531 0.019531 0.91\n0 2 drone 0.363281 0.449219 0.019531 0.019531 0.78\n0 3 drone 0.371094 0.449219 0.019531 0.019531 0.88\n0 4 drone 0.375000 0.453125 0.019531 0.019531 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0450_0490.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0500_0540.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.473438 0.452148 0.006250 0.005859 0.98\n0 1 drone 0.471094 0.440430 0.004687 0.005859 0.96\n0 2 drone 0.469531 0.428711 0.004687 0.005859 0.79\n0 3 drone 0.467969 0.413086 0.004687 0.005859 0.74\n0 4 drone 0.465625 0.395508 0.006250 0.005859 0.82\n1 0 drone 0.464844 0.375000 0.007812 0.007812 0.98\n1 1 drone 0.462500 0.354492 0.006250 0.009766 0.93\n1 2 drone 0.460938 0.332031 0.006250 0.007812 0.9\n1 3 drone 0.457813 0.307617 0.006250 0.009766 0.96\n1 4 drone 0.455469 0.280273 0.007812 0.009766 0.71\n2 0 drone 0.453906 0.252930 0.007812 0.009766 0.86\n2 1 drone 0.451562 0.221680 0.006250 0.009766 0.78\n2 2 drone 0.449219 0.190430 0.007812 0.009766 0.88\n2 3 drone 0.446094 0.157227 0.007812 0.009766 0.94\n2 4 drone 0.442969 0.124023 0.007812 0.009766 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.500000 0.019531 0.019531 0.96\n0 1 drone 0.367188 0.507812 0.019531 0.019531 0.75\n0 2 drone 0.371094 0.503906 0.019531 0.019531 0.96\n0 3 drone 0.363281 0.496094 0.019531 0.019531 0.95\n0 4 drone 0.359375 0.500000 0.019531 0.019531 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.382812 0.019531 0.019531 0.85\n0 1 drone 0.378906 0.378906 0.019531 0.019531 0.87\n0 2 drone 0.414062 0.382812 0.019531 0.019531 0.95\n0 3 drone 0.394531 0.390625 0.019531 0.019531 0.92\n0 4 drone 0.382812 0.394531 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.729688 0.349609 0.012500 0.011719 0.98\n0 1 drone 0.729688 0.349609 0.012500 0.011719 0.99\n0 2 drone 0.724219 0.346680 0.010937 0.009766 0.74\n0 3 drone 0.716406 0.346680 0.010937 0.009766 0.98\n0 4 drone 0.707812 0.345703 0.006250 0.007812 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0750_0790.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0800_0840.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.441406 0.378906 0.019531 0.019531 0.73\n0 1 drone 0.417969 0.394531 0.019531 0.019531 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.498437 0.291016 0.006250 0.003906 0.91\n0 1 drone 0.593750 0.285156 0.009375 0.003906 0.9\n0 2 drone 0.607812 0.284180 0.006250 0.005859 0.99\n0 3 drone 0.607031 0.282227 0.007812 0.005859 0.77\n0 4 drone 0.607812 0.282227 0.009375 0.005859 0.71"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.621094 0.355469 0.010937 0.007812 0.83\n0 1 drone 0.596875 0.356445 0.006250 0.005859 0.92\n0 2 drone 0.566406 0.354492 0.007812 0.005859 0.91\n0 3 drone 0.539062 0.352539 0.006250 0.005859 0.85\n0 4 drone 0.515625 0.349609 0.006250 0.003906 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0780_0820.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0600_0640.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0650_0690.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.496875 0.536133 0.015625 0.017578 0.85\n0 1 drone 0.496875 0.539062 0.015625 0.019531 1.0\n0 2 drone 0.496875 0.541992 0.015625 0.017578 0.8\n0 3 drone 0.496875 0.535156 0.015625 0.015625 0.97\n0 4 drone 0.497656 0.536133 0.014063 0.017578 0.79\n1 0 drone 0.496875 0.540039 0.015625 0.017578 0.74\n1 1 drone 0.496875 0.541992 0.015625 0.017578 0.98\n1 2 drone 0.496875 0.536133 0.015625 0.017578 0.92\n1 3 drone 0.495312 0.534180 0.015625 0.017578 0.71\n1 4 drone 0.496094 0.535156 0.014063 0.015625 0.9\n2 0 drone 0.496094 0.537109 0.014063 0.015625 0.82\n2 1 drone 0.496094 0.540039 0.014063 0.017578 0.92\n2 2 drone 0.496875 0.539062 0.012500 0.015625 0.85\n2 3 drone 0.496875 0.539062 0.015625 0.019531 0.81\n2 4 drone 0.497656 0.538086 0.014063 0.017578 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.402344 0.019531 0.019531 0.81\n0 1 drone 0.386719 0.394531 0.019531 0.019531 0.75\n0 2 drone 0.386719 0.394531 0.019531 0.019531 0.74\n0 3 drone 0.394531 0.398438 0.019531 0.019531 0.99\n0 4 drone 0.390625 0.394531 0.019531 0.019531 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0900_0940.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.391406 0.369141 0.020313 0.007812 0.81\n0 1 drone 0.373437 0.364258 0.021875 0.009766 0.79\n0 2 drone 0.357812 0.360352 0.018750 0.009766 0.84\n0 3 drone 0.340625 0.357422 0.021875 0.007812 0.78\n0 4 drone 0.321094 0.354492 0.020313 0.009766 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1200_1240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.414062 0.351562 0.021875 0.011719 0.71\n0 1 drone 0.412500 0.351562 0.021875 0.011719 0.79\n0 2 drone 0.410938 0.349609 0.021875 0.007812 0.72\n0 3 drone 0.410156 0.350586 0.020313 0.009766 0.95\n0 4 drone 0.410938 0.350586 0.018750 0.009766 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.473438 0.452148 0.006250 0.005859 0.89\n0 1 drone 0.471094 0.440430 0.004687 0.005859 0.75\n0 2 drone 0.469531 0.428711 0.004687 0.005859 0.72\n0 3 drone 0.467969 0.413086 0.004687 0.005859 0.71\n0 4 drone 0.465625 0.395508 0.006250 0.005859 0.71"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.470313 0.289062 0.021875 0.015625 0.92\n0 1 drone 0.470313 0.281250 0.021875 0.015625 0.85\n0 2 drone 0.467969 0.276367 0.023438 0.013672 0.86\n0 3 drone 0.460156 0.278320 0.020313 0.009766 0.98\n0 4 drone 0.451562 0.286133 0.021875 0.009766 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0367_0407.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.355506 0.512460 0.019531 0.019531 0.99\n0 1 drone 0.371893 0.515131 0.019531 0.019531 0.71\n0 2 drone 0.363062 0.495463 0.019531 0.019531 0.83\n0 3 drone 0.347287 0.517394 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0803_0843.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.479165 0.545719 0.015625 0.017578 0.74\n0 1 drone 0.496086 0.562622 0.014063 0.019531 0.82\n0 2 drone 0.515838 0.525076 0.014063 0.021484 0.8\n0 3 drone 0.498472 0.518083 0.015625 0.019531 0.85\n0 4 drone 0.493913 0.523885 0.017188 0.019531 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1250_1290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.687500 0.227539 0.015625 0.009766 0.83\n0 1 drone 0.687500 0.227539 0.015625 0.009766 0.8\n0 2 drone 0.686719 0.223633 0.014063 0.009766 0.85\n0 3 drone 0.682813 0.214844 0.012500 0.011719 0.87\n0 4 drone 0.660156 0.209961 0.014063 0.013672 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.496094 0.538086 0.014063 0.017578 0.73\n0 1 drone 0.497656 0.541016 0.014063 0.015625 0.76\n0 2 drone 0.502344 0.540039 0.014063 0.017578 0.92\n0 3 drone 0.503906 0.536133 0.014063 0.017578 0.72\n0 4 drone 0.500000 0.537109 0.015625 0.015625 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.355469 0.019531 0.019531 0.81\n0 1 drone 0.386719 0.363281 0.019531 0.019531 0.87\n0 2 drone 0.386719 0.359375 0.019531 0.019531 0.92\n0 3 drone 0.390625 0.359375 0.019531 0.019531 0.86\n0 4 drone 0.386719 0.355469 0.019531 0.019531 0.78\n1 0 drone 0.394531 0.359375 0.019531 0.019531 0.91\n1 1 drone 0.398438 0.367188 0.019531 0.019531 0.82\n1 2 drone 0.398438 0.359375 0.019531 0.019531 0.9\n1 3 drone 0.398438 0.355469 0.019531 0.019531 0.91\n1 4 drone 0.398438 0.371094 0.019531 0.019531 0.83\n2 0 drone 0.394531 0.371094 0.019531 0.019531 0.93\n2 1 drone 0.386719 0.367188 0.019531 0.019531 0.84\n2 2 drone 0.386719 0.375000 0.019531 0.019531 0.96\n2 3 drone 0.382812 0.371094 0.019531 0.019531 0.82\n2 4 drone 0.378906 0.363281 0.019531 0.019531 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0900_0940.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0950_0990.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.544531 0.427734 0.020313 0.015625 0.83\n0 1 drone 0.531250 0.426758 0.015625 0.017578 0.99\n0 2 drone 0.485938 0.315430 0.015625 0.021484 0.89\n0 3 drone 0.438281 0.180664 0.017188 0.021484 0.99\n0 4 drone 0.393750 0.031250 0.018750 0.023438 0.98\n1 2 drone 0.256250 0.006836 0.021875 0.013672 0.94\n1 3 drone 0.210938 0.064453 0.021875 0.031250 0.93\n1 4 drone 0.167969 0.122070 0.020313 0.025391 0.99\n2 0 drone 0.125781 0.150391 0.020313 0.023438 0.93\n2 1 drone 0.089063 0.148438 0.018750 0.023438 0.75\n2 2 drone 0.055469 0.115234 0.017188 0.023438 0.84\n2 3 drone 0.032031 0.086914 0.017188 0.025391 0.92\n2 4 drone 0.016406 0.075195 0.017188 0.025391 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.382812 0.019531 0.019531 0.72\n0 1 drone 0.394531 0.386719 0.019531 0.019531 0.7\n0 2 drone 0.390625 0.382812 0.019531 0.019531 0.72\n0 3 drone 0.386719 0.375000 0.019531 0.019531 0.8\n0 4 drone 0.390625 0.390625 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0450_0490.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0500_0540.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.611719 0.481445 0.010937 0.013672 0.72\n0 1 drone 0.609375 0.481445 0.012500 0.013672 0.86\n0 2 drone 0.606250 0.478516 0.012500 0.015625 0.77\n0 3 drone 0.601562 0.478516 0.012500 0.015625 0.93\n0 4 drone 0.582031 0.473633 0.010937 0.013672 0.75\n1 0 drone 0.566406 0.473633 0.010937 0.013672 0.77\n1 1 drone 0.545312 0.473633 0.012500 0.013672 0.87\n1 2 drone 0.535156 0.475586 0.010937 0.013672 0.72\n1 3 drone 0.531250 0.475586 0.012500 0.013672 0.94\n1 4 drone 0.531250 0.474609 0.009375 0.011719 0.94\n2 0 drone 0.546875 0.478516 0.012500 0.015625 0.71\n2 1 drone 0.546094 0.476562 0.010937 0.015625 0.89\n2 2 drone 0.573438 0.475586 0.012500 0.013672 0.83\n2 3 drone 0.617969 0.473633 0.010937 0.013672 0.81\n2 4 drone 0.651563 0.469727 0.012500 0.013672 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.345313 0.477539 0.012500 0.013672 0.76\n0 1 drone 0.345313 0.479492 0.012500 0.013672 0.93\n0 2 drone 0.345313 0.481445 0.012500 0.013672 0.96\n0 3 drone 0.345313 0.481445 0.012500 0.013672 0.96\n0 4 drone 0.345313 0.481445 0.012500 0.013672 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1078_1118.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.400154 0.450084 0.019531 0.019531 0.7\n0 1 drone 0.391981 0.456840 0.019531 0.019531 0.94\n0 2 drone 0.399099 0.467994 0.019531 0.019531 0.74\n0 3 drone 0.389206 0.455150 0.019531 0.019531 0.72\n0 4 drone 0.389748 0.467707 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.662500 0.193359 0.015625 0.011719 0.75\n0 1 drone 0.660937 0.201172 0.015625 0.011719 0.8\n0 2 drone 0.660937 0.201172 0.015625 0.011719 0.87\n0 3 drone 0.660156 0.201172 0.017188 0.011719 0.94\n0 4 drone 0.660937 0.202148 0.015625 0.009766 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0600_0640.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0650_0690.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.383594 0.457031 0.023438 0.011719 0.9\n0 1 drone 0.384375 0.432617 0.021875 0.013672 0.88\n0 2 drone 0.384375 0.407227 0.021875 0.013672 0.98\n0 3 drone 0.386719 0.379883 0.023438 0.013672 0.84\n0 4 drone 0.392188 0.359375 0.021875 0.011719 0.75\n1 0 drone 0.403125 0.347656 0.021875 0.011719 0.81\n1 1 drone 0.417187 0.336914 0.021875 0.009766 0.93\n1 2 drone 0.435937 0.323242 0.018750 0.009766 0.95\n1 3 drone 0.450781 0.311523 0.023438 0.009766 0.77\n1 4 drone 0.463281 0.298828 0.023438 0.011719 0.74\n2 0 drone 0.470313 0.289062 0.021875 0.015625 0.86\n2 1 drone 0.470313 0.281250 0.021875 0.015625 0.85\n2 2 drone 0.467969 0.276367 0.023438 0.013672 0.76\n2 3 drone 0.460156 0.278320 0.020313 0.009766 0.92\n2 4 drone 0.451562 0.286133 0.021875 0.009766 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.8\n0 1 drone 0.421875 0.457031 0.019531 0.019531 0.88\n0 2 drone 0.421875 0.457031 0.019531 0.019531 0.73\n0 3 drone 0.421875 0.460938 0.019531 0.019531 0.8\n0 4 drone 0.421875 0.453125 0.019531 0.019531 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.411719 0.384766 0.023438 0.011719 0.9\n0 1 drone 0.411719 0.404297 0.023438 0.011719 0.74\n0 2 drone 0.410156 0.429688 0.023438 0.011719 0.76\n0 3 drone 0.410156 0.451172 0.023438 0.011719 0.83\n0 4 drone 0.411719 0.466797 0.023438 0.011719 0.86\n1 0 drone 0.410938 0.475586 0.021875 0.009766 0.82\n1 1 drone 0.410938 0.479492 0.021875 0.009766 0.81\n1 2 drone 0.411719 0.486328 0.023438 0.011719 0.98\n1 3 drone 0.409375 0.490234 0.021875 0.011719 0.96\n1 4 drone 0.406250 0.493164 0.021875 0.009766 0.9\n2 0 drone 0.404687 0.499023 0.021875 0.009766 0.75\n2 1 drone 0.403125 0.502930 0.021875 0.009766 0.74\n2 2 drone 0.403125 0.507812 0.021875 0.011719 0.71\n2 3 drone 0.403125 0.510742 0.021875 0.009766 0.96\n2 4 drone 0.403906 0.513672 0.023438 0.011719 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0245_0285.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.389653 0.357305 0.019531 0.019531 0.76\n0 1 drone 0.400517 0.407465 0.019531 0.019531 0.76\n0 2 drone 0.384906 0.397434 0.019531 0.019531 1.0\n0 3 drone 0.370373 0.386942 0.019531 0.019531 0.84\n0 4 drone 0.391661 0.386474 0.019531 0.019531 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.497656 0.536133 0.017188 0.017578 0.84\n0 1 drone 0.497656 0.537109 0.017188 0.019531 0.85\n0 2 drone 0.499219 0.537109 0.017188 0.019531 0.7\n0 3 drone 0.500781 0.537109 0.014063 0.019531 0.71\n0 4 drone 0.499219 0.538086 0.014063 0.017578 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.226562 0.429688 0.019531 0.019531 0.91\n0 1 drone 0.242188 0.425781 0.019531 0.019531 0.95\n0 2 drone 0.273438 0.429688 0.019531 0.019531 0.75\n0 3 drone 0.261719 0.437500 0.019531 0.019531 0.81\n0 4 drone 0.253906 0.437500 0.019531 0.019531 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.390625 0.019531 0.019531 0.97\n0 1 drone 0.386719 0.394531 0.019531 0.019531 0.72\n0 2 drone 0.386719 0.394531 0.019531 0.019531 0.73\n0 3 drone 0.453125 0.375000 0.019531 0.019531 0.99\n0 4 drone 0.441406 0.390625 0.019531 0.019531 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.347656 0.500000 0.019531 0.019531 0.89\n0 1 drone 0.347656 0.500000 0.019531 0.019531 0.9\n0 2 drone 0.351562 0.507812 0.019531 0.019531 0.7\n0 3 drone 0.371094 0.503906 0.019531 0.019531 0.89\n0 4 drone 0.355469 0.503906 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.603906 0.277344 0.007812 0.007812 0.98\n0 1 drone 0.603906 0.279297 0.007812 0.007812 0.94\n0 2 drone 0.603906 0.281250 0.007812 0.007812 0.77\n0 3 drone 0.603906 0.283203 0.007812 0.007812 0.83\n0 4 drone 0.606250 0.288086 0.006250 0.005859 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.335938 0.449219 0.019531 0.019531 0.71\n0 1 drone 0.347656 0.445312 0.019531 0.019531 0.75\n0 2 drone 0.339844 0.445312 0.019531 0.019531 0.73\n0 3 drone 0.347656 0.445312 0.019531 0.019531 0.91\n0 4 drone 0.355469 0.449219 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0814_0854.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.315508 0.187607 0.012500 0.021484 0.81\n0 1 drone 0.325810 0.289196 0.012500 0.019531 0.88\n0 2 drone 0.320363 0.450672 0.012500 0.019531 0.84\n0 3 drone 0.299813 0.534275 0.014063 0.019531 0.81\n0 4 drone 0.259843 0.535362 0.010937 0.013672 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.363281 0.019531 0.019531 0.74\n0 1 drone 0.398438 0.363281 0.019531 0.019531 0.85\n0 2 drone 0.394531 0.359375 0.019531 0.019531 0.88\n0 3 drone 0.394531 0.367188 0.019531 0.019531 0.72\n0 4 drone 0.394531 0.355469 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.367188 0.019531 0.019531 0.77\n0 1 drone 0.390625 0.359375 0.019531 0.019531 0.99\n0 2 drone 0.382812 0.359375 0.019531 0.019531 0.87\n0 3 drone 0.382812 0.371094 0.019531 0.019531 0.92\n0 4 drone 0.398438 0.359375 0.019531 0.019531 0.73\n1 0 drone 0.398438 0.363281 0.019531 0.019531 0.86\n1 1 drone 0.398438 0.363281 0.019531 0.019531 0.94\n1 2 drone 0.394531 0.359375 0.019531 0.019531 0.88\n1 3 drone 0.394531 0.367188 0.019531 0.019531 0.77\n1 4 drone 0.394531 0.355469 0.019531 0.019531 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.433594 0.472656 0.019531 0.019531 0.95\n0 1 drone 0.425781 0.472656 0.019531 0.019531 0.85\n0 2 drone 0.421875 0.472656 0.019531 0.019531 0.72\n0 3 drone 0.421875 0.468750 0.019531 0.019531 0.76\n0 4 drone 0.429688 0.472656 0.019531 0.019531 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.375000 0.484375 0.019531 0.019531 0.79\n0 1 drone 0.085938 0.644531 0.019531 0.019531 0.97\n0 2 drone 0.382812 0.480469 0.019531 0.019531 0.78\n0 3 drone 0.378906 0.488281 0.019531 0.019531 0.9\n0 4 drone 0.382812 0.496094 0.019531 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1400_1440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.448437 0.129883 0.025000 0.033203 0.72\n0 1 drone 0.453125 0.128906 0.021875 0.031250 0.8\n0 2 drone 0.460156 0.123047 0.023438 0.031250 0.96\n0 3 drone 0.464062 0.112305 0.025000 0.033203 0.88\n0 4 drone 0.470313 0.105469 0.021875 0.031250 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0600_0640.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0650_0690.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.402344 0.019531 0.019531 0.99\n0 1 drone 0.386719 0.394531 0.019531 0.019531 0.93\n0 2 drone 0.386719 0.394531 0.019531 0.019531 0.93\n0 3 drone 0.394531 0.398438 0.019531 0.019531 0.77\n0 4 drone 0.390625 0.394531 0.019531 0.019531 0.86\n1 0 drone 0.386719 0.398438 0.019531 0.019531 0.9\n1 1 drone 0.390625 0.394531 0.019531 0.019531 0.83\n1 2 drone 0.390625 0.394531 0.019531 0.019531 0.72\n1 3 drone 0.386719 0.398438 0.019531 0.019531 0.91\n1 4 drone 0.386719 0.390625 0.019531 0.019531 0.92\n2 0 drone 0.386719 0.394531 0.019531 0.019531 0.83\n2 1 drone 0.386719 0.394531 0.019531 0.019531 0.79\n2 2 drone 0.394531 0.390625 0.019531 0.019531 0.72\n2 3 drone 0.386719 0.394531 0.019531 0.019531 0.76\n2 4 drone 0.386719 0.390625 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_1290_1330.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.653125 0.274414 0.018750 0.013672 0.71\n0 1 drone 0.654687 0.274414 0.018750 0.013672 0.72\n0 2 drone 0.652344 0.282227 0.017188 0.009766 0.91\n0 3 drone 0.650781 0.290039 0.017188 0.017578 0.87\n0 4 drone 0.654687 0.302734 0.018750 0.015625 0.94\n1 0 drone 0.662500 0.317383 0.018750 0.017578 0.8\n1 1 drone 0.680469 0.329102 0.017188 0.013672 0.92\n1 2 drone 0.697656 0.335938 0.017188 0.011719 0.82\n1 3 drone 0.714844 0.346680 0.014063 0.009766 0.7\n1 4 drone 0.724219 0.349609 0.014063 0.011719 0.74\n2 0 drone 0.729688 0.349609 0.012500 0.011719 0.77\n2 1 drone 0.729688 0.349609 0.012500 0.011719 0.82\n2 2 drone 0.724219 0.346680 0.010937 0.009766 0.81\n2 3 drone 0.716406 0.346680 0.010937 0.009766 0.91\n2 4 drone 0.707812 0.345703 0.006250 0.007812 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.382812 0.378906 0.019531 0.019531 0.76\n0 1 drone 0.382812 0.367188 0.019531 0.019531 0.81\n0 2 drone 0.394531 0.371094 0.019531 0.019531 0.95\n0 3 drone 0.406250 0.367188 0.019531 0.019531 0.71\n0 4 drone 0.386719 0.367188 0.019531 0.019531 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.378906 0.390625 0.019531 0.019531 0.96\n0 1 drone 0.378906 0.386719 0.019531 0.019531 0.88\n0 2 drone 0.375000 0.378906 0.019531 0.019531 0.95\n0 3 drone 0.378906 0.382812 0.019531 0.019531 0.75\n0 4 drone 0.382812 0.390625 0.019531 0.019531 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.312500 0.117188 0.117188 0.91\n0 1 drone 0.351562 0.281250 0.117188 0.117188 0.98\n0 2 drone 0.375000 0.289062 0.117188 0.117188 0.73\n0 3 drone 0.355469 0.300781 0.117188 0.117188 0.88\n0 4 drone 0.343750 0.292969 0.117188 0.117188 0.8\n1 0 drone 0.375000 0.300781 0.117188 0.117188 0.75\n1 1 drone 0.339844 0.273438 0.117188 0.117188 0.8\n1 2 drone 0.390625 0.320312 0.117188 0.117188 0.85\n1 3 drone 0.406250 0.308594 0.117188 0.117188 0.72\n1 4 drone 0.382812 0.281250 0.117188 0.117188 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.382812 0.468750 0.019531 0.019531 0.78\n0 1 drone 0.382812 0.468750 0.019531 0.019531 0.93\n0 2 drone 0.375000 0.464844 0.019531 0.019531 0.99\n0 3 drone 0.382812 0.468750 0.019531 0.019531 0.92\n0 4 drone 0.378906 0.472656 0.019531 0.019531 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0697_0737.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.470660 0.273003 0.021875 0.013672 0.8\n0 1 drone 0.454057 0.289107 0.021875 0.015625 0.79\n0 2 drone 0.468036 0.280570 0.021875 0.015625 0.77\n0 3 drone 0.475382 0.279087 0.021875 0.011719 0.92\n0 4 drone 0.471098 0.282928 0.020313 0.009766 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.382812 0.378906 0.019531 0.019531 0.92\n0 1 drone 0.382812 0.367188 0.019531 0.019531 0.73\n0 2 drone 0.394531 0.371094 0.019531 0.019531 0.84\n0 3 drone 0.406250 0.367188 0.019531 0.019531 0.86\n0 4 drone 0.386719 0.367188 0.019531 0.019531 0.84\n1 0 drone 0.386719 0.367188 0.019531 0.019531 0.75\n1 1 drone 0.378906 0.367188 0.019531 0.019531 0.78\n1 2 drone 0.386719 0.382812 0.019531 0.019531 0.97\n1 3 drone 0.386719 0.371094 0.019531 0.019531 0.97\n1 4 drone 0.382812 0.375000 0.019531 0.019531 0.84\n2 0 drone 0.386719 0.382812 0.019531 0.019531 0.95\n2 1 drone 0.394531 0.386719 0.019531 0.019531 0.78\n2 2 drone 0.390625 0.382812 0.019531 0.019531 0.98\n2 3 drone 0.386719 0.375000 0.019531 0.019531 1.0\n2 4 drone 0.390625 0.390625 0.019531 0.019531 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1053_1093.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.643078 0.182663 0.015625 0.009766 0.81\n0 1 drone 0.645481 0.196998 0.015625 0.011719 0.89\n0 2 drone 0.645728 0.217222 0.017188 0.011719 0.78\n0 3 drone 0.669341 0.194113 0.015625 0.011719 0.73\n0 4 drone 0.671306 0.214010 0.014063 0.009766 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0840_0880.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0900_0940.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0950_0990.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.391406 0.369141 0.020313 0.007812 0.83\n0 1 drone 0.373437 0.364258 0.021875 0.009766 0.81\n0 2 drone 0.357812 0.360352 0.018750 0.009766 0.9\n0 3 drone 0.340625 0.357422 0.021875 0.007812 0.88\n0 4 drone 0.321094 0.354492 0.020313 0.009766 0.82\n1 0 drone 0.303906 0.350586 0.017188 0.009766 0.92\n1 1 drone 0.289844 0.347656 0.017188 0.007812 0.76\n1 2 drone 0.276562 0.345703 0.015625 0.007812 0.77\n1 3 drone 0.269531 0.344727 0.010937 0.005859 0.97\n1 4 drone 0.261719 0.341797 0.020313 0.011719 0.85\n2 0 drone 0.262500 0.340820 0.018750 0.009766 0.98\n2 1 drone 0.264844 0.342773 0.017188 0.009766 0.9\n2 2 drone 0.279687 0.338867 0.021875 0.009766 0.83\n2 3 drone 0.294531 0.336914 0.020313 0.009766 0.75\n2 4 drone 0.314063 0.335938 0.021875 0.011719 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1141_1181.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.516076 0.539969 0.015625 0.015625 0.73\n0 1 drone 0.448631 0.530419 0.014063 0.017578 0.76\n0 2 drone 0.470480 0.550107 0.015625 0.017578 0.95\n0 3 drone 0.521330 0.541072 0.017188 0.019531 0.81\n0 4 drone 0.521454 0.533074 0.017188 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.453906 0.252930 0.007812 0.009766 0.78\n0 1 drone 0.451562 0.221680 0.006250 0.009766 0.87\n0 2 drone 0.449219 0.190430 0.007812 0.009766 0.81\n0 3 drone 0.446094 0.157227 0.007812 0.009766 0.82\n0 4 drone 0.442969 0.124023 0.007812 0.009766 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0656_0696.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.411484 0.334825 0.020313 0.011719 0.93\n0 1 drone 0.445154 0.316643 0.023438 0.009766 0.89\n0 2 drone 0.436125 0.319814 0.023438 0.009766 0.98\n0 3 drone 0.444141 0.324255 0.023438 0.011719 0.92\n0 4 drone 0.485067 0.296996 0.021875 0.013672 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.739844 0.063477 0.004687 0.005859 0.71\n0 1 drone 0.728906 0.053711 0.004687 0.005859 0.84\n0 2 drone 0.719531 0.041992 0.004687 0.005859 0.89\n0 3 drone 0.707031 0.028320 0.004687 0.005859 0.73\n0 4 drone 0.695312 0.012695 0.006250 0.005859 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1050_1090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1100_1140.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.008594 0.071289 0.017188 0.025391 0.98\n0 1 drone 0.005469 0.071289 0.010937 0.025391 0.86\n0 2 drone 0.003906 0.070312 0.007812 0.023438 0.73\n0 3 drone 0.002344 0.072266 0.004687 0.023438 0.96\n0 4 drone 0.003906 0.074219 0.007812 0.023438 0.72\n1 0 drone 0.158594 0.106445 0.020313 0.025391 0.89\n1 1 drone 0.234375 0.116211 0.018750 0.025391 0.95\n1 2 drone 0.235156 0.114258 0.020313 0.025391 0.79\n1 3 drone 0.368750 0.127930 0.021875 0.025391 0.73\n1 4 drone 0.399219 0.125977 0.020313 0.029297 0.97\n2 0 drone 0.430469 0.117188 0.020313 0.027344 0.84\n2 1 drone 0.450781 0.108398 0.020313 0.029297 0.9\n2 2 drone 0.457813 0.107422 0.018750 0.027344 0.8\n2 3 drone 0.460156 0.124023 0.020313 0.029297 0.97\n2 4 drone 0.456250 0.130859 0.021875 0.027344 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.289062 0.449219 0.019531 0.019531 0.76\n0 1 drone 0.304688 0.445312 0.019531 0.019531 0.75\n0 2 drone 0.304688 0.445312 0.019531 0.019531 0.96\n0 3 drone 0.300781 0.445312 0.019531 0.019531 0.97\n0 4 drone 0.316406 0.445312 0.019531 0.019531 0.99\n1 0 drone 0.304688 0.453125 0.019531 0.019531 0.89\n1 1 drone 0.339844 0.453125 0.019531 0.019531 0.96\n1 2 drone 0.328125 0.453125 0.019531 0.019531 0.84\n1 3 drone 0.328125 0.449219 0.019531 0.019531 0.72\n1 4 drone 0.347656 0.449219 0.019531 0.019531 0.79\n2 0 drone 0.335938 0.449219 0.019531 0.019531 1.0\n2 1 drone 0.347656 0.445312 0.019531 0.019531 0.83\n2 2 drone 0.339844 0.445312 0.019531 0.019531 0.79\n2 3 drone 0.347656 0.445312 0.019531 0.019531 0.89\n2 4 drone 0.355469 0.449219 0.019531 0.019531 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0943_0983.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.687546 0.215928 0.015625 0.011719 0.99\n0 1 drone 0.675000 0.225648 0.015625 0.011719 0.99\n0 2 drone 0.678761 0.199233 0.015625 0.011719 0.91\n0 3 drone 0.687756 0.192741 0.015625 0.011719 0.78\n0 4 drone 0.684057 0.205057 0.015625 0.009766 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.281250 0.541016 0.012500 0.011719 0.99\n0 1 drone 0.268750 0.495117 0.012500 0.013672 0.78\n0 2 drone 0.254688 0.435547 0.012500 0.015625 0.77\n0 3 drone 0.241406 0.360352 0.014063 0.017578 0.76\n0 4 drone 0.342969 0.343750 0.023438 0.015625 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1350_1390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1400_1440.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.464844 0.019531 0.019531 0.89\n0 1 drone 0.378906 0.464844 0.019531 0.019531 0.94\n0 2 drone 0.382812 0.464844 0.019531 0.019531 0.8\n0 3 drone 0.386719 0.460938 0.019531 0.019531 0.71\n0 4 drone 0.382812 0.464844 0.019531 0.019531 0.75\n1 0 drone 0.378906 0.464844 0.019531 0.019531 0.96\n1 1 drone 0.386719 0.464844 0.019531 0.019531 0.73\n1 2 drone 0.386719 0.464844 0.019531 0.019531 0.99\n1 3 drone 0.386719 0.464844 0.019531 0.019531 0.78\n1 4 drone 0.382812 0.464844 0.019531 0.019531 0.97\n2 0 drone 0.382812 0.468750 0.019531 0.019531 0.76\n2 1 drone 0.382812 0.468750 0.019531 0.019531 0.9\n2 2 drone 0.375000 0.464844 0.019531 0.019531 0.82\n2 3 drone 0.382812 0.468750 0.019531 0.019531 0.79\n2 4 drone 0.378906 0.472656 0.019531 0.019531 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0950_0990.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 2 drone 0.256250 0.006836 0.021875 0.013672 0.87\n0 3 drone 0.210938 0.064453 0.021875 0.031250 0.77\n0 4 drone 0.167969 0.122070 0.020313 0.025391 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.681250 0.251953 0.015625 0.015625 0.7\n0 1 drone 0.685156 0.242188 0.017188 0.011719 0.86\n0 2 drone 0.686719 0.236328 0.017188 0.011719 0.95\n0 3 drone 0.689844 0.227539 0.014063 0.009766 0.85\n0 4 drone 0.689844 0.226562 0.014063 0.011719 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1400_1440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.378906 0.464844 0.019531 0.019531 0.96\n0 1 drone 0.386719 0.464844 0.019531 0.019531 0.89\n0 2 drone 0.386719 0.464844 0.019531 0.019531 0.9\n0 3 drone 0.386719 0.464844 0.019531 0.019531 1.0\n0 4 drone 0.382812 0.464844 0.019531 0.019531 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0600_0640.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0650_0690.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.441406 0.085938 0.007812 0.007812 0.82\n0 1 drone 0.437500 0.046875 0.009375 0.007812 0.86\n0 2 drone 0.434375 0.005859 0.009375 0.011719 0.8\n1 0 drone 0.417187 0.132812 0.009375 0.015625 0.97\n1 1 drone 0.412500 0.309570 0.009375 0.013672 0.73\n1 2 drone 0.404687 0.389648 0.009375 0.013672 0.89\n1 3 drone 0.399219 0.385742 0.010937 0.013672 0.74\n1 4 drone 0.393750 0.400391 0.009375 0.011719 0.78\n2 0 drone 0.389844 0.499023 0.007812 0.009766 0.87\n2 1 drone 0.385937 0.495117 0.009375 0.009766 0.87\n2 2 drone 0.379688 0.475586 0.009375 0.009766 0.93\n2 3 drone 0.376563 0.446289 0.009375 0.009766 0.75\n2 4 drone 0.371094 0.412109 0.010937 0.011719 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0900_0940.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0950_0990.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.86\n0 1 drone 0.414062 0.460938 0.019531 0.019531 0.79\n0 2 drone 0.410156 0.457031 0.019531 0.019531 0.86\n0 3 drone 0.410156 0.460938 0.019531 0.019531 0.95\n0 4 drone 0.410156 0.460938 0.019531 0.019531 0.92\n1 0 drone 0.414062 0.460938 0.019531 0.019531 0.88\n1 1 drone 0.410156 0.460938 0.019531 0.019531 0.78\n1 2 drone 0.410156 0.460938 0.019531 0.019531 0.89\n1 3 drone 0.410156 0.464844 0.019531 0.019531 0.81\n1 4 drone 0.410156 0.464844 0.019531 0.019531 0.8\n2 0 drone 0.410156 0.464844 0.019531 0.019531 0.7\n2 1 drone 0.410156 0.464844 0.019531 0.019531 0.75\n2 2 drone 0.410156 0.464844 0.019531 0.019531 0.9\n2 3 drone 0.410156 0.468750 0.019531 0.019531 0.85\n2 4 drone 0.406250 0.464844 0.019531 0.019531 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0015_0055.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.622762 0.485863 0.117188 0.117188 0.99\n0 1 drone 0.623014 0.422236 0.117188 0.117188 0.97\n0 2 drone 0.596125 0.316402 0.117188 0.117188 0.77\n0 3 drone 0.533504 0.273180 0.117188 0.117188 0.72\n0 4 drone 0.499689 0.196596 0.117188 0.117188 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.552344 0.481445 0.010937 0.013672 0.73\n0 1 drone 0.552344 0.481445 0.010937 0.013672 0.72\n0 2 drone 0.552344 0.482422 0.010937 0.011719 0.95\n0 3 drone 0.551562 0.484375 0.012500 0.015625 0.85\n0 4 drone 0.551562 0.480469 0.012500 0.015625 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.471875 0.539062 0.018750 0.019531 0.71\n0 1 drone 0.505469 0.541016 0.020313 0.023438 0.8\n0 2 drone 0.551562 0.538086 0.018750 0.021484 0.73\n0 3 drone 0.477344 0.538086 0.026562 0.017578 0.96\n0 4 drone 0.464062 0.536133 0.018750 0.021484 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.406250 0.335938 0.019531 0.019531 0.8\n0 1 drone 0.414062 0.324219 0.019531 0.019531 0.79\n0 2 drone 0.410156 0.339844 0.019531 0.019531 0.98\n0 3 drone 0.402344 0.324219 0.019531 0.019531 0.72\n0 4 drone 0.398438 0.320312 0.019531 0.019531 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1200_1240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.455469 0.144531 0.020313 0.027344 0.99\n0 1 drone 0.452344 0.151367 0.020313 0.029297 0.94\n0 2 drone 0.450000 0.154297 0.025000 0.031250 0.89\n0 3 drone 0.442188 0.158203 0.025000 0.031250 0.9\n0 4 drone 0.438281 0.159180 0.023438 0.029297 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.410938 0.475586 0.021875 0.009766 0.81\n0 1 drone 0.410938 0.479492 0.021875 0.009766 0.81\n0 2 drone 0.411719 0.486328 0.023438 0.011719 0.83\n0 3 drone 0.409375 0.490234 0.021875 0.011719 0.91\n0 4 drone 0.406250 0.493164 0.021875 0.009766 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.402344 0.515625 0.023438 0.011719 0.87\n0 1 drone 0.401562 0.515625 0.025000 0.011719 0.85\n0 2 drone 0.400781 0.516602 0.023438 0.009766 0.94\n0 3 drone 0.400781 0.518555 0.023438 0.009766 0.77\n0 4 drone 0.400781 0.518555 0.023438 0.009766 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0848_0888.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.741674 0.740285 0.012500 0.015625 0.7"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.375000 0.300781 0.117188 0.117188 0.8\n0 1 drone 0.339844 0.273438 0.117188 0.117188 0.8\n0 2 drone 0.390625 0.320312 0.117188 0.117188 0.93\n0 3 drone 0.406250 0.308594 0.117188 0.117188 0.88\n0 4 drone 0.382812 0.281250 0.117188 0.117188 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.566406 0.473633 0.010937 0.013672 0.95\n0 1 drone 0.545312 0.473633 0.012500 0.013672 0.96\n0 2 drone 0.535156 0.475586 0.010937 0.013672 0.9\n0 3 drone 0.531250 0.475586 0.012500 0.013672 0.83\n0 4 drone 0.531250 0.474609 0.009375 0.011719 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.394531 0.019531 0.019531 0.83\n0 1 drone 0.386719 0.394531 0.019531 0.019531 0.73\n0 2 drone 0.394531 0.390625 0.019531 0.019531 0.91\n0 3 drone 0.386719 0.394531 0.019531 0.019531 0.75\n0 4 drone 0.386719 0.390625 0.019531 0.019531 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.520312 0.494141 0.012500 0.015625 0.83\n0 1 drone 0.516406 0.495117 0.010937 0.013672 0.99\n0 2 drone 0.553906 0.490234 0.010937 0.015625 0.87\n0 3 drone 0.594531 0.487305 0.014063 0.017578 0.97\n0 4 drone 0.615625 0.484375 0.012500 0.015625 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0900_0940.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.496875 0.536133 0.015625 0.017578 0.71\n0 1 drone 0.496875 0.537109 0.015625 0.019531 0.79\n0 2 drone 0.496875 0.542969 0.015625 0.019531 0.91\n0 3 drone 0.497656 0.539062 0.014063 0.019531 0.92\n0 4 drone 0.500000 0.534180 0.015625 0.017578 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.496875 0.540039 0.015625 0.017578 0.74\n0 1 drone 0.496875 0.541992 0.015625 0.017578 0.83\n0 2 drone 0.496875 0.536133 0.015625 0.017578 0.82\n0 3 drone 0.495312 0.534180 0.015625 0.017578 0.77\n0 4 drone 0.496094 0.535156 0.014063 0.015625 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.475781 0.099609 0.020313 0.031250 0.92\n0 1 drone 0.478125 0.099609 0.021875 0.031250 0.72\n0 2 drone 0.480469 0.087891 0.017188 0.023438 0.85\n0 3 drone 0.480469 0.026367 0.020313 0.025391 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.84\n0 1 drone 0.425781 0.464844 0.019531 0.019531 0.89\n0 2 drone 0.425781 0.460938 0.019531 0.019531 0.71\n0 3 drone 0.429688 0.460938 0.019531 0.019531 0.99\n0 4 drone 0.433594 0.472656 0.019531 0.019531 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.496094 0.537109 0.014063 0.015625 0.74\n0 1 drone 0.496094 0.540039 0.014063 0.017578 0.95\n0 2 drone 0.496875 0.539062 0.012500 0.015625 0.9\n0 3 drone 0.496875 0.539062 0.015625 0.019531 0.85\n0 4 drone 0.497656 0.538086 0.014063 0.017578 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1400_1440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.288281 0.211914 0.014063 0.009766 0.88\n0 1 drone 0.270313 0.208984 0.015625 0.015625 0.81\n0 2 drone 0.275000 0.207031 0.015625 0.011719 0.92\n0 3 drone 0.291406 0.206055 0.017188 0.009766 0.82\n0 4 drone 0.309375 0.204102 0.015625 0.009766 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.553125 0.485352 0.015625 0.017578 0.96\n0 1 drone 0.550781 0.476562 0.017188 0.015625 0.84\n0 2 drone 0.551562 0.485352 0.015625 0.017578 0.79\n0 3 drone 0.553906 0.484375 0.014063 0.015625 0.82\n0 4 drone 0.551562 0.486328 0.012500 0.015625 0.9\n1 0 drone 0.553125 0.487305 0.012500 0.013672 0.76\n1 1 drone 0.553906 0.487305 0.010937 0.013672 0.87\n1 2 drone 0.551562 0.488281 0.012500 0.015625 0.86\n1 3 drone 0.539062 0.491211 0.012500 0.013672 0.93\n1 4 drone 0.525781 0.490234 0.014063 0.015625 0.99\n2 0 drone 0.520312 0.494141 0.012500 0.015625 0.86\n2 1 drone 0.516406 0.495117 0.010937 0.013672 0.92\n2 2 drone 0.553906 0.490234 0.010937 0.015625 0.84\n2 3 drone 0.594531 0.487305 0.014063 0.017578 0.92\n2 4 drone 0.615625 0.484375 0.012500 0.015625 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.409375 0.349609 0.021875 0.011719 0.81\n0 1 drone 0.406250 0.347656 0.021875 0.011719 0.97\n0 2 drone 0.400781 0.348633 0.020313 0.009766 0.98\n0 3 drone 0.395313 0.349609 0.018750 0.011719 0.86\n0 4 drone 0.395313 0.349609 0.021875 0.011719 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_1290_1330.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.523438 0.226562 0.117188 0.117188 0.72\n0 1 drone 0.480469 0.156250 0.117188 0.117188 0.9\n0 2 drone 0.449219 0.156250 0.117188 0.117188 0.81\n0 3 drone 0.421875 0.117188 0.117188 0.117188 0.76\n0 4 drone 0.410156 0.156250 0.117188 0.117188 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.731250 0.549805 0.012500 0.013672 0.81\n0 1 drone 0.735938 0.593750 0.012500 0.015625 0.84\n0 2 drone 0.738281 0.635742 0.010937 0.013672 0.92\n0 3 drone 0.739844 0.678711 0.014063 0.013672 0.81\n0 4 drone 0.739844 0.723633 0.014063 0.013672 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0600_0640.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0650_0690.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.98\n0 1 drone 0.425781 0.464844 0.019531 0.019531 0.9\n0 2 drone 0.425781 0.460938 0.019531 0.019531 0.82\n0 3 drone 0.429688 0.460938 0.019531 0.019531 0.74\n0 4 drone 0.433594 0.472656 0.019531 0.019531 0.87\n1 0 drone 0.429688 0.472656 0.019531 0.019531 0.84\n1 1 drone 0.433594 0.476562 0.019531 0.019531 0.91\n1 2 drone 0.425781 0.472656 0.019531 0.019531 0.83\n1 3 drone 0.425781 0.472656 0.019531 0.019531 0.77\n1 4 drone 0.433594 0.476562 0.019531 0.019531 0.83\n2 0 drone 0.433594 0.472656 0.019531 0.019531 0.83\n2 1 drone 0.425781 0.472656 0.019531 0.019531 0.76\n2 2 drone 0.421875 0.472656 0.019531 0.019531 0.79\n2 3 drone 0.421875 0.468750 0.019531 0.019531 0.92\n2 4 drone 0.429688 0.472656 0.019531 0.019531 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0450_0490.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0500_0540.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.377344 0.346680 0.004687 0.005859 0.93\n0 2 drone 0.489844 0.343750 0.007812 0.003906 0.83\n0 3 drone 0.489063 0.342773 0.006250 0.005859 0.88\n0 4 drone 0.491406 0.319336 0.004687 0.005859 0.87\n1 0 drone 0.498437 0.291016 0.006250 0.003906 0.99\n1 1 drone 0.593750 0.285156 0.009375 0.003906 0.87\n1 2 drone 0.607812 0.284180 0.006250 0.005859 0.88\n1 3 drone 0.607031 0.282227 0.007812 0.005859 0.93\n1 4 drone 0.607812 0.282227 0.009375 0.005859 0.79\n2 0 drone 0.605469 0.281250 0.007812 0.007812 0.97\n2 1 drone 0.605469 0.280273 0.007812 0.005859 0.84\n2 2 drone 0.603906 0.278320 0.007812 0.005859 0.97\n2 3 drone 0.603906 0.277344 0.007812 0.007812 0.76\n2 4 drone 0.603906 0.277344 0.007812 0.007812 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1050_1090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1100_1140.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.495312 0.538086 0.015625 0.017578 0.71\n0 1 drone 0.483594 0.541016 0.014063 0.019531 0.86\n0 2 drone 0.484375 0.540039 0.015625 0.017578 0.99\n0 3 drone 0.518750 0.541016 0.015625 0.019531 0.72\n0 4 drone 0.518750 0.538086 0.015625 0.017578 0.95\n1 0 drone 0.505469 0.539062 0.014063 0.015625 0.95\n1 1 drone 0.482031 0.539062 0.017188 0.019531 0.82\n1 2 drone 0.498437 0.537109 0.015625 0.019531 0.79\n1 3 drone 0.525781 0.541016 0.017188 0.019531 0.72\n1 4 drone 0.515625 0.540039 0.015625 0.017578 0.84\n2 0 drone 0.464062 0.536133 0.015625 0.017578 0.9\n2 1 drone 0.462500 0.537109 0.015625 0.015625 0.77\n2 2 drone 0.507031 0.539062 0.017188 0.019531 0.91\n2 3 drone 0.524219 0.539062 0.017188 0.019531 0.75\n2 4 drone 0.518750 0.540039 0.015625 0.017578 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1200_1240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1250_1290.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.669531 0.222656 0.017188 0.011719 0.71\n0 1 drone 0.675000 0.225586 0.015625 0.013672 0.84\n0 2 drone 0.680469 0.225586 0.017188 0.013672 0.84\n0 3 drone 0.684375 0.225586 0.015625 0.009766 0.75\n0 4 drone 0.685937 0.226562 0.015625 0.007812 0.84\n1 0 drone 0.687500 0.227539 0.015625 0.009766 0.72\n1 1 drone 0.687500 0.227539 0.015625 0.009766 0.8\n1 2 drone 0.686719 0.223633 0.014063 0.009766 0.95\n1 3 drone 0.682813 0.214844 0.012500 0.011719 0.95\n1 4 drone 0.660156 0.209961 0.014063 0.013672 0.88\n2 0 drone 0.616406 0.206055 0.014063 0.009766 1.0\n2 1 drone 0.564063 0.207031 0.012500 0.007812 0.89\n2 2 drone 0.503125 0.211914 0.009375 0.005859 0.95\n2 3 drone 0.439063 0.212891 0.006250 0.007812 1.0\n2 4 drone 0.378906 0.208984 0.004687 0.003906 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0131_0171.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.380903 0.377052 0.019531 0.019531 0.87\n0 1 drone 0.370611 0.354617 0.019531 0.019531 1.0\n0 2 drone 0.387199 0.385137 0.019531 0.019531 0.99\n0 3 drone 0.388196 0.356728 0.019531 0.019531 0.74\n0 4 drone 0.406699 0.369346 0.019531 0.019531 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1050_1090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1100_1140.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.332031 0.333008 0.017188 0.009766 0.8\n0 1 drone 0.356250 0.332031 0.021875 0.011719 0.86\n0 2 drone 0.378906 0.329102 0.020313 0.009766 0.8\n0 3 drone 0.399219 0.331055 0.017188 0.009766 0.83\n0 4 drone 0.418750 0.328125 0.021875 0.011719 0.8\n1 0 drone 0.431250 0.327148 0.018750 0.013672 0.71\n1 1 drone 0.435937 0.327148 0.018750 0.013672 0.71\n1 2 drone 0.434375 0.331055 0.018750 0.009766 0.74\n1 3 drone 0.428906 0.340820 0.020313 0.009766 0.77\n1 4 drone 0.422656 0.348633 0.020313 0.009766 0.93\n2 0 drone 0.417969 0.348633 0.020313 0.009766 0.72\n2 1 drone 0.416406 0.347656 0.020313 0.007812 0.91\n2 2 drone 0.416406 0.349609 0.020313 0.007812 0.89\n2 3 drone 0.415625 0.348633 0.018750 0.009766 0.92\n2 4 drone 0.415625 0.350586 0.021875 0.009766 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.615625 0.292969 0.009375 0.007812 0.86\n0 1 drone 0.633594 0.288086 0.010937 0.009766 0.94\n0 2 drone 0.658594 0.276367 0.014063 0.009766 0.7\n0 3 drone 0.699219 0.256836 0.020313 0.009766 0.75\n0 4 drone 0.749219 0.232422 0.023438 0.015625 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.550781 0.484375 0.010937 0.015625 0.78\n0 1 drone 0.551562 0.486328 0.012500 0.015625 0.87\n0 2 drone 0.555469 0.483398 0.010937 0.013672 0.99\n0 3 drone 0.552344 0.485352 0.010937 0.013672 0.72\n0 4 drone 0.552344 0.481445 0.010937 0.013672 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.411719 0.384766 0.023438 0.011719 0.93\n0 1 drone 0.411719 0.404297 0.023438 0.011719 0.8\n0 2 drone 0.410156 0.429688 0.023438 0.011719 1.0\n0 3 drone 0.410156 0.451172 0.023438 0.011719 0.82\n0 4 drone 0.411719 0.466797 0.023438 0.011719 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.408594 0.516602 0.023438 0.009766 0.7\n0 1 drone 0.408594 0.515625 0.023438 0.011719 0.94\n0 2 drone 0.408594 0.515625 0.023438 0.011719 0.9\n0 3 drone 0.408594 0.515625 0.023438 0.011719 0.98\n0 4 drone 0.407031 0.515625 0.023438 0.011719 0.71\n1 0 drone 0.406250 0.515625 0.021875 0.011719 0.8\n1 1 drone 0.405469 0.513672 0.023438 0.011719 0.92\n1 2 drone 0.405469 0.513672 0.023438 0.011719 0.78\n1 3 drone 0.403906 0.512695 0.023438 0.009766 0.85\n1 4 drone 0.405469 0.512695 0.023438 0.009766 0.84\n2 0 drone 0.405469 0.511719 0.023438 0.007812 0.99\n2 1 drone 0.403906 0.512695 0.023438 0.009766 0.85\n2 2 drone 0.404687 0.513672 0.021875 0.011719 0.81\n2 3 drone 0.403125 0.513672 0.025000 0.011719 0.85\n2 4 drone 0.402344 0.514648 0.023438 0.009766 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.425781 0.019531 0.019531 0.98\n0 1 drone 0.253906 0.425781 0.019531 0.019531 0.81\n0 2 drone 0.273438 0.425781 0.019531 0.019531 0.87\n0 3 drone 0.277344 0.425781 0.019531 0.019531 0.96\n0 4 drone 0.261719 0.425781 0.019531 0.019531 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1173_1213.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.451603 0.088235 0.020313 0.027344 0.89\n0 1 drone 0.460659 0.149624 0.020313 0.027344 0.93\n0 2 drone 0.462100 0.148153 0.021875 0.027344 0.83\n0 3 drone 0.448370 0.144110 0.020313 0.027344 0.73\n0 4 drone 0.452043 0.168139 0.020313 0.029297 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.386719 0.019531 0.019531 0.92\n0 1 drone 0.386719 0.390625 0.019531 0.019531 0.91\n0 2 drone 0.386719 0.394531 0.019531 0.019531 0.73\n0 3 drone 0.382812 0.390625 0.019531 0.019531 0.75\n0 4 drone 0.386719 0.394531 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1350_1390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.404687 0.532227 0.018750 0.017578 0.82\n0 1 drone 0.557031 0.539062 0.023438 0.015625 0.82\n0 2 drone 0.459375 0.547852 0.031250 0.017578 0.8\n0 3 drone 0.438281 0.538086 0.020313 0.017578 0.71\n0 4 drone 0.525781 0.534180 0.020313 0.017578 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0450_0490.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0500_0540.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.501563 0.539062 0.015625 0.019531 0.77\n0 1 drone 0.500000 0.538086 0.015625 0.017578 0.83\n0 2 drone 0.496875 0.540039 0.015625 0.017578 0.9\n0 3 drone 0.495312 0.535156 0.015625 0.019531 0.99\n0 4 drone 0.498437 0.540039 0.015625 0.017578 0.71\n1 0 drone 0.499219 0.538086 0.014063 0.017578 0.92\n1 1 drone 0.497656 0.535156 0.014063 0.019531 0.82\n1 2 drone 0.495312 0.537109 0.015625 0.019531 0.82\n1 3 drone 0.493750 0.540039 0.015625 0.017578 0.73\n1 4 drone 0.499219 0.536133 0.017188 0.017578 0.78\n2 0 drone 0.499219 0.535156 0.014063 0.015625 0.9\n2 1 drone 0.498437 0.536133 0.015625 0.017578 0.99\n2 2 drone 0.501563 0.539062 0.015625 0.019531 0.97\n2 3 drone 0.496875 0.535156 0.015625 0.015625 0.74\n2 4 drone 0.499219 0.537109 0.017188 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.695312 0.346680 0.006250 0.005859 0.71\n0 1 drone 0.681250 0.344727 0.006250 0.005859 0.99\n0 2 drone 0.675000 0.347656 0.006250 0.007812 0.77\n0 3 drone 0.664844 0.348633 0.004687 0.005859 0.72\n0 4 drone 0.647656 0.351562 0.004687 0.003906 0.72\n1 0 drone 0.621094 0.355469 0.010937 0.007812 0.91\n1 1 drone 0.596875 0.356445 0.006250 0.005859 1.0\n1 2 drone 0.566406 0.354492 0.007812 0.005859 0.8\n1 3 drone 0.539062 0.352539 0.006250 0.005859 0.9\n1 4 drone 0.515625 0.349609 0.006250 0.003906 0.84\n2 0 drone 0.485156 0.348633 0.004687 0.005859 0.75\n2 1 drone 0.464844 0.344727 0.004687 0.005859 0.95\n2 2 drone 0.448437 0.347656 0.006250 0.007812 0.74\n2 3 drone 0.426563 0.344727 0.006250 0.005859 0.97\n2 4 drone 0.393750 0.344727 0.006250 0.005859 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.504883 0.020313 0.009766 0.86\n0 1 drone 0.391406 0.500000 0.020313 0.007812 0.9\n0 2 drone 0.387500 0.496094 0.018750 0.007812 0.84\n0 3 drone 0.386719 0.488281 0.020313 0.007812 0.84\n0 4 drone 0.383594 0.476562 0.023438 0.011719 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1100_1140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.505469 0.539062 0.014063 0.015625 0.9\n0 1 drone 0.482031 0.539062 0.017188 0.019531 0.77\n0 2 drone 0.498437 0.537109 0.015625 0.019531 0.76\n0 3 drone 0.525781 0.541016 0.017188 0.019531 0.95\n0 4 drone 0.515625 0.540039 0.015625 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.832031 0.161133 0.004687 0.005859 0.95\n0 1 drone 0.830469 0.159180 0.004687 0.005859 0.82\n0 2 drone 0.824219 0.157227 0.004687 0.005859 0.89\n0 3 drone 0.817969 0.149414 0.004687 0.005859 0.91\n0 4 drone 0.810156 0.139648 0.004687 0.005859 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.359375 0.500000 0.019531 0.019531 0.88\n0 1 drone 0.343750 0.500000 0.019531 0.019531 0.88\n0 2 drone 0.355469 0.500000 0.019531 0.019531 1.0\n0 3 drone 0.375000 0.496094 0.019531 0.019531 0.79\n0 4 drone 0.367188 0.507812 0.019531 0.019531 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.430469 0.117188 0.020313 0.027344 0.7\n0 1 drone 0.450781 0.108398 0.020313 0.029297 0.76\n0 2 drone 0.457813 0.107422 0.018750 0.027344 0.89\n0 3 drone 0.460156 0.124023 0.020313 0.029297 0.83\n0 4 drone 0.456250 0.130859 0.021875 0.027344 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0750_0790.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0800_0840.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.753125 0.229492 0.021875 0.021484 0.72\n0 2 drone 0.752344 0.213867 0.023438 0.017578 0.8\n1 3 drone 0.676562 0.252930 0.015625 0.009766 0.87\n2 0 drone 0.681250 0.251953 0.015625 0.015625 0.91\n2 1 drone 0.685156 0.242188 0.017188 0.011719 0.8\n2 2 drone 0.686719 0.236328 0.017188 0.011719 0.94\n2 3 drone 0.689844 0.227539 0.014063 0.009766 0.86\n2 4 drone 0.689844 0.226562 0.014063 0.011719 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1100_1140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.460938 0.019531 0.019531 0.81\n0 1 drone 0.398438 0.464844 0.019531 0.019531 0.99\n0 2 drone 0.398438 0.464844 0.019531 0.019531 0.84\n0 3 drone 0.394531 0.464844 0.019531 0.019531 0.98\n0 4 drone 0.394531 0.468750 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.371094 0.019531 0.019531 0.95\n0 1 drone 0.386719 0.367188 0.019531 0.019531 0.88\n0 2 drone 0.386719 0.375000 0.019531 0.019531 0.92\n0 3 drone 0.382812 0.371094 0.019531 0.019531 0.71\n0 4 drone 0.378906 0.363281 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1350_1390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.464844 0.019531 0.019531 0.73\n0 1 drone 0.378906 0.464844 0.019531 0.019531 0.89\n0 2 drone 0.382812 0.464844 0.019531 0.019531 0.79\n0 3 drone 0.386719 0.460938 0.019531 0.019531 0.93\n0 4 drone 0.382812 0.464844 0.019531 0.019531 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.125781 0.150391 0.020313 0.023438 0.84\n0 1 drone 0.089063 0.148438 0.018750 0.023438 0.96\n0 2 drone 0.055469 0.115234 0.017188 0.023438 0.8\n0 3 drone 0.032031 0.086914 0.017188 0.025391 0.71\n0 4 drone 0.016406 0.075195 0.017188 0.025391 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.473438 0.538086 0.018750 0.021484 0.96\n0 1 drone 0.535156 0.538086 0.020313 0.021484 0.71\n0 2 drone 0.489063 0.539062 0.021875 0.023438 0.76\n0 3 drone 0.476562 0.535156 0.018750 0.019531 0.75\n0 4 drone 0.542969 0.546875 0.017188 0.023438 0.85\n1 0 drone 0.506250 0.533203 0.018750 0.019531 0.78\n1 1 drone 0.476562 0.540039 0.018750 0.021484 0.71\n1 2 drone 0.524219 0.541016 0.020313 0.019531 0.77\n1 3 drone 0.515625 0.537109 0.018750 0.019531 0.88\n1 4 drone 0.482031 0.539062 0.017188 0.019531 0.93\n2 0 drone 0.492188 0.536133 0.018750 0.021484 0.86\n2 1 drone 0.524219 0.534180 0.020313 0.021484 0.79\n2 2 drone 0.492188 0.538086 0.018750 0.021484 0.75\n2 3 drone 0.482031 0.539062 0.020313 0.019531 0.81\n2 4 drone 0.493750 0.537109 0.018750 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.410156 0.464844 0.019531 0.019531 0.99\n0 1 drone 0.410156 0.464844 0.019531 0.019531 0.87\n0 2 drone 0.410156 0.464844 0.019531 0.019531 0.87\n0 3 drone 0.410156 0.468750 0.019531 0.019531 0.75\n0 4 drone 0.406250 0.464844 0.019531 0.019531 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.382812 0.019531 0.019531 0.89\n0 1 drone 0.378906 0.378906 0.019531 0.019531 0.83\n0 2 drone 0.414062 0.382812 0.019531 0.019531 0.73\n0 3 drone 0.394531 0.390625 0.019531 0.019531 0.97\n0 4 drone 0.382812 0.394531 0.019531 0.019531 0.92\n1 0 drone 0.378906 0.390625 0.019531 0.019531 0.96\n1 1 drone 0.378906 0.386719 0.019531 0.019531 0.87\n1 2 drone 0.375000 0.378906 0.019531 0.019531 0.77\n1 3 drone 0.378906 0.382812 0.019531 0.019531 0.84\n1 4 drone 0.382812 0.390625 0.019531 0.019531 0.98\n2 0 drone 0.390625 0.386719 0.019531 0.019531 0.73\n2 1 drone 0.386719 0.390625 0.019531 0.019531 0.73\n2 2 drone 0.386719 0.394531 0.019531 0.019531 0.78\n2 3 drone 0.382812 0.390625 0.019531 0.019531 0.91\n2 4 drone 0.386719 0.394531 0.019531 0.019531 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.464062 0.536133 0.015625 0.017578 0.82\n0 1 drone 0.462500 0.537109 0.015625 0.015625 0.78\n0 2 drone 0.507031 0.539062 0.017188 0.019531 0.93\n0 3 drone 0.524219 0.539062 0.017188 0.019531 0.81\n0 4 drone 0.518750 0.540039 0.015625 0.017578 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1350_1390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.443750 0.132812 0.021875 0.031250 0.81\n0 1 drone 0.442969 0.134766 0.023438 0.031250 0.78\n0 2 drone 0.442969 0.136719 0.023438 0.031250 0.89\n0 3 drone 0.447656 0.133789 0.023438 0.033203 0.93\n0 4 drone 0.447656 0.130859 0.023438 0.031250 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.662500 0.317383 0.018750 0.017578 0.96\n0 1 drone 0.680469 0.329102 0.017188 0.013672 0.84\n0 2 drone 0.697656 0.335938 0.017188 0.011719 0.72\n0 3 drone 0.714844 0.346680 0.014063 0.009766 0.99\n0 4 drone 0.724219 0.349609 0.014063 0.011719 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.496875 0.536133 0.015625 0.017578 0.89\n0 1 drone 0.496875 0.539062 0.015625 0.019531 0.85\n0 2 drone 0.496875 0.541992 0.015625 0.017578 0.86\n0 3 drone 0.496875 0.535156 0.015625 0.015625 0.79\n0 4 drone 0.497656 0.536133 0.014063 0.017578 0.72"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.405469 0.511719 0.023438 0.007812 0.78\n0 1 drone 0.403906 0.512695 0.023438 0.009766 0.96\n0 2 drone 0.404687 0.513672 0.021875 0.011719 0.93\n0 3 drone 0.403125 0.513672 0.025000 0.011719 0.97\n0 4 drone 0.402344 0.514648 0.023438 0.009766 0.78"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1250_1290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.460938 0.019531 0.019531 0.71\n0 1 drone 0.386719 0.460938 0.019531 0.019531 0.97\n0 2 drone 0.386719 0.460938 0.019531 0.019531 0.87\n0 3 drone 0.386719 0.460938 0.019531 0.019531 0.98\n0 4 drone 0.386719 0.460938 0.019531 0.019531 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1400_1440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.397656 0.348633 0.020313 0.013672 0.82\n0 1 drone 0.399219 0.348633 0.020313 0.013672 0.85\n0 2 drone 0.403906 0.347656 0.020313 0.011719 0.9\n0 3 drone 0.407813 0.346680 0.021875 0.013672 0.8\n0 4 drone 0.410156 0.345703 0.023438 0.015625 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.371094 0.492188 0.019531 0.019531 0.76\n0 1 drone 0.363281 0.488281 0.019531 0.019531 0.72\n0 2 drone 0.363281 0.496094 0.019531 0.019531 0.7\n0 3 drone 0.367188 0.492188 0.019531 0.019531 0.89\n0 4 drone 0.363281 0.484375 0.019531 0.019531 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0950_0990.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.673438 0.210938 0.015625 0.011719 0.78\n0 1 drone 0.670312 0.207031 0.015625 0.011719 0.99\n0 2 drone 0.670312 0.207031 0.015625 0.011719 0.76\n0 3 drone 0.667188 0.204102 0.015625 0.009766 0.82\n0 4 drone 0.664062 0.193359 0.015625 0.011719 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.839063 0.166016 0.006250 0.007812 0.91\n0 1 drone 0.833594 0.163086 0.004687 0.005859 0.99\n0 2 drone 0.832031 0.163086 0.004687 0.005859 0.75\n0 3 drone 0.832031 0.161133 0.004687 0.005859 0.77\n0 4 drone 0.833594 0.161133 0.004687 0.005859 0.77\n1 0 drone 0.832031 0.161133 0.004687 0.005859 0.81\n1 1 drone 0.830469 0.159180 0.004687 0.005859 0.82\n1 2 drone 0.824219 0.157227 0.004687 0.005859 0.9\n1 3 drone 0.817969 0.149414 0.004687 0.005859 0.87\n1 4 drone 0.810156 0.139648 0.004687 0.005859 0.77\n2 0 drone 0.799219 0.127930 0.004687 0.005859 0.78\n2 1 drone 0.788281 0.116211 0.004687 0.005859 0.71\n2 2 drone 0.777344 0.102539 0.004687 0.005859 0.93\n2 3 drone 0.763281 0.088867 0.004687 0.005859 0.94\n2 4 drone 0.750781 0.073242 0.004687 0.005859 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0820_0860.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.407031 0.518555 0.023438 0.009766 0.95\n0 1 drone 0.405469 0.518555 0.023438 0.009766 0.92\n0 2 drone 0.407031 0.518555 0.023438 0.009766 0.82\n0 3 drone 0.408594 0.518555 0.023438 0.009766 0.82\n0 4 drone 0.408594 0.516602 0.023438 0.009766 0.81"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1250_1290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.475000 0.536133 0.018750 0.017578 0.9\n0 1 drone 0.482812 0.536133 0.015625 0.017578 1.0\n0 2 drone 0.516406 0.540039 0.014063 0.017578 0.96\n0 3 drone 0.507031 0.540039 0.017188 0.017578 0.82\n0 4 drone 0.480469 0.538086 0.017188 0.017578 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 3 drone 0.642969 0.044922 0.004687 0.007812 0.92\n0 4 drone 0.644531 0.235352 0.004687 0.005859 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1050_1090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.659375 0.202148 0.015625 0.009766 0.95\n0 1 drone 0.657813 0.202148 0.015625 0.009766 0.81\n0 2 drone 0.657031 0.199219 0.017188 0.011719 0.81\n0 3 drone 0.657813 0.197266 0.015625 0.011719 0.73\n0 4 drone 0.658594 0.198242 0.017188 0.009766 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0900_0940.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.421875 0.460938 0.019531 0.019531 0.72\n0 1 drone 0.414062 0.460938 0.019531 0.019531 0.7\n0 2 drone 0.410156 0.457031 0.019531 0.019531 0.82\n0 3 drone 0.410156 0.460938 0.019531 0.019531 0.81\n0 4 drone 0.410156 0.460938 0.019531 0.019531 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0950_0990.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.303906 0.350586 0.017188 0.009766 0.99\n0 1 drone 0.289844 0.347656 0.017188 0.007812 0.94\n0 2 drone 0.276562 0.345703 0.015625 0.007812 0.99\n0 3 drone 0.269531 0.344727 0.010937 0.005859 0.91\n0 4 drone 0.261719 0.341797 0.020313 0.011719 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0300_0340.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0350_0390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "1 3 drone 0.642969 0.044922 0.004687 0.007812 0.8\n1 4 drone 0.644531 0.235352 0.004687 0.005859 0.75\n2 0 drone 0.645312 0.408203 0.006250 0.007812 0.78\n2 1 drone 0.643750 0.458984 0.006250 0.007812 0.83\n2 2 drone 0.643750 0.460938 0.006250 0.007812 0.76\n2 3 drone 0.643750 0.460938 0.006250 0.007812 0.95\n2 4 drone 0.553906 0.459961 0.007812 0.005859 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.550781 0.483398 0.010937 0.013672 0.99\n0 1 drone 0.550781 0.484375 0.010937 0.011719 0.82\n0 2 drone 0.553125 0.482422 0.009375 0.011719 0.96\n0 3 drone 0.550000 0.487305 0.012500 0.013672 0.76\n0 4 drone 0.550781 0.484375 0.010937 0.011719 0.86\n1 0 drone 0.551562 0.483398 0.012500 0.013672 0.95\n1 1 drone 0.552344 0.487305 0.010937 0.013672 0.74\n1 2 drone 0.551562 0.480469 0.012500 0.011719 0.89\n1 3 drone 0.551562 0.477539 0.012500 0.013672 0.81\n1 4 drone 0.553906 0.481445 0.010937 0.013672 0.84\n2 0 drone 0.548438 0.478516 0.012500 0.015625 0.78\n2 1 drone 0.553125 0.481445 0.012500 0.013672 0.89\n2 2 drone 0.553906 0.484375 0.014063 0.015625 0.98\n2 3 drone 0.550781 0.484375 0.010937 0.015625 0.72\n2 4 drone 0.552344 0.483398 0.010937 0.013672 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1100_1140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.158594 0.106445 0.020313 0.025391 0.92\n0 1 drone 0.234375 0.116211 0.018750 0.025391 0.74\n0 2 drone 0.235156 0.114258 0.020313 0.025391 0.73\n0 3 drone 0.368750 0.127930 0.021875 0.025391 0.72\n0 4 drone 0.399219 0.125977 0.020313 0.029297 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0165_0205.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.356003 0.490976 0.019531 0.019531 0.98\n0 1 drone 0.343401 0.517967 0.019531 0.019531 0.8\n0 2 drone 0.358938 0.514758 0.019531 0.019531 0.75\n0 3 drone 0.372165 0.492312 0.019531 0.019531 0.71\n0 4 drone 0.313013 0.500013 0.019531 0.019531 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0112_0152.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.368311 0.500783 0.019531 0.019531 0.93\n0 1 drone 0.361840 0.480710 0.019531 0.019531 0.7\n0 2 drone 0.366071 0.497154 0.019531 0.019531 0.78\n0 3 drone 0.336592 0.484956 0.019531 0.019531 0.92\n0 4 drone 0.360794 0.487183 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.578906 0.540039 0.020313 0.021484 0.96\n0 1 drone 0.522656 0.540039 0.020313 0.021484 0.99\n0 2 drone 0.464844 0.541016 0.020313 0.023438 0.9\n0 3 drone 0.476562 0.538086 0.018750 0.021484 0.86\n0 4 drone 0.541406 0.536133 0.020313 0.021484 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.464844 0.375000 0.007812 0.007812 0.9\n0 1 drone 0.462500 0.354492 0.006250 0.009766 0.9\n0 2 drone 0.460938 0.332031 0.006250 0.007812 0.84\n0 3 drone 0.457813 0.307617 0.006250 0.009766 0.94\n0 4 drone 0.455469 0.280273 0.007812 0.009766 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.682031 0.001953 0.004687 0.003906 0.73"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0950_0990.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.497656 0.535156 0.014063 0.015625 0.81\n0 1 drone 0.495312 0.541992 0.015625 0.021484 0.98\n0 2 drone 0.493750 0.543945 0.015625 0.017578 0.89\n0 3 drone 0.496875 0.532227 0.015625 0.017578 0.77\n0 4 drone 0.498437 0.535156 0.015625 0.019531 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.312500 0.117188 0.117188 0.87\n0 1 drone 0.351562 0.281250 0.117188 0.117188 0.98\n0 2 drone 0.375000 0.289062 0.117188 0.117188 0.84\n0 3 drone 0.355469 0.300781 0.117188 0.117188 0.91\n0 4 drone 0.343750 0.292969 0.117188 0.117188 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.398438 0.019531 0.019531 0.76\n0 1 drone 0.390625 0.394531 0.019531 0.019531 0.75\n0 2 drone 0.390625 0.394531 0.019531 0.019531 0.93\n0 3 drone 0.386719 0.398438 0.019531 0.019531 1.0\n0 4 drone 0.386719 0.390625 0.019531 0.019531 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1200_1240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.478125 0.540039 0.018750 0.021484 0.71\n0 1 drone 0.468750 0.537109 0.015625 0.019531 0.73\n0 2 drone 0.498437 0.540039 0.018750 0.017578 0.83\n0 3 drone 0.538281 0.541016 0.017188 0.015625 0.91\n0 4 drone 0.527344 0.539062 0.017188 0.019531 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.262500 0.340820 0.018750 0.009766 0.89\n0 1 drone 0.264844 0.342773 0.017188 0.009766 0.88\n0 2 drone 0.279687 0.338867 0.021875 0.009766 0.74\n0 3 drone 0.294531 0.336914 0.020313 0.009766 0.73\n0 4 drone 0.314063 0.335938 0.021875 0.011719 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.408594 0.516602 0.023438 0.009766 0.77\n0 1 drone 0.408594 0.515625 0.023438 0.011719 0.99\n0 2 drone 0.408594 0.515625 0.023438 0.011719 0.98\n0 3 drone 0.408594 0.515625 0.023438 0.011719 0.73\n0 4 drone 0.407031 0.515625 0.023438 0.011719 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.645312 0.408203 0.006250 0.007812 0.85\n0 1 drone 0.643750 0.458984 0.006250 0.007812 0.74\n0 2 drone 0.643750 0.460938 0.006250 0.007812 0.95\n0 3 drone 0.643750 0.460938 0.006250 0.007812 0.91\n0 4 drone 0.553906 0.459961 0.007812 0.005859 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.445312 0.019531 0.019531 0.83\n0 1 drone 0.296875 0.449219 0.019531 0.019531 0.77\n0 2 drone 0.277344 0.449219 0.019531 0.019531 0.89\n0 3 drone 0.289062 0.445312 0.019531 0.019531 0.74\n0 4 drone 0.304688 0.449219 0.019531 0.019531 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.367188 0.019531 0.019531 0.89\n0 1 drone 0.378906 0.367188 0.019531 0.019531 0.91\n0 2 drone 0.386719 0.382812 0.019531 0.019531 0.93\n0 3 drone 0.386719 0.371094 0.019531 0.019531 0.7\n0 4 drone 0.382812 0.375000 0.019531 0.019531 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0900_0940.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0950_0990.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.496875 0.536133 0.015625 0.017578 0.88\n0 1 drone 0.496875 0.537109 0.015625 0.019531 0.78\n0 2 drone 0.496875 0.542969 0.015625 0.019531 0.98\n0 3 drone 0.497656 0.539062 0.014063 0.019531 0.8\n0 4 drone 0.500000 0.534180 0.015625 0.017578 0.81\n1 0 drone 0.497656 0.535156 0.014063 0.015625 0.92\n1 1 drone 0.495312 0.541992 0.015625 0.021484 0.82\n1 2 drone 0.493750 0.543945 0.015625 0.017578 0.74\n1 3 drone 0.496875 0.532227 0.015625 0.017578 0.78\n1 4 drone 0.498437 0.535156 0.015625 0.019531 0.9\n2 0 drone 0.496094 0.538086 0.014063 0.017578 0.78\n2 1 drone 0.497656 0.541016 0.014063 0.015625 0.87\n2 2 drone 0.502344 0.540039 0.014063 0.017578 0.97\n2 3 drone 0.503906 0.536133 0.014063 0.017578 0.8\n2 4 drone 0.500000 0.537109 0.015625 0.015625 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.473438 0.538086 0.018750 0.021484 0.82\n0 1 drone 0.535156 0.538086 0.020313 0.021484 0.78\n0 2 drone 0.489063 0.539062 0.021875 0.023438 0.7\n0 3 drone 0.476562 0.535156 0.018750 0.019531 0.73\n0 4 drone 0.542969 0.546875 0.017188 0.023438 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0750_0790.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0800_0840.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.497656 0.536133 0.017188 0.017578 0.85\n0 1 drone 0.497656 0.537109 0.017188 0.019531 0.73\n0 2 drone 0.499219 0.537109 0.017188 0.019531 0.83\n0 3 drone 0.500781 0.537109 0.014063 0.019531 0.82\n0 4 drone 0.499219 0.538086 0.014063 0.017578 0.81\n1 0 drone 0.497656 0.540039 0.014063 0.017578 0.93\n1 1 drone 0.494531 0.541016 0.017188 0.019531 0.87\n1 2 drone 0.495312 0.542969 0.015625 0.019531 0.81\n1 3 drone 0.497656 0.537109 0.017188 0.019531 0.83\n1 4 drone 0.492969 0.536133 0.017188 0.017578 0.71\n2 0 drone 0.496875 0.541016 0.018750 0.019531 0.82\n2 1 drone 0.496094 0.537109 0.014063 0.019531 0.95\n2 2 drone 0.496875 0.533203 0.015625 0.019531 0.84\n2 3 drone 0.496094 0.538086 0.014063 0.017578 0.72\n2 4 drone 0.495312 0.536133 0.015625 0.017578 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.497656 0.540039 0.014063 0.017578 0.73\n0 1 drone 0.494531 0.541016 0.017188 0.019531 0.94\n0 2 drone 0.495312 0.542969 0.015625 0.019531 0.78\n0 3 drone 0.497656 0.537109 0.017188 0.019531 0.75\n0 4 drone 0.492969 0.536133 0.017188 0.017578 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1200_1240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1250_1290.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.478125 0.540039 0.018750 0.021484 0.77\n0 1 drone 0.468750 0.537109 0.015625 0.019531 1.0\n0 2 drone 0.498437 0.540039 0.018750 0.017578 0.79\n0 3 drone 0.538281 0.541016 0.017188 0.015625 0.82\n0 4 drone 0.527344 0.539062 0.017188 0.019531 0.75\n1 0 drone 0.475000 0.536133 0.018750 0.017578 0.93\n1 1 drone 0.482812 0.536133 0.015625 0.017578 0.75\n1 2 drone 0.516406 0.540039 0.014063 0.017578 0.92\n1 3 drone 0.507031 0.540039 0.017188 0.017578 0.93\n1 4 drone 0.480469 0.538086 0.017188 0.017578 0.8\n2 0 drone 0.465625 0.532227 0.015625 0.017578 0.73\n2 1 drone 0.474219 0.535156 0.017188 0.019531 1.0\n2 2 drone 0.566406 0.549805 0.020313 0.021484 0.71\n2 3 drone 0.572656 0.545898 0.020313 0.017578 0.98\n2 4 drone 0.375781 0.543945 0.017188 0.017578 0.73"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.365625 0.374023 0.009375 0.013672 0.97\n0 1 drone 0.361719 0.326172 0.010937 0.011719 0.75\n0 2 drone 0.356250 0.275391 0.009375 0.011719 0.96\n0 3 drone 0.352344 0.216797 0.010937 0.011719 0.92\n0 4 drone 0.345313 0.153320 0.012500 0.013672 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.273438 0.441406 0.019531 0.019531 0.89\n0 1 drone 0.285156 0.441406 0.019531 0.019531 0.78\n0 2 drone 0.269531 0.445312 0.019531 0.019531 0.89\n0 3 drone 0.285156 0.445312 0.019531 0.019531 0.77\n0 4 drone 0.265625 0.445312 0.019531 0.019531 0.82\n1 0 drone 0.277344 0.445312 0.019531 0.019531 0.99\n1 1 drone 0.308594 0.445312 0.019531 0.019531 0.78\n1 2 drone 0.277344 0.445312 0.019531 0.019531 0.85\n1 3 drone 0.285156 0.445312 0.019531 0.019531 0.79\n1 4 drone 0.289062 0.445312 0.019531 0.019531 0.97\n2 0 drone 0.277344 0.445312 0.019531 0.019531 0.84\n2 1 drone 0.296875 0.449219 0.019531 0.019531 0.8\n2 2 drone 0.277344 0.449219 0.019531 0.019531 0.79\n2 3 drone 0.289062 0.445312 0.019531 0.019531 0.9\n2 4 drone 0.304688 0.449219 0.019531 0.019531 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_1330_1370.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: negative\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "未检测到目标。"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.398438 0.367188 0.019531 0.019531 0.76\n0 1 drone 0.390625 0.359375 0.019531 0.019531 0.87\n0 2 drone 0.382812 0.359375 0.019531 0.019531 0.76\n0 3 drone 0.382812 0.371094 0.019531 0.019531 0.83\n0 4 drone 0.398438 0.359375 0.019531 0.019531 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0259_0299.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.385751 0.345203 0.019531 0.019531 0.71\n0 1 drone 0.382010 0.354580 0.019531 0.019531 0.87\n0 2 drone 0.391300 0.335691 0.019531 0.019531 0.91\n0 3 drone 0.386839 0.360407 0.019531 0.019531 0.88\n0 4 drone 0.411912 0.374226 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1400_1440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.550000 0.537109 0.018750 0.019531 0.73\n0 1 drone 0.495312 0.539062 0.018750 0.019531 0.89\n0 2 drone 0.466406 0.538086 0.020313 0.021484 0.91\n0 3 drone 0.453125 0.540039 0.018750 0.021484 1.0\n0 4 drone 0.528906 0.540039 0.023438 0.021484 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1350_1390.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1400_1440.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.404687 0.532227 0.018750 0.017578 0.94\n0 1 drone 0.557031 0.539062 0.023438 0.015625 0.85\n0 2 drone 0.459375 0.547852 0.031250 0.017578 0.94\n0 3 drone 0.438281 0.538086 0.020313 0.017578 0.7\n0 4 drone 0.525781 0.534180 0.020313 0.017578 0.79\n1 0 drone 0.550000 0.537109 0.018750 0.019531 1.0\n1 1 drone 0.495312 0.539062 0.018750 0.019531 0.99\n1 2 drone 0.466406 0.538086 0.020313 0.021484 0.81\n1 3 drone 0.453125 0.540039 0.018750 0.021484 0.87\n1 4 drone 0.528906 0.540039 0.023438 0.021484 0.85\n2 0 drone 0.578906 0.540039 0.020313 0.021484 0.75\n2 1 drone 0.522656 0.540039 0.020313 0.021484 0.85\n2 2 drone 0.464844 0.541016 0.020313 0.023438 0.96\n2 3 drone 0.476562 0.538086 0.018750 0.021484 0.8\n2 4 drone 0.541406 0.536133 0.020313 0.021484 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.611719 0.481445 0.010937 0.013672 0.89\n0 1 drone 0.609375 0.481445 0.012500 0.013672 0.8\n0 2 drone 0.606250 0.478516 0.012500 0.015625 0.84\n0 3 drone 0.601562 0.478516 0.012500 0.015625 0.76\n0 4 drone 0.582031 0.473633 0.010937 0.013672 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0377_0417.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.540600 0.340545 0.006250 0.005859 0.75\n0 1 drone 0.524255 0.348071 0.006250 0.005859 0.98\n0 2 drone 0.481469 0.351679 0.004687 0.005859 0.78\n0 3 drone 0.458133 0.333301 0.004687 0.005859 0.85\n0 4 drone 0.451179 0.342429 0.007812 0.007812 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1050_1090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.332031 0.333008 0.017188 0.009766 0.84\n0 1 drone 0.356250 0.332031 0.021875 0.011719 0.77\n0 2 drone 0.378906 0.329102 0.020313 0.009766 0.88\n0 3 drone 0.399219 0.331055 0.017188 0.009766 0.99\n0 4 drone 0.418750 0.328125 0.021875 0.011719 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.465625 0.532227 0.015625 0.017578 0.74\n0 1 drone 0.474219 0.535156 0.017188 0.019531 0.84\n0 2 drone 0.566406 0.549805 0.020313 0.021484 0.79\n0 3 drone 0.572656 0.545898 0.020313 0.017578 0.75\n0 4 drone 0.375781 0.543945 0.017188 0.017578 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0450_0490.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0500_0540.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.402344 0.515625 0.023438 0.011719 0.91\n0 1 drone 0.401562 0.515625 0.025000 0.011719 0.89\n0 2 drone 0.400781 0.516602 0.023438 0.009766 0.98\n0 3 drone 0.400781 0.518555 0.023438 0.009766 0.72\n0 4 drone 0.400781 0.518555 0.023438 0.009766 0.76\n1 0 drone 0.400000 0.520508 0.021875 0.009766 0.78\n1 1 drone 0.400000 0.519531 0.021875 0.007812 0.71\n1 2 drone 0.400781 0.519531 0.017188 0.007812 0.87\n1 3 drone 0.400000 0.515625 0.021875 0.011719 0.78\n1 4 drone 0.398438 0.510742 0.021875 0.009766 0.93\n2 0 drone 0.394531 0.504883 0.020313 0.009766 0.72\n2 1 drone 0.391406 0.500000 0.020313 0.007812 0.87\n2 2 drone 0.387500 0.496094 0.018750 0.007812 0.88\n2 3 drone 0.386719 0.488281 0.020313 0.007812 0.73\n2 4 drone 0.383594 0.476562 0.023438 0.011719 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0141_0181.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.408911 0.336662 0.019531 0.019531 0.98\n0 1 drone 0.420268 0.331258 0.019531 0.019531 0.96\n0 2 drone 0.411326 0.332642 0.019531 0.019531 0.74\n0 3 drone 0.418220 0.366725 0.019531 0.019531 0.89\n0 4 drone 0.401465 0.354213 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.402344 0.394531 0.019531 0.019531 0.99\n0 1 drone 0.386719 0.390625 0.019531 0.019531 0.79\n0 2 drone 0.386719 0.390625 0.019531 0.019531 0.88\n0 3 drone 0.390625 0.382812 0.019531 0.019531 0.98\n0 4 drone 0.386719 0.394531 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0269_0309.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.365383 0.307312 0.117188 0.117188 0.79\n0 1 drone 0.388463 0.302323 0.117188 0.117188 0.97\n0 2 drone 0.353835 0.295040 0.117188 0.117188 0.74\n0 3 drone 0.367502 0.325375 0.117188 0.117188 0.76\n0 4 drone 0.368850 0.279614 0.117188 0.117188 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.389844 0.499023 0.007812 0.009766 0.98\n0 1 drone 0.385937 0.495117 0.009375 0.009766 0.81\n0 2 drone 0.379688 0.475586 0.009375 0.009766 0.76\n0 3 drone 0.376563 0.446289 0.009375 0.009766 0.98\n0 4 drone 0.371094 0.412109 0.010937 0.011719 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.407031 0.517578 0.023438 0.011719 0.78\n0 1 drone 0.407031 0.517578 0.023438 0.011719 0.94\n0 2 drone 0.407031 0.517578 0.023438 0.011719 0.88\n0 3 drone 0.407031 0.517578 0.023438 0.011719 0.95\n0 4 drone 0.407031 0.518555 0.023438 0.009766 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.499219 0.535156 0.014063 0.015625 0.75\n0 1 drone 0.498437 0.536133 0.015625 0.017578 0.97\n0 2 drone 0.501563 0.539062 0.015625 0.019531 0.98\n0 3 drone 0.496875 0.535156 0.015625 0.015625 0.96\n0 4 drone 0.499219 0.537109 0.017188 0.019531 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.552344 0.481445 0.010937 0.013672 0.82\n0 1 drone 0.552344 0.481445 0.010937 0.013672 0.97\n0 2 drone 0.552344 0.482422 0.010937 0.011719 0.78\n0 3 drone 0.551562 0.484375 0.012500 0.015625 0.87\n0 4 drone 0.551562 0.480469 0.012500 0.015625 0.91\n1 0 drone 0.550781 0.484375 0.010937 0.015625 0.75\n1 1 drone 0.551562 0.486328 0.012500 0.015625 0.88\n1 2 drone 0.555469 0.483398 0.010937 0.013672 0.84\n1 3 drone 0.552344 0.485352 0.010937 0.013672 0.85\n1 4 drone 0.552344 0.481445 0.010937 0.013672 0.71\n2 0 drone 0.550000 0.477539 0.012500 0.013672 0.71\n2 1 drone 0.549219 0.481445 0.010937 0.013672 0.86\n2 2 drone 0.552344 0.484375 0.014063 0.015625 0.96\n2 3 drone 0.548438 0.485352 0.012500 0.013672 0.75\n2 4 drone 0.552344 0.488281 0.017188 0.019531 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1200_1240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.464844 0.019531 0.019531 0.73\n0 1 drone 0.390625 0.464844 0.019531 0.019531 0.85\n0 2 drone 0.390625 0.468750 0.019531 0.019531 0.73\n0 3 drone 0.394531 0.457031 0.019531 0.019531 0.82\n0 4 drone 0.386719 0.460938 0.019531 0.019531 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.377344 0.346680 0.004687 0.005859 0.86\n0 2 drone 0.489844 0.343750 0.007812 0.003906 0.88\n0 3 drone 0.489063 0.342773 0.006250 0.005859 0.71\n0 4 drone 0.491406 0.319336 0.004687 0.005859 0.95"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1100_1140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.658594 0.199219 0.017188 0.011719 0.78\n0 1 drone 0.657813 0.200195 0.015625 0.009766 0.84\n0 2 drone 0.657813 0.203125 0.015625 0.011719 0.81\n0 3 drone 0.660937 0.206055 0.018750 0.013672 0.76\n0 4 drone 0.662500 0.207031 0.015625 0.011719 0.98"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.605469 0.281250 0.007812 0.007812 0.95\n0 1 drone 0.605469 0.280273 0.007812 0.005859 0.72\n0 2 drone 0.603906 0.278320 0.007812 0.005859 0.92\n0 3 drone 0.603906 0.277344 0.007812 0.007812 0.97\n0 4 drone 0.603906 0.277344 0.007812 0.007812 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0450_0490.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0500_0540.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.390625 0.019531 0.019531 0.86\n0 1 drone 0.398438 0.382812 0.019531 0.019531 0.96\n0 2 drone 0.394531 0.394531 0.019531 0.019531 0.89\n0 3 drone 0.390625 0.386719 0.019531 0.019531 0.92\n0 4 drone 0.390625 0.394531 0.019531 0.019531 0.82\n1 0 drone 0.386719 0.390625 0.019531 0.019531 0.86\n1 1 drone 0.386719 0.394531 0.019531 0.019531 0.78\n1 2 drone 0.386719 0.394531 0.019531 0.019531 0.92\n1 3 drone 0.453125 0.375000 0.019531 0.019531 0.79\n1 4 drone 0.441406 0.390625 0.019531 0.019531 0.81\n2 0 drone 0.402344 0.394531 0.019531 0.019531 0.9\n2 1 drone 0.386719 0.390625 0.019531 0.019531 0.9\n2 2 drone 0.386719 0.390625 0.019531 0.019531 0.96\n2 3 drone 0.390625 0.382812 0.019531 0.019531 0.91\n2 4 drone 0.386719 0.394531 0.019531 0.019531 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.695312 0.346680 0.006250 0.005859 0.85\n0 1 drone 0.681250 0.344727 0.006250 0.005859 0.97\n0 2 drone 0.675000 0.347656 0.006250 0.007812 0.88\n0 3 drone 0.664844 0.348633 0.004687 0.005859 0.84\n0 4 drone 0.647656 0.351562 0.004687 0.003906 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.363281 0.085938 0.117188 0.117188 0.82\n0 1 drone 0.371094 0.148438 0.117188 0.117188 0.98\n0 2 drone 0.406250 0.191406 0.117188 0.117188 0.89\n0 3 drone 0.355469 0.132812 0.117188 0.117188 0.99\n0 4 drone 0.308594 0.207031 0.117188 0.117188 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1050_1090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.495312 0.538086 0.015625 0.017578 0.94\n0 1 drone 0.483594 0.541016 0.014063 0.019531 0.94\n0 2 drone 0.484375 0.540039 0.015625 0.017578 0.89\n0 3 drone 0.518750 0.541016 0.015625 0.019531 0.72\n0 4 drone 0.518750 0.538086 0.015625 0.017578 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.551562 0.483398 0.012500 0.013672 0.94\n0 1 drone 0.552344 0.487305 0.010937 0.013672 0.71\n0 2 drone 0.551562 0.480469 0.012500 0.011719 0.88\n0 3 drone 0.551562 0.477539 0.012500 0.013672 0.84\n0 4 drone 0.553906 0.481445 0.010937 0.013672 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.548438 0.478516 0.012500 0.015625 0.73\n0 1 drone 0.553125 0.481445 0.012500 0.013672 0.72\n0 2 drone 0.553906 0.484375 0.014063 0.015625 0.91\n0 3 drone 0.550781 0.484375 0.010937 0.015625 0.74\n0 4 drone 0.552344 0.483398 0.010937 0.013672 0.85"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.433594 0.472656 0.019531 0.019531 0.75\n0 1 drone 0.429688 0.468750 0.019531 0.019531 0.82\n0 2 drone 0.429688 0.472656 0.019531 0.019531 0.89\n0 3 drone 0.425781 0.472656 0.019531 0.019531 0.98\n0 4 drone 0.421875 0.460938 0.019531 0.019531 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0750_0790.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0800_0840.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.445312 0.295898 0.018750 0.009766 0.79\n0 1 drone 0.444531 0.303711 0.020313 0.009766 0.87\n0 2 drone 0.446875 0.309570 0.018750 0.005859 0.83\n0 3 drone 0.451562 0.316406 0.012500 0.007812 0.99\n0 4 drone 0.454688 0.329102 0.012500 0.009766 0.7\n1 0 drone 0.456250 0.341797 0.012500 0.007812 0.82\n1 1 drone 0.456250 0.358398 0.018750 0.009766 0.83\n1 2 drone 0.457031 0.366211 0.017188 0.009766 0.88\n1 3 drone 0.457031 0.368164 0.017188 0.009766 0.89\n1 4 drone 0.460156 0.370117 0.020313 0.009766 0.84\n2 0 drone 0.455469 0.369141 0.020313 0.011719 0.84\n2 1 drone 0.449219 0.370117 0.020313 0.013672 0.95\n2 2 drone 0.438281 0.370117 0.020313 0.009766 0.98\n2 3 drone 0.423438 0.370117 0.021875 0.009766 0.74\n2 4 drone 0.408594 0.372070 0.020313 0.009766 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.653125 0.274414 0.018750 0.013672 0.87\n0 1 drone 0.654687 0.274414 0.018750 0.013672 0.86\n0 2 drone 0.652344 0.282227 0.017188 0.009766 0.77\n0 3 drone 0.650781 0.290039 0.017188 0.017578 0.88\n0 4 drone 0.654687 0.302734 0.018750 0.015625 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.621875 0.470703 0.012500 0.015625 0.82\n0 1 drone 0.617969 0.473633 0.010937 0.013672 0.79\n0 2 drone 0.343750 0.473633 0.012500 0.013672 0.76\n0 3 drone 0.345313 0.474609 0.012500 0.011719 0.72\n0 4 drone 0.343750 0.479492 0.012500 0.013672 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.367188 0.257812 0.117188 0.117188 0.96\n0 1 drone 0.312500 0.214844 0.117188 0.117188 0.89\n0 2 drone 0.406250 0.273438 0.117188 0.117188 1.0\n0 3 drone 0.355469 0.234375 0.117188 0.117188 0.71\n0 4 drone 0.359375 0.269531 0.117188 0.117188 0.77\n1 0 drone 0.375000 0.300781 0.117188 0.117188 0.88\n1 1 drone 0.332031 0.265625 0.117188 0.117188 0.76\n1 2 drone 0.367188 0.304688 0.117188 0.117188 0.84\n1 3 drone 0.347656 0.292969 0.117188 0.117188 0.89\n1 4 drone 0.367188 0.281250 0.117188 0.117188 0.7\n2 0 drone 0.355469 0.300781 0.117188 0.117188 0.99\n2 1 drone 0.312500 0.269531 0.117188 0.117188 0.92\n2 2 drone 0.355469 0.289062 0.117188 0.117188 0.73\n2 3 drone 0.382812 0.304688 0.117188 0.117188 0.91\n2 4 drone 0.367188 0.285156 0.117188 0.117188 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.417187 0.132812 0.009375 0.015625 0.86\n0 1 drone 0.412500 0.309570 0.009375 0.013672 0.99\n0 2 drone 0.404687 0.389648 0.009375 0.013672 0.84\n0 3 drone 0.399219 0.385742 0.010937 0.013672 0.7\n0 4 drone 0.393750 0.400391 0.009375 0.011719 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.489063 0.538086 0.021875 0.021484 1.0\n0 1 drone 0.546094 0.536133 0.020313 0.021484 0.85\n0 2 drone 0.513281 0.538086 0.020313 0.021484 0.71\n0 3 drone 0.505469 0.538086 0.020313 0.025391 0.79\n0 4 drone 0.482812 0.539062 0.018750 0.023438 0.77\n1 0 drone 0.485156 0.540039 0.020313 0.021484 0.9\n1 1 drone 0.527344 0.537109 0.020313 0.023438 0.97\n1 2 drone 0.535156 0.538086 0.020313 0.021484 0.88\n1 3 drone 0.485938 0.539062 0.018750 0.023438 0.95\n1 4 drone 0.474219 0.537109 0.020313 0.019531 0.93\n2 0 drone 0.471875 0.539062 0.018750 0.019531 0.7\n2 1 drone 0.505469 0.541016 0.020313 0.023438 0.74\n2 2 drone 0.551562 0.538086 0.018750 0.021484 1.0\n2 3 drone 0.477344 0.538086 0.026562 0.017578 0.75\n2 4 drone 0.464062 0.536133 0.018750 0.021484 0.74"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.429688 0.472656 0.019531 0.019531 0.73\n0 1 drone 0.433594 0.476562 0.019531 0.019531 0.79\n0 2 drone 0.425781 0.472656 0.019531 0.019531 0.74\n0 3 drone 0.425781 0.472656 0.019531 0.019531 0.84\n0 4 drone 0.433594 0.476562 0.019531 0.019531 0.71"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.277344 0.425781 0.019531 0.019531 0.72\n0 1 drone 0.253906 0.425781 0.019531 0.019531 0.85\n0 2 drone 0.273438 0.425781 0.019531 0.019531 0.9\n0 3 drone 0.277344 0.425781 0.019531 0.019531 0.76\n0 4 drone 0.261719 0.425781 0.019531 0.019531 1.0\n1 0 drone 0.226562 0.429688 0.019531 0.019531 0.83\n1 1 drone 0.242188 0.425781 0.019531 0.019531 0.85\n1 2 drone 0.273438 0.429688 0.019531 0.019531 0.98\n1 3 drone 0.261719 0.437500 0.019531 0.019531 0.83\n1 4 drone 0.253906 0.437500 0.019531 0.019531 0.92\n2 0 drone 0.277344 0.441406 0.019531 0.019531 0.92\n2 1 drone 0.250000 0.437500 0.019531 0.019531 0.89\n2 2 drone 0.265625 0.437500 0.019531 0.019531 0.77\n2 3 drone 0.289062 0.441406 0.019531 0.019531 0.87\n2 4 drone 0.265625 0.437500 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.390625 0.316406 0.019531 0.019531 0.95\n0 1 drone 0.417969 0.320312 0.019531 0.019531 0.82\n0 2 drone 0.406250 0.343750 0.019531 0.019531 0.81\n0 3 drone 0.402344 0.324219 0.019531 0.019531 0.76\n0 4 drone 0.417969 0.335938 0.019531 0.019531 0.77\n1 0 drone 0.406250 0.335938 0.019531 0.019531 0.98\n1 1 drone 0.414062 0.324219 0.019531 0.019531 0.7\n1 2 drone 0.410156 0.339844 0.019531 0.019531 0.76\n1 3 drone 0.402344 0.324219 0.019531 0.019531 1.0\n1 4 drone 0.398438 0.320312 0.019531 0.019531 0.99\n2 0 drone 0.406250 0.347656 0.019531 0.019531 0.95\n2 1 drone 0.398438 0.316406 0.019531 0.019531 0.96\n2 2 drone 0.398438 0.339844 0.019531 0.019531 0.93\n2 3 drone 0.402344 0.339844 0.019531 0.019531 0.93\n2 4 drone 0.414062 0.339844 0.019531 0.019531 0.92"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.414062 0.328125 0.019531 0.019531 0.79\n0 1 drone 0.406250 0.339844 0.019531 0.019531 0.71\n0 2 drone 0.398438 0.367188 0.019531 0.019531 0.93\n0 3 drone 0.414062 0.351562 0.019531 0.019531 0.84\n0 4 drone 0.406250 0.359375 0.019531 0.019531 0.9\n1 0 drone 0.394531 0.355469 0.019531 0.019531 0.7\n1 1 drone 0.382812 0.351562 0.019531 0.019531 0.87\n1 2 drone 0.378906 0.371094 0.019531 0.019531 0.97\n1 3 drone 0.394531 0.355469 0.019531 0.019531 0.96\n1 4 drone 0.398438 0.359375 0.019531 0.019531 0.82\n2 0 drone 0.406250 0.371094 0.019531 0.019531 0.89\n2 1 drone 0.394531 0.347656 0.019531 0.019531 0.94\n2 2 drone 0.386719 0.363281 0.019531 0.019531 0.96\n2 3 drone 0.382812 0.359375 0.019531 0.019531 0.82\n2 4 drone 0.398438 0.355469 0.019531 0.019531 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0000_0040.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0050_0090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.761719 0.339844 0.020313 0.023438 0.99\n0 1 drone 0.785156 0.332031 0.023438 0.019531 0.92\n0 2 drone 0.816406 0.327148 0.023438 0.017578 0.98\n0 3 drone 0.817187 0.318359 0.018750 0.011719 0.96\n0 4 drone 0.770312 0.310547 0.021875 0.011719 0.8\n1 0 drone 0.787500 0.304688 0.021875 0.019531 0.91\n1 1 drone 0.734375 0.299805 0.018750 0.017578 0.92\n1 2 drone 0.710938 0.290039 0.021875 0.013672 0.72\n1 3 drone 0.715625 0.285156 0.021875 0.011719 0.96\n1 4 drone 0.732031 0.279297 0.017188 0.011719 0.91\n2 0 drone 0.748437 0.279297 0.021875 0.015625 0.92\n2 1 drone 0.667969 0.279297 0.020313 0.011719 0.91\n2 2 drone 0.636719 0.284180 0.017188 0.017578 0.71\n2 3 drone 0.637500 0.284180 0.021875 0.013672 0.86\n2 4 drone 0.645312 0.281250 0.018750 0.011719 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1050_1090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.008594 0.071289 0.017188 0.025391 1.0\n0 1 drone 0.005469 0.071289 0.010937 0.025391 0.75\n0 2 drone 0.003906 0.070312 0.007812 0.023438 0.99\n0 3 drone 0.002344 0.072266 0.004687 0.023438 0.71\n0 4 drone 0.003906 0.074219 0.007812 0.023438 0.89"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0829_0869.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.475176 0.354575 0.017188 0.009766 0.92\n0 1 drone 0.468197 0.363380 0.020313 0.009766 0.95\n0 2 drone 0.448663 0.381119 0.021875 0.011719 0.99\n0 3 drone 0.442070 0.352722 0.021875 0.011719 0.76\n0 4 drone 0.440326 0.358365 0.020313 0.009766 0.93"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.425781 0.464844 0.019531 0.019531 0.94\n0 1 drone 0.425781 0.453125 0.019531 0.019531 0.83\n0 2 drone 0.421875 0.457031 0.019531 0.019531 0.86\n0 3 drone 0.410156 0.460938 0.019531 0.019531 0.72\n0 4 drone 0.417969 0.457031 0.019531 0.019531 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1200_1240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1250_1290.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.464844 0.019531 0.019531 0.77\n0 1 drone 0.390625 0.464844 0.019531 0.019531 0.91\n0 2 drone 0.390625 0.468750 0.019531 0.019531 0.88\n0 3 drone 0.394531 0.457031 0.019531 0.019531 0.76\n0 4 drone 0.386719 0.460938 0.019531 0.019531 0.72\n1 0 drone 0.386719 0.460938 0.019531 0.019531 0.87\n1 1 drone 0.386719 0.460938 0.019531 0.019531 0.86\n1 2 drone 0.386719 0.460938 0.019531 0.019531 0.97\n1 3 drone 0.386719 0.460938 0.019531 0.019531 0.9\n1 4 drone 0.386719 0.460938 0.019531 0.019531 0.7\n2 0 drone 0.386719 0.460938 0.019531 0.019531 0.74\n2 1 drone 0.386719 0.460938 0.019531 0.019531 0.83\n2 2 drone 0.390625 0.464844 0.019531 0.019531 0.71\n2 3 drone 0.394531 0.464844 0.019531 0.019531 0.84\n2 4 drone 0.386719 0.464844 0.019531 0.019531 0.91"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.550781 0.483398 0.010937 0.013672 0.88\n0 1 drone 0.550781 0.484375 0.010937 0.011719 0.76\n0 2 drone 0.553125 0.482422 0.009375 0.011719 0.85\n0 3 drone 0.550000 0.487305 0.012500 0.013672 0.91\n0 4 drone 0.550781 0.484375 0.010937 0.011719 0.84"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1231_1271.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.434239 0.149915 0.025000 0.031250 0.77\n0 1 drone 0.457939 0.155898 0.023438 0.029297 0.91\n0 2 drone 0.421611 0.132788 0.021875 0.035156 0.93\n0 3 drone 0.433197 0.107101 0.021875 0.031250 0.81\n0 4 drone 0.438205 0.101154 0.021875 0.027344 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1050_1090.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1100_1140.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.659375 0.202148 0.015625 0.009766 0.76\n0 1 drone 0.657813 0.202148 0.015625 0.009766 0.89\n0 2 drone 0.657031 0.199219 0.017188 0.011719 0.89\n0 3 drone 0.657813 0.197266 0.015625 0.011719 0.74\n0 4 drone 0.658594 0.198242 0.017188 0.009766 0.77\n1 0 drone 0.658594 0.199219 0.017188 0.011719 0.82\n1 1 drone 0.657813 0.200195 0.015625 0.009766 0.96\n1 2 drone 0.657813 0.203125 0.015625 0.011719 0.96\n1 3 drone 0.660937 0.206055 0.018750 0.013672 0.99\n1 4 drone 0.662500 0.207031 0.015625 0.011719 0.76\n2 0 drone 0.663281 0.209961 0.017188 0.013672 0.81\n2 1 drone 0.664062 0.212891 0.015625 0.011719 0.95\n2 2 drone 0.664844 0.214844 0.017188 0.011719 0.87\n2 3 drone 0.664844 0.219727 0.017188 0.009766 0.71\n2 4 drone 0.667188 0.221680 0.015625 0.009766 0.75"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0050_0090.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.394531 0.359375 0.019531 0.019531 0.89\n0 1 drone 0.398438 0.367188 0.019531 0.019531 0.74\n0 2 drone 0.398438 0.359375 0.019531 0.019531 0.8\n0 3 drone 0.398438 0.355469 0.019531 0.019531 0.85\n0 4 drone 0.398438 0.371094 0.019531 0.019531 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1200_1240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1250_1290.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.414062 0.351562 0.021875 0.011719 0.73\n0 1 drone 0.412500 0.351562 0.021875 0.011719 0.96\n0 2 drone 0.410938 0.349609 0.021875 0.007812 0.75\n0 3 drone 0.410156 0.350586 0.020313 0.009766 0.84\n0 4 drone 0.410938 0.350586 0.018750 0.009766 0.74\n1 0 drone 0.410156 0.348633 0.020313 0.009766 0.9\n1 1 drone 0.409375 0.348633 0.021875 0.009766 0.76\n1 2 drone 0.410156 0.348633 0.020313 0.009766 0.9\n1 3 drone 0.410156 0.346680 0.020313 0.009766 0.79\n1 4 drone 0.410938 0.346680 0.021875 0.009766 0.73\n2 0 drone 0.409375 0.349609 0.021875 0.011719 0.88\n2 1 drone 0.406250 0.347656 0.021875 0.011719 0.88\n2 2 drone 0.400781 0.348633 0.020313 0.009766 0.72\n2 3 drone 0.395313 0.349609 0.018750 0.011719 0.96\n2 4 drone 0.395313 0.349609 0.021875 0.011719 0.8"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0001_0041.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: augmented\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.697194 0.603241 0.117188 0.117188 0.94\n0 1 drone 0.640047 0.510334 0.117188 0.117188 0.95\n0 2 drone 0.634010 0.420271 0.117188 0.117188 0.94\n0 3 drone 0.595897 0.364184 0.117188 0.117188 0.83\n0 4 drone 0.540069 0.274590 0.117188 0.117188 0.77"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.663281 0.209961 0.017188 0.013672 0.83\n0 1 drone 0.664062 0.212891 0.015625 0.011719 0.75\n0 2 drone 0.664844 0.214844 0.017188 0.011719 0.97\n0 3 drone 0.664844 0.219727 0.017188 0.009766 0.93\n0 4 drone 0.667188 0.221680 0.015625 0.009766 0.76"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.403906 0.517578 0.023438 0.011719 0.91\n0 1 drone 0.403906 0.517578 0.023438 0.011719 0.85\n0 2 drone 0.405469 0.514648 0.023438 0.009766 0.76\n0 3 drone 0.405469 0.517578 0.023438 0.011719 0.8\n0 4 drone 0.407031 0.517578 0.023438 0.011719 0.99\n1 0 drone 0.407031 0.517578 0.023438 0.011719 0.98\n1 1 drone 0.407031 0.517578 0.023438 0.011719 0.93\n1 2 drone 0.407031 0.517578 0.023438 0.011719 0.7\n1 3 drone 0.407031 0.517578 0.023438 0.011719 0.97\n1 4 drone 0.407031 0.518555 0.023438 0.009766 0.9\n2 0 drone 0.407031 0.518555 0.023438 0.009766 0.74\n2 1 drone 0.405469 0.518555 0.023438 0.009766 0.78\n2 2 drone 0.407031 0.518555 0.023438 0.009766 0.81\n2 3 drone 0.408594 0.518555 0.023438 0.009766 0.98\n2 4 drone 0.408594 0.516602 0.023438 0.009766 0.79"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0750_0790.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0800_0840.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.707812 0.529297 0.015625 0.015625 0.93\n0 1 drone 0.721094 0.559570 0.014063 0.013672 0.83\n0 2 drone 0.718750 0.441406 0.015625 0.019531 0.75\n0 3 drone 0.724219 0.461914 0.014063 0.013672 0.96\n0 4 drone 0.728906 0.505859 0.010937 0.015625 0.84\n1 0 drone 0.731250 0.549805 0.012500 0.013672 0.8\n1 1 drone 0.735938 0.593750 0.012500 0.015625 0.78\n1 2 drone 0.738281 0.635742 0.010937 0.013672 0.94\n1 3 drone 0.739844 0.678711 0.014063 0.013672 0.95\n1 4 drone 0.739844 0.723633 0.014063 0.013672 0.71"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.406250 0.347656 0.019531 0.019531 0.95\n0 1 drone 0.398438 0.316406 0.019531 0.019531 0.88\n0 2 drone 0.398438 0.339844 0.019531 0.019531 0.8\n0 3 drone 0.402344 0.339844 0.019531 0.019531 0.99\n0 4 drone 0.414062 0.339844 0.019531 0.019531 0.88"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data25/data25_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data25\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.616406 0.206055 0.014063 0.009766 0.95\n0 1 drone 0.564063 0.207031 0.012500 0.007812 0.98\n0 2 drone 0.503125 0.211914 0.009375 0.005859 0.93\n0 3 drone 0.439063 0.212891 0.006250 0.007812 0.75\n0 4 drone 0.378906 0.208984 0.004687 0.003906 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1300_1340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.460938 0.019531 0.019531 0.92\n0 1 drone 0.386719 0.460938 0.019531 0.019531 0.95\n0 2 drone 0.390625 0.464844 0.019531 0.019531 0.83\n0 3 drone 0.394531 0.464844 0.019531 0.019531 0.89\n0 4 drone 0.386719 0.464844 0.019531 0.019531 0.82"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.501563 0.539062 0.015625 0.019531 0.99\n0 1 drone 0.500000 0.538086 0.015625 0.017578 0.85\n0 2 drone 0.496875 0.540039 0.015625 0.017578 0.91\n0 3 drone 0.495312 0.535156 0.015625 0.019531 0.96\n0 4 drone 0.498437 0.540039 0.015625 0.017578 0.83"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.406250 0.371094 0.019531 0.019531 0.95\n0 1 drone 0.394531 0.347656 0.019531 0.019531 0.95\n0 2 drone 0.386719 0.363281 0.019531 0.019531 0.92\n0 3 drone 0.382812 0.359375 0.019531 0.019531 0.76\n0 4 drone 0.398438 0.355469 0.019531 0.019531 0.96"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.441406 0.085938 0.007812 0.007812 0.8\n0 1 drone 0.437500 0.046875 0.009375 0.007812 0.85\n0 2 drone 0.434375 0.005859 0.009375 0.011719 0.99"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data24/data24_interval10_5frames_1100_1140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data24\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.431250 0.327148 0.018750 0.013672 0.97\n0 1 drone 0.435937 0.327148 0.018750 0.013672 0.77\n0 2 drone 0.434375 0.331055 0.018750 0.009766 0.99\n0 3 drone 0.428906 0.340820 0.020313 0.009766 0.73\n0 4 drone 0.422656 0.348633 0.020313 0.009766 0.94"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0150_0190.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0200_0240.mp4"}, {"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这3个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 3个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: multi\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-2）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.739844 0.063477 0.004687 0.005859 0.93\n0 1 drone 0.728906 0.053711 0.004687 0.005859 0.77\n0 2 drone 0.719531 0.041992 0.004687 0.005859 0.89\n0 3 drone 0.707031 0.028320 0.004687 0.005859 0.73\n0 4 drone 0.695312 0.012695 0.006250 0.005859 0.73\n1 0 drone 0.682031 0.001953 0.004687 0.003906 0.87"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.304688 0.453125 0.019531 0.019531 0.9\n0 1 drone 0.339844 0.453125 0.019531 0.019531 0.82\n0 2 drone 0.328125 0.453125 0.019531 0.019531 0.84\n0 3 drone 0.328125 0.449219 0.019531 0.019531 0.86\n0 4 drone 0.347656 0.449219 0.019531 0.019531 0.86"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.496875 0.541016 0.018750 0.019531 0.99\n0 1 drone 0.496094 0.537109 0.014063 0.019531 0.87\n0 2 drone 0.496875 0.533203 0.015625 0.019531 0.95\n0 3 drone 0.496094 0.538086 0.014063 0.017578 0.87\n0 4 drone 0.495312 0.536133 0.015625 0.017578 0.97"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.799219 0.127930 0.004687 0.005859 0.87\n0 1 drone 0.788281 0.116211 0.004687 0.005859 0.84\n0 2 drone 0.777344 0.102539 0.004687 0.005859 0.74\n0 3 drone 0.763281 0.088867 0.004687 0.005859 0.96\n0 4 drone 0.750781 0.073242 0.004687 0.005859 0.9"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这1个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 视频数量: 1个\n- 每个视频: 5帧，隔10帧采样\n- 样本类型: single\n\n请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_idx frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_idx为视频索引（0-0）\n2. frame_idx为视频内帧索引（0-4）\n3. 坐标为相对坐标（0-1之间）\n4. 只检测尺寸在3-80像素之间的弱小目标\n5. 如果某帧无目标，不输出该帧的结果\n6. confidence为检测置信度（0.5-1.0之间）"}]}, {"role": "assistant", "content": "0 0 drone 0.386719 0.460938 0.019531 0.019531 0.78\n0 1 drone 0.386719 0.464844 0.019531 0.019531 0.98\n0 2 drone 0.398438 0.464844 0.019531 0.019531 0.93\n0 3 drone 0.398438 0.464844 0.019531 0.019531 0.95\n0 4 drone 0.394531 0.468750 0.019531 0.019531 0.77"}]}]