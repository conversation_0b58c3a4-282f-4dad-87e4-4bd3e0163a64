[{"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 500-540\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.499219 0.538086 0.014063 0.017578 1.0\n1 drone 0.497656 0.535156 0.014063 0.019531 1.0\n2 drone 0.495312 0.537109 0.015625 0.019531 1.0\n3 drone 0.493750 0.540039 0.015625 0.017578 1.0\n4 drone 0.499219 0.536133 0.017188 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0400_0440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 400-440\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.390625 0.386719 0.019531 0.019531 1.0\n1 drone 0.386719 0.390625 0.019531 0.019531 1.0\n2 drone 0.386719 0.394531 0.019531 0.019531 1.0\n3 drone 0.382812 0.390625 0.019531 0.019531 1.0\n4 drone 0.386719 0.394531 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1350_1390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1350-1390\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.443750 0.132812 0.021875 0.031250 1.0\n1 drone 0.442969 0.134766 0.023438 0.031250 1.0\n2 drone 0.442969 0.136719 0.023438 0.031250 1.0\n3 drone 0.447656 0.133789 0.023438 0.033203 1.0\n4 drone 0.447656 0.130859 0.023438 0.031250 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1250_1290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1250-1290\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.475000 0.536133 0.018750 0.017578 1.0\n1 drone 0.482812 0.536133 0.015625 0.017578 1.0\n2 drone 0.516406 0.540039 0.014063 0.017578 1.0\n3 drone 0.507031 0.540039 0.017188 0.017578 1.0\n4 drone 0.480469 0.538086 0.017188 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 100-140\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.394531 0.371094 0.019531 0.019531 1.0\n1 drone 0.386719 0.367188 0.019531 0.019531 1.0\n2 drone 0.386719 0.375000 0.019531 0.019531 1.0\n3 drone 0.382812 0.371094 0.019531 0.019531 1.0\n4 drone 0.378906 0.363281 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0850_0890.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 850-890\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.496875 0.541016 0.018750 0.019531 1.0\n1 drone 0.496094 0.537109 0.014063 0.019531 1.0\n2 drone 0.496875 0.533203 0.015625 0.019531 1.0\n3 drone 0.496094 0.538086 0.014063 0.017578 1.0\n4 drone 0.495312 0.536133 0.015625 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1150-1190\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.386719 0.460938 0.019531 0.019531 1.0\n1 drone 0.386719 0.464844 0.019531 0.019531 1.0\n2 drone 0.398438 0.464844 0.019531 0.019531 1.0\n3 drone 0.398438 0.464844 0.019531 0.019531 1.0\n4 drone 0.394531 0.468750 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 350-390\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.304688 0.453125 0.019531 0.019531 1.0\n1 drone 0.339844 0.453125 0.019531 0.019531 1.0\n2 drone 0.328125 0.453125 0.019531 0.019531 1.0\n3 drone 0.328125 0.449219 0.019531 0.019531 1.0\n4 drone 0.347656 0.449219 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 100-140\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.363281 0.085938 0.117188 0.117188 1.0\n1 drone 0.371094 0.148438 0.117188 0.117188 1.0\n2 drone 0.406250 0.191406 0.117188 0.117188 1.0\n3 drone 0.355469 0.132812 0.117188 0.117188 1.0\n4 drone 0.308594 0.207031 0.117188 0.117188 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 600-640\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.621875 0.470703 0.012500 0.015625 1.0\n1 drone 0.617969 0.473633 0.010937 0.013672 1.0\n2 drone 0.343750 0.473633 0.012500 0.013672 1.0\n3 drone 0.345313 0.474609 0.012500 0.011719 1.0\n4 drone 0.343750 0.479492 0.012500 0.013672 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1450-1490\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.382812 0.468750 0.019531 0.019531 1.0\n1 drone 0.382812 0.468750 0.019531 0.019531 1.0\n2 drone 0.375000 0.464844 0.019531 0.019531 1.0\n3 drone 0.382812 0.468750 0.019531 0.019531 1.0\n4 drone 0.378906 0.472656 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0900_0940.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 900-940\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.496875 0.536133 0.015625 0.017578 1.0\n1 drone 0.496875 0.537109 0.015625 0.019531 1.0\n2 drone 0.496875 0.542969 0.015625 0.019531 1.0\n3 drone 0.497656 0.539062 0.014063 0.019531 1.0\n4 drone 0.500000 0.534180 0.015625 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 350-390\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.378906 0.390625 0.019531 0.019531 1.0\n1 drone 0.378906 0.386719 0.019531 0.019531 1.0\n2 drone 0.375000 0.378906 0.019531 0.019531 1.0\n3 drone 0.378906 0.382812 0.019531 0.019531 1.0\n4 drone 0.382812 0.390625 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 300-340\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.386719 0.382812 0.019531 0.019531 1.0\n1 drone 0.378906 0.378906 0.019531 0.019531 1.0\n2 drone 0.414062 0.382812 0.019531 0.019531 1.0\n3 drone 0.394531 0.390625 0.019531 0.019531 1.0\n4 drone 0.382812 0.394531 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 800-840\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.425781 0.464844 0.019531 0.019531 1.0\n1 drone 0.425781 0.453125 0.019531 0.019531 1.0\n2 drone 0.421875 0.457031 0.019531 0.019531 1.0\n3 drone 0.410156 0.460938 0.019531 0.019531 1.0\n4 drone 0.417969 0.457031 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1200_1240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1200-1240\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.455469 0.144531 0.020313 0.027344 1.0\n1 drone 0.452344 0.151367 0.020313 0.029297 1.0\n2 drone 0.450000 0.154297 0.025000 0.031250 1.0\n3 drone 0.442188 0.158203 0.025000 0.031250 1.0\n4 drone 0.438281 0.159180 0.023438 0.029297 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0300_0340.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 300-340\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.386719 0.312500 0.117188 0.117188 1.0\n1 drone 0.351562 0.281250 0.117188 0.117188 1.0\n2 drone 0.375000 0.289062 0.117188 0.117188 1.0\n3 drone 0.355469 0.300781 0.117188 0.117188 1.0\n4 drone 0.343750 0.292969 0.117188 0.117188 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1100_1140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1100-1140\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.505469 0.539062 0.014063 0.015625 1.0\n1 drone 0.482031 0.539062 0.017188 0.019531 1.0\n2 drone 0.498437 0.537109 0.015625 0.019531 1.0\n3 drone 0.525781 0.541016 0.017188 0.019531 1.0\n4 drone 0.515625 0.540039 0.015625 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 550-590\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.402344 0.394531 0.019531 0.019531 1.0\n1 drone 0.386719 0.390625 0.019531 0.019531 1.0\n2 drone 0.386719 0.390625 0.019531 0.019531 1.0\n3 drone 0.390625 0.382812 0.019531 0.019531 1.0\n4 drone 0.386719 0.394531 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 650-690\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.386719 0.398438 0.019531 0.019531 1.0\n1 drone 0.390625 0.394531 0.019531 0.019531 1.0\n2 drone 0.390625 0.394531 0.019531 0.019531 1.0\n3 drone 0.386719 0.398438 0.019531 0.019531 1.0\n4 drone 0.386719 0.390625 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 100-140\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.471875 0.539062 0.018750 0.019531 1.0\n1 drone 0.505469 0.541016 0.020313 0.023438 1.0\n2 drone 0.551562 0.538086 0.018750 0.021484 1.0\n3 drone 0.477344 0.538086 0.026562 0.017578 1.0\n4 drone 0.464062 0.536133 0.018750 0.021484 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 100-140\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.371094 0.492188 0.019531 0.019531 1.0\n1 drone 0.359375 0.496094 0.019531 0.019531 1.0\n2 drone 0.367188 0.496094 0.019531 0.019531 1.0\n3 drone 0.355469 0.496094 0.019531 0.019531 1.0\n4 drone 0.363281 0.500000 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1250_1290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1250-1290\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.386719 0.460938 0.019531 0.019531 1.0\n1 drone 0.386719 0.460938 0.019531 0.019531 1.0\n2 drone 0.386719 0.460938 0.019531 0.019531 1.0\n3 drone 0.386719 0.460938 0.019531 0.019531 1.0\n4 drone 0.386719 0.460938 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 150-190\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.473438 0.538086 0.018750 0.021484 1.0\n1 drone 0.535156 0.538086 0.020313 0.021484 1.0\n2 drone 0.489063 0.539062 0.021875 0.023438 1.0\n3 drone 0.476562 0.535156 0.018750 0.019531 1.0\n4 drone 0.542969 0.546875 0.017188 0.023438 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 450-490\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.501563 0.539062 0.015625 0.019531 1.0\n1 drone 0.500000 0.538086 0.015625 0.017578 1.0\n2 drone 0.496875 0.540039 0.015625 0.017578 1.0\n3 drone 0.495312 0.535156 0.015625 0.019531 1.0\n4 drone 0.498437 0.540039 0.015625 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 150-190\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.367188 0.500000 0.019531 0.019531 1.0\n1 drone 0.367188 0.507812 0.019531 0.019531 1.0\n2 drone 0.371094 0.503906 0.019531 0.019531 1.0\n3 drone 0.363281 0.496094 0.019531 0.019531 1.0\n4 drone 0.359375 0.500000 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data04/data04_interval10_5frames_0250_0290.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data04\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 250-290\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.406250 0.371094 0.019531 0.019531 1.0\n1 drone 0.394531 0.347656 0.019531 0.019531 1.0\n2 drone 0.386719 0.363281 0.019531 0.019531 1.0\n3 drone 0.382812 0.359375 0.019531 0.019531 1.0\n4 drone 0.398438 0.355469 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 0-40\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.371094 0.492188 0.019531 0.019531 1.0\n1 drone 0.363281 0.488281 0.019531 0.019531 1.0\n2 drone 0.363281 0.496094 0.019531 0.019531 1.0\n3 drone 0.367188 0.492188 0.019531 0.019531 1.0\n4 drone 0.363281 0.484375 0.019531 0.019531 1.0"}]}]