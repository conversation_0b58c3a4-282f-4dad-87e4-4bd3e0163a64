[{"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 550-590\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.499219 0.535156 0.014063 0.015625 1.0\n1 drone 0.498437 0.536133 0.015625 0.017578 1.0\n2 drone 0.501563 0.539062 0.015625 0.019531 1.0\n3 drone 0.496875 0.535156 0.015625 0.015625 1.0\n4 drone 0.499219 0.537109 0.017188 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 150-190\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.367188 0.257812 0.117188 0.117188 1.0\n1 drone 0.312500 0.214844 0.117188 0.117188 1.0\n2 drone 0.406250 0.273438 0.117188 0.117188 1.0\n3 drone 0.355469 0.234375 0.117188 0.117188 1.0\n4 drone 0.359375 0.269531 0.117188 0.117188 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 350-390\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.553125 0.487305 0.012500 0.013672 1.0\n1 drone 0.553906 0.487305 0.010937 0.013672 1.0\n2 drone 0.551562 0.488281 0.012500 0.015625 1.0\n3 drone 0.539062 0.491211 0.012500 0.013672 1.0\n4 drone 0.525781 0.490234 0.014063 0.015625 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0750_0790.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 750-790\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.433594 0.472656 0.019531 0.019531 1.0\n1 drone 0.429688 0.468750 0.019531 0.019531 1.0\n2 drone 0.429688 0.472656 0.019531 0.019531 1.0\n3 drone 0.425781 0.472656 0.019531 0.019531 1.0\n4 drone 0.421875 0.460938 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0450_0490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 450-490\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.501563 0.539062 0.015625 0.019531 1.0\n1 drone 0.500000 0.538086 0.015625 0.017578 1.0\n2 drone 0.496875 0.540039 0.015625 0.017578 1.0\n3 drone 0.495312 0.535156 0.015625 0.019531 1.0\n4 drone 0.498437 0.540039 0.015625 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1000_1040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1000-1040\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.125781 0.150391 0.020313 0.023438 1.0\n1 drone 0.089063 0.148438 0.018750 0.023438 1.0\n2 drone 0.055469 0.115234 0.017188 0.023438 1.0\n3 drone 0.032031 0.086914 0.017188 0.025391 1.0\n4 drone 0.016406 0.075195 0.017188 0.025391 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1200_1240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1200-1240\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.394531 0.464844 0.019531 0.019531 1.0\n1 drone 0.390625 0.464844 0.019531 0.019531 1.0\n2 drone 0.390625 0.468750 0.019531 0.019531 1.0\n3 drone 0.394531 0.457031 0.019531 0.019531 1.0\n4 drone 0.386719 0.460938 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0800_0840.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 800-840\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.731250 0.549805 0.012500 0.013672 1.0\n1 drone 0.735938 0.593750 0.012500 0.015625 1.0\n2 drone 0.738281 0.635742 0.010937 0.013672 1.0\n3 drone 0.739844 0.678711 0.014063 0.013672 1.0\n4 drone 0.739844 0.723633 0.014063 0.013672 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 350-390\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.367188 0.511719 0.019531 0.019531 1.0\n1 drone 0.359375 0.500000 0.019531 0.019531 1.0\n2 drone 0.371094 0.503906 0.019531 0.019531 1.0\n3 drone 0.359375 0.515625 0.019531 0.019531 1.0\n4 drone 0.375000 0.507812 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 500-540\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.464844 0.375000 0.007812 0.007812 1.0\n1 drone 0.462500 0.354492 0.006250 0.009766 1.0\n2 drone 0.460938 0.332031 0.006250 0.007812 1.0\n3 drone 0.457813 0.307617 0.006250 0.009766 1.0\n4 drone 0.455469 0.280273 0.007812 0.009766 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0500_0540.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 500-540\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.375000 0.453125 0.019531 0.019531 1.0\n1 drone 0.390625 0.457031 0.019531 0.019531 1.0\n2 drone 0.394531 0.453125 0.019531 0.019531 1.0\n3 drone 0.398438 0.453125 0.019531 0.019531 1.0\n4 drone 0.402344 0.453125 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0550_0590.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 550-590\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.410156 0.453125 0.019531 0.019531 1.0\n1 drone 0.410156 0.453125 0.019531 0.019531 1.0\n2 drone 0.414062 0.453125 0.019531 0.019531 1.0\n3 drone 0.417969 0.457031 0.019531 0.019531 1.0\n4 drone 0.417969 0.457031 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 650-690\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.496875 0.540039 0.015625 0.017578 1.0\n1 drone 0.496875 0.541992 0.015625 0.017578 1.0\n2 drone 0.496875 0.536133 0.015625 0.017578 1.0\n3 drone 0.495312 0.534180 0.015625 0.017578 1.0\n4 drone 0.496094 0.535156 0.014063 0.015625 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 700-740\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.433594 0.472656 0.019531 0.019531 1.0\n1 drone 0.425781 0.472656 0.019531 0.019531 1.0\n2 drone 0.421875 0.472656 0.019531 0.019531 1.0\n3 drone 0.421875 0.468750 0.019531 0.019531 1.0\n4 drone 0.429688 0.472656 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data02/data02_interval10_5frames_0650_0690.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data02\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 650-690\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.386719 0.398438 0.019531 0.019531 1.0\n1 drone 0.390625 0.394531 0.019531 0.019531 1.0\n2 drone 0.390625 0.394531 0.019531 0.019531 1.0\n3 drone 0.386719 0.398438 0.019531 0.019531 1.0\n4 drone 0.386719 0.390625 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_0700_0740.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 700-740\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.496094 0.537109 0.014063 0.015625 1.0\n1 drone 0.496094 0.540039 0.014063 0.017578 1.0\n2 drone 0.496875 0.539062 0.012500 0.015625 1.0\n3 drone 0.496875 0.539062 0.015625 0.019531 1.0\n4 drone 0.497656 0.538086 0.014063 0.017578 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data07/data07_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data07\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 0-40\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.699219 0.597656 0.117188 0.117188 1.0\n1 drone 0.640625 0.519531 0.117188 0.117188 1.0\n2 drone 0.617188 0.445312 0.117188 0.117188 1.0\n3 drone 0.593750 0.371094 0.117188 0.117188 1.0\n4 drone 0.562500 0.296875 0.117188 0.117188 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0100_0140.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 100-140\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.277344 0.441406 0.019531 0.019531 1.0\n1 drone 0.250000 0.437500 0.019531 0.019531 1.0\n2 drone 0.265625 0.437500 0.019531 0.019531 1.0\n3 drone 0.289062 0.441406 0.019531 0.019531 1.0\n4 drone 0.265625 0.437500 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 600-640\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.421875 0.460938 0.019531 0.019531 1.0\n1 drone 0.425781 0.464844 0.019531 0.019531 1.0\n2 drone 0.425781 0.460938 0.019531 0.019531 1.0\n3 drone 0.429688 0.460938 0.019531 0.019531 1.0\n4 drone 0.433594 0.472656 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0150_0190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 150-190\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.552344 0.481445 0.010937 0.013672 1.0\n1 drone 0.552344 0.481445 0.010937 0.013672 1.0\n2 drone 0.552344 0.482422 0.010937 0.011719 1.0\n3 drone 0.551562 0.484375 0.012500 0.015625 1.0\n4 drone 0.551562 0.480469 0.012500 0.015625 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_1150_1190.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1150-1190\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.386719 0.460938 0.019531 0.019531 1.0\n1 drone 0.386719 0.464844 0.019531 0.019531 1.0\n2 drone 0.398438 0.464844 0.019531 0.019531 1.0\n3 drone 0.398438 0.464844 0.019531 0.019531 1.0\n4 drone 0.394531 0.468750 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_0950_0990.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 950-990\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "2 drone 0.256250 0.006836 0.021875 0.013672 1.0\n3 drone 0.210938 0.064453 0.021875 0.031250 1.0\n4 drone 0.167969 0.122070 0.020313 0.025391 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0600_0640.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 600-640\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.621875 0.470703 0.012500 0.015625 1.0\n1 drone 0.617969 0.473633 0.010937 0.013672 1.0\n2 drone 0.343750 0.473633 0.012500 0.013672 1.0\n3 drone 0.345313 0.474609 0.012500 0.011719 1.0\n4 drone 0.343750 0.479492 0.012500 0.013672 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data19/data19_interval10_5frames_1400_1440.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data19\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1400-1440\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.550000 0.537109 0.018750 0.019531 1.0\n1 drone 0.495312 0.539062 0.018750 0.019531 1.0\n2 drone 0.466406 0.538086 0.020313 0.021484 1.0\n3 drone 0.453125 0.540039 0.018750 0.021484 1.0\n4 drone 0.528906 0.540039 0.023438 0.021484 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data01/data01_interval10_5frames_0350_0390.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data01\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 350-390\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.304688 0.453125 0.019531 0.019531 1.0\n1 drone 0.339844 0.453125 0.019531 0.019531 1.0\n2 drone 0.328125 0.453125 0.019531 0.019531 1.0\n3 drone 0.328125 0.449219 0.019531 0.019531 1.0\n4 drone 0.347656 0.449219 0.019531 0.019531 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data26/data26_interval10_5frames_1450_1490.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data26\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 1450-1490\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.475781 0.099609 0.020313 0.031250 1.0\n1 drone 0.478125 0.099609 0.021875 0.031250 1.0\n2 drone 0.480469 0.087891 0.017188 0.023438 1.0\n3 drone 0.480469 0.026367 0.020313 0.025391 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data23/data23_interval10_5frames_0000_0040.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data23\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 0-40\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.550781 0.483398 0.010937 0.013672 1.0\n1 drone 0.550781 0.484375 0.010937 0.011719 1.0\n2 drone 0.553125 0.482422 0.009375 0.011719 1.0\n3 drone 0.550000 0.487305 0.012500 0.013672 1.0\n4 drone 0.550781 0.484375 0.010937 0.011719 1.0"}]}, {"messages": [{"role": "user", "content": [{"type": "video", "video": "dataset/interval_5frame_videos/data06/data06_interval10_5frames_0200_0240.mp4"}, {"type": "text", "text": "你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。\n\n视频信息:\n- 序列: data06\n- 采样方式: 隔10帧取1帧\n- 帧数: 5帧\n- 原始帧范围: 200-240\n\n请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。\n\n输出格式：每行一个检测结果\nvideo_frame_idx class_name x_center y_center width height confidence\n\n注意：\n1. video_frame_idx为视频中的帧索引（0-4）\n2. 坐标为相对坐标（0-1之间）\n3. 只检测尺寸在3-80像素之间的弱小目标\n4. 利用5帧视频的时序信息提高检测精度\n5. confidence为检测置信度（0-1之间）"}]}, {"role": "assistant", "content": "0 drone 0.359375 0.500000 0.019531 0.019531 1.0\n1 drone 0.343750 0.500000 0.019531 0.019531 1.0\n2 drone 0.355469 0.500000 0.019531 0.019531 1.0\n3 drone 0.375000 0.496094 0.019531 0.019531 1.0\n4 drone 0.367188 0.507812 0.019531 0.019531 1.0"}]}]