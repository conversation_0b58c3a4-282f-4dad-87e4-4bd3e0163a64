#!/usr/bin/env python3
"""
精确分析坐标偏差，找出校正规律
"""

import json
import numpy as np
from pathlib import Path

def analyze_coordinate_bias():
    print("=== 精确坐标偏差分析 ===")
    
    # 分析每个序列的偏差
    sequences = ['data01', 'data02', 'data19', 'data23', 'data25', 'data26']
    
    all_x_biases = []
    all_y_biases = []
    sequence_biases = {}
    
    for seq_id in sequences:
        print(f"\n分析序列 {seq_id}:")
        
        # 加载检测结果
        det_file = Path(f"results/interval_5frame_detection/{seq_id}_detections.json")
        if not det_file.exists():
            print(f"  检测文件不存在")
            continue
            
        with open(det_file, 'r') as f:
            detections = json.load(f)
        
        if not detections:
            print(f"  无检测结果")
            continue
        
        # 分析前10个检测结果的偏差
        x_biases = []
        y_biases = []
        
        for i, det in enumerate(detections[:10]):
            frame_idx = det['original_frame_idx']
            
            # 查找对应的真实标注
            label_file = Path(f"dataset/labels/{seq_id}/{frame_idx}.txt")
            if not label_file.exists():
                continue
                
            try:
                with open(label_file, 'r') as f:
                    lines = [line.strip() for line in f if line.strip()]
                
                if lines:
                    parts = lines[0].split()
                    if len(parts) >= 5:
                        gt_x = float(parts[1])
                        gt_y = float(parts[2])
                        det_x = det['x_center']
                        det_y = det['y_center']
                        
                        x_bias = det_x - gt_x
                        y_bias = det_y - gt_y
                        
                        x_biases.append(x_bias)
                        y_biases.append(y_bias)
                        
                        print(f"  帧{frame_idx}: 检测({det_x:.3f}, {det_y:.3f}) vs 真实({gt_x:.3f}, {gt_y:.3f}) = 偏差({x_bias:.3f}, {y_bias:.3f})")
            except:
                continue
        
        if x_biases and y_biases:
            avg_x_bias = np.mean(x_biases)
            avg_y_bias = np.mean(y_biases)
            std_x_bias = np.std(x_biases)
            std_y_bias = np.std(y_biases)
            
            sequence_biases[seq_id] = {
                'x_bias': avg_x_bias,
                'y_bias': avg_y_bias,
                'x_std': std_x_bias,
                'y_std': std_y_bias,
                'samples': len(x_biases)
            }
            
            all_x_biases.extend(x_biases)
            all_y_biases.extend(y_biases)
            
            print(f"  平均偏差: X={avg_x_bias:.3f}±{std_x_bias:.3f}, Y={avg_y_bias:.3f}±{std_y_bias:.3f}")
    
    # 计算全局偏差
    if all_x_biases and all_y_biases:
        global_x_bias = np.mean(all_x_biases)
        global_y_bias = np.mean(all_y_biases)
        global_x_std = np.std(all_x_biases)
        global_y_std = np.std(all_y_biases)
        
        print(f"\n=== 全局偏差分析 ===")
        print(f"总样本数: {len(all_x_biases)}")
        print(f"全局X偏差: {global_x_bias:.6f} ± {global_x_std:.6f}")
        print(f"全局Y偏差: {global_y_bias:.6f} ± {global_y_std:.6f}")
        
        # 推荐校正值
        print(f"\n=== 推荐校正值 ===")
        print(f"X校正: {-global_x_bias:.6f}")
        print(f"Y校正: {-global_y_bias:.6f}")
        
        # 各序列偏差对比
        print(f"\n=== 各序列偏差对比 ===")
        for seq_id, bias_info in sequence_biases.items():
            print(f"{seq_id}: X={bias_info['x_bias']:+.3f}, Y={bias_info['y_bias']:+.3f} (样本数:{bias_info['samples']})")
        
        return global_x_bias, global_y_bias
    
    return None, None

if __name__ == "__main__":
    analyze_coordinate_bias()
