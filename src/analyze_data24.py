#!/usr/bin/env python3
"""
分析data24为什么有效果而其他序列没有
"""

import json
from pathlib import Path

def analyze_data24():
    print("=== 分析data24特殊性 ===")
    
    # 检查检测结果
    detection_file = Path("results/interval_5frame_detection/data24_detections.json")
    with open(detection_file, 'r') as f:
        detections = json.load(f)
    
    print(f"data24检测结果总数: {len(detections)}")
    
    # 检查真实标注
    label_dir = Path("dataset/labels/data24")
    label_files = list(label_dir.glob("*.txt"))
    
    print(f"data24真实标注文件总数: {len(label_files)}")
    
    # 检查前几个检测结果的坐标
    print("\ndata24前5个检测结果:")
    for i, det in enumerate(detections[:5]):
        print(f"  {i+1}. 帧{det['original_frame_idx']}: ({det['x_center']:.6f}, {det['y_center']:.6f})")
    
    # 检查对应的真实标注
    print("\ndata24前5个真实标注:")
    for i in range(5):
        label_file = Path(f"dataset/labels/data24/{i}.txt")
        if label_file.exists():
            with open(label_file, 'r') as f:
                lines = [line.strip() for line in f if line.strip()]
            if lines:
                parts = lines[0].split()
                if len(parts) >= 5:
                    print(f"  {i+1}. 帧{i}: ({parts[1]}, {parts[2]})")
    
    # 计算距离
    print("\n距离分析:")
    for i in range(min(5, len(detections))):
        det = detections[i]
        frame_idx = det['original_frame_idx']
        label_file = Path(f"dataset/labels/data24/{frame_idx}.txt")
        
        if label_file.exists():
            with open(label_file, 'r') as f:
                lines = [line.strip() for line in f if line.strip()]
            if lines:
                parts = lines[0].split()
                if len(parts) >= 5:
                    gt_x = float(parts[1])
                    gt_y = float(parts[2])
                    det_x = det['x_center']
                    det_y = det['y_center']
                    
                    distance = ((det_x - gt_x)**2 + (det_y - gt_y)**2)**0.5
                    print(f"  帧{frame_idx}: 距离={distance:.6f}")
    
    # 对比其他序列
    print("\n对比其他序列:")
    for seq_id in ['data01', 'data02', 'data19', 'data26']:
        det_file = Path(f"results/interval_5frame_detection/{seq_id}_detections.json")
        if det_file.exists():
            with open(det_file, 'r') as f:
                seq_detections = json.load(f)
            
            if seq_detections:
                det = seq_detections[0]
                frame_idx = det['original_frame_idx']
                label_file = Path(f"dataset/labels/{seq_id}/{frame_idx}.txt")
                
                if label_file.exists():
                    with open(label_file, 'r') as f:
                        lines = [line.strip() for line in f if line.strip()]
                    if lines:
                        parts = lines[0].split()
                        if len(parts) >= 5:
                            gt_x = float(parts[1])
                            gt_y = float(parts[2])
                            det_x = det['x_center']
                            det_y = det['y_center']
                            
                            distance = ((det_x - gt_x)**2 + (det_y - gt_y)**2)**0.5
                            print(f"  {seq_id}: 检测({det_x:.3f}, {det_y:.3f}) vs 真实({gt_x:.3f}, {gt_y:.3f}) = 距离{distance:.3f}")

if __name__ == "__main__":
    analyze_data24()
