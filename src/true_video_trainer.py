#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的视频微调训练器
使用Qwen2.5-VL的原生视频处理能力进行微调
"""

import os
import sys
import json
import torch
import logging
import random
from pathlib import Path
from typing import List, Dict, Any, Optional
from PIL import Image
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
import cv2
import numpy as np
from qwen_vl_utils import process_vision_info

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoDataset(Dataset):
    """视频数据集"""
    
    def __init__(self, data: List[Dict], processor, is_training: bool = True):
        self.data = data
        self.processor = processor
        self.is_training = is_training
        logger.info(f"创建视频数据集: {len(data)} 个样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        try:
            item = self.data[idx]
            messages = item["messages"]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )

            # 使用process_vision_info处理视频
            image_inputs, video_inputs = process_vision_info(messages)

            # 处理输入
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            
            # 移除batch维度
            for key in inputs:
                if inputs[key].dim() > 1:
                    inputs[key] = inputs[key].squeeze(0)
            
            # 设置labels用于计算loss
            inputs["labels"] = inputs["input_ids"].clone()
            
            return inputs
            
        except Exception as e:
            logger.error(f"处理样本 {idx} 时出错: {e}")
            # 返回一个空白样本
            return self.get_empty_sample()
    
    def load_video(self, video_path: str, max_frames: int = 16) -> Optional[str]:
        """返回视频路径，让processor直接处理"""
        try:
            if not os.path.exists(video_path):
                logger.warning(f"视频文件不存在: {video_path}")
                return None
            return video_path
        except Exception as e:
            logger.error(f"视频路径处理失败 {video_path}: {e}")
            return None
    
    def get_empty_sample(self):
        """获取空白样本"""
        empty_text = "这是一个空白样本。"
        # 创建一个简单的文本输入
        inputs = self.processor(
            text=[empty_text],
            padding=True,
            return_tensors="pt"
        )

        for key in inputs:
            if inputs[key].dim() > 1:
                inputs[key] = inputs[key].squeeze(0)

        inputs["labels"] = inputs["input_ids"].clone()
        return inputs

class TrueVideoTrainer:
    """真正的视频微调训练器"""
    
    def __init__(self, base_model_path: str):
        self.base_model_path = base_model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        
        self.model = None
        self.processor = None
    
    def load_model_and_processor(self):
        """加载模型和处理器"""
        logger.info("加载Qwen2.5-VL模型和处理器...")
        
        # 加载处理器
        self.processor = AutoProcessor.from_pretrained(self.base_model_path)
        
        # 加载模型
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            self.base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        logger.info("模型和处理器加载完成")
    
    def setup_video_lora_config(self):
        """设置视频LoRA配置"""
        logger.info("设置视频LoRA配置...")
        
        # 视频感知LoRA配置
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=32,  # 增加rank以更好地学习视频特征
            lora_alpha=64,
            lora_dropout=0.1,
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            bias="none",
        )
        
        # 应用LoRA
        self.model = get_peft_model(self.model, lora_config)
        self.model.print_trainable_parameters()
        
        logger.info("视频LoRA配置完成")
    
    def load_video_training_data(self) -> tuple:
        """加载视频训练数据"""
        train_file = "data/video_train_data.json"
        test_file = "data/video_test_data.json"
        
        if not os.path.exists(train_file) or not os.path.exists(test_file):
            logger.error("视频训练数据不存在，请先运行视频预处理")
            return None, None
        
        with open(train_file, 'r', encoding='utf-8') as f:
            train_data = json.load(f)
        
        with open(test_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        logger.info(f"加载训练数据: {len(train_data)} 个样本")
        logger.info(f"加载测试数据: {len(test_data)} 个样本")
        
        return train_data, test_data
    
    def create_video_datasets(self, train_data: List[Dict], test_data: List[Dict]) -> tuple:
        """创建视频数据集"""
        logger.info("创建视频数据集...")
        
        train_dataset = VideoDataset(train_data, self.processor, is_training=True)
        test_dataset = VideoDataset(test_data, self.processor, is_training=False)
        
        return train_dataset, test_dataset
    
    def setup_video_training_arguments(self, output_dir: str):
        """设置视频训练参数"""
        logger.info("设置视频训练参数...")
        
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=6,  # 视频学习需要更多epoch
            per_device_train_batch_size=1,  # 视频数据内存占用大
            per_device_eval_batch_size=1,
            gradient_accumulation_steps=16,  # 增加梯度累积
            learning_rate=2e-5,  # 较低的学习率学习视频特征
            weight_decay=0.01,
            logging_steps=10,
            eval_steps=50,
            save_steps=100,
            eval_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            warmup_steps=50,
            lr_scheduler_type="cosine",
            fp16=True,
            dataloader_pin_memory=False,
            remove_unused_columns=False,
            report_to=None,
            save_total_limit=3,
            max_grad_norm=1.0,  # 梯度裁剪
        )
        
        return training_args
    
    def train_video_model(self, output_dir: str = "output/true_video_lora", 
                         resume_from_checkpoint: str = None):
        """训练视频模型"""
        logger.info("开始真正的视频LoRA微调训练...")
        
        # 加载模型和处理器
        self.load_model_and_processor()
        
        # 设置视频LoRA
        self.setup_video_lora_config()
        
        # 加载视频数据
        train_data, test_data = self.load_video_training_data()
        if train_data is None:
            return False
        
        # 创建视频数据集
        train_dataset, test_dataset = self.create_video_datasets(train_data, test_data)
        
        # 设置训练参数
        training_args = self.setup_video_training_arguments(output_dir)
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=test_dataset,
            tokenizer=self.processor.tokenizer,
        )
        
        # 开始训练
        try:
            if resume_from_checkpoint:
                logger.info(f"从检查点恢复训练: {resume_from_checkpoint}")
                trainer.train(resume_from_checkpoint=resume_from_checkpoint)
            else:
                trainer.train()
            
            # 保存最终模型
            trainer.save_model()
            self.processor.save_pretrained(output_dir)
            
            logger.info("视频模型训练完成")
            return True
            
        except Exception as e:
            logger.error(f"训练过程中出错: {e}")
            return False

def main():
    """主函数"""
    print("=== 真正的视频微调训练系统 ===")
    print("🎯 使用Qwen2.5-VL原生视频处理能力")
    print("📹 基于真实视频文件进行微调")
    print("🔧 支持时序连续性学习")
    print("💡 完全的视频输入，不是抽帧序列")
    
    # 检查视频训练数据
    train_file = "data/video_train_data.json"
    test_file = "data/video_test_data.json"
    
    if not os.path.exists(train_file) or not os.path.exists(test_file):
        print("\n❌ 视频训练数据不存在")
        print("请先运行: python src/video_data_preprocessor.py")
        return False
    
    # 检查是否有检查点
    output_dir = "output/true_video_lora"
    checkpoint_dir = None
    if os.path.exists(output_dir):
        checkpoints = [d for d in os.listdir(output_dir) if d.startswith('checkpoint-')]
        if checkpoints:
            latest_checkpoint = max(checkpoints, key=lambda x: int(x.split('-')[1]))
            checkpoint_dir = os.path.join(output_dir, latest_checkpoint)
            print(f"🔄 发现检查点: {checkpoint_dir}")
    
    # 创建视频训练器
    base_model_path = "qwen2.5-vl-7b"
    trainer = TrueVideoTrainer(base_model_path)
    
    # 开始视频训练
    success = trainer.train_video_model(output_dir, resume_from_checkpoint=checkpoint_dir)
    
    if success:
        print(f"\n🎉 真正的视频微调训练完成！")
        print(f"📁 视频模型保存到: {output_dir}")
        print(f"💡 这个模型学习了完整的视频时序信息")
        print(f"🎯 支持真正的视频输入检测")
        print(f"🚀 使用Qwen2.5-VL原生视频处理能力")
    else:
        print(f"\n❌ 视频训练失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
