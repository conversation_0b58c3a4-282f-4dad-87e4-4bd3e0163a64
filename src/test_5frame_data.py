#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试5帧视频数据和模型加载
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_loading():
    """测试数据加载"""
    print("=== 测试5帧视频数据加载 ===")
    
    train_file = "data/interval_5frame_video_train_data.json"
    test_file = "data/interval_5frame_video_test_data.json"
    
    if not os.path.exists(train_file):
        print(f"❌ 训练数据不存在: {train_file}")
        return False
    
    if not os.path.exists(test_file):
        print(f"❌ 测试数据不存在: {test_file}")
        return False
    
    # 加载数据
    with open(train_file, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    with open(test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    print(f"✅ 训练数据: {len(train_data)} 个样本")
    print(f"✅ 测试数据: {len(test_data)} 个样本")
    
    # 检查第一个样本
    if train_data:
        sample = train_data[0]
        print(f"✅ 样本格式检查:")
        print(f"   - 消息数量: {len(sample['messages'])}")
        
        user_content = sample['messages'][0]['content']
        print(f"   - 用户内容数量: {len(user_content)}")
        
        for i, content in enumerate(user_content):
            print(f"   - 内容{i}: {content['type']}")
            if content['type'] == 'video':
                video_path = content['video']
                print(f"     视频路径: {video_path}")
                print(f"     文件存在: {os.path.exists(video_path)}")
    
    return True

def test_model_loading():
    """测试模型加载"""
    print("\n=== 测试模型加载 ===")
    
    base_model_path = "qwen2.5-vl-7b"
    
    try:
        # 加载处理器
        processor = AutoProcessor.from_pretrained(base_model_path)
        print("✅ 处理器加载成功")
        
        # 加载模型
        model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        print("✅ 模型加载成功")
        
        return True, processor, model
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False, None, None

def test_video_processing():
    """测试视频处理"""
    print("\n=== 测试视频处理 ===")
    
    # 加载一个样本
    train_file = "data/interval_5frame_video_train_data.json"
    with open(train_file, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    if not train_data:
        print("❌ 没有训练数据")
        return False
    
    sample = train_data[0]
    messages = sample["messages"]
    
    # 加载模型
    success, processor, model = test_model_loading()
    if not success:
        return False
    
    try:
        # 应用聊天模板
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        print("✅ 聊天模板应用成功")
        
        # 使用process_vision_info处理视频
        image_inputs, video_inputs = process_vision_info(messages)
        print(f"✅ 视频处理成功: {len(video_inputs) if video_inputs else 0} 个视频")
        
        # 处理输入
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        print("✅ 输入处理成功")
        print(f"   - input_ids shape: {inputs['input_ids'].shape}")
        print(f"   - attention_mask shape: {inputs['attention_mask'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 视频处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_files():
    """测试视频文件"""
    print("\n=== 测试视频文件 ===")
    
    video_dir = Path("dataset/interval_5frame_videos")
    if not video_dir.exists():
        print(f"❌ 视频目录不存在: {video_dir}")
        return False
    
    video_count = 0
    for seq_dir in video_dir.iterdir():
        if seq_dir.is_dir():
            videos = list(seq_dir.glob("*.mp4"))
            video_count += len(videos)
            print(f"   {seq_dir.name}: {len(videos)} 个视频")
    
    print(f"✅ 总共 {video_count} 个视频文件")
    return True

def main():
    """主函数"""
    print("=== 5帧视频数据和模型测试 ===")
    print("🎯 验证数据、模型和视频处理流程")
    
    # 测试数据加载
    if not test_data_loading():
        return False
    
    # 测试视频文件
    if not test_video_files():
        return False
    
    # 测试模型加载
    success, _, _ = test_model_loading()
    if not success:
        return False
    
    # 测试视频处理
    if not test_video_processing():
        return False
    
    print("\n🎉 所有测试通过！")
    print("✅ 数据格式正确")
    print("✅ 视频文件存在")
    print("✅ 模型加载成功")
    print("✅ 视频处理正常")
    print("🚀 可以开始训练了！")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
