#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据增强器 - 大幅扩增训练数据
优化点1: 从110个样本扩增到500+样本
"""

import os
import json
import cv2
import numpy as np
import random
from pathlib import Path
from typing import List, Dict, Any
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataAugmenter:
    """数据增强器"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
    
    def create_sliding_window_videos(self, seq_id: str, output_dir: Path, 
                                   interval: int = 10, frames_per_video: int = 5,
                                   stride: int = 2) -> List[Dict]:
        """滑动窗口创建更多视频片段"""
        logger.info(f"为序列 {seq_id} 创建滑动窗口视频...")
        
        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        
        # 获取图像文件
        if seq_info['naming'] == 'simple':
            image_files = list(images_dir.glob(f"*{seq_info['format']}"))
            image_files = sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(images_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                import re
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            image_files = sorted(image_files, key=complex_sort_key)
        
        # 隔10取1
        selected_indices = list(range(0, len(image_files), interval))
        
        video_files = []
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 滑动窗口：每stride帧开始一个新的5帧视频
        for start_idx in range(0, len(selected_indices) - frames_per_video + 1, stride):
            end_idx = start_idx + frames_per_video
            frame_indices = [selected_indices[i] for i in range(start_idx, end_idx)]
            
            # 创建视频文件
            video_filename = f"{seq_id}_sliding_{start_idx:04d}_{end_idx:04d}.mp4"
            video_path = output_dir / video_filename
            
            # 获取对应的图像文件
            segment_files = [image_files[idx] for idx in frame_indices]
            
            # 创建视频
            first_img = cv2.imread(str(segment_files[0]))
            if first_img is None:
                continue
            height, width = first_img.shape[:2]
            
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(str(video_path), fourcc, 10, (width, height))
            
            # 收集标注
            segment_annotations = {}
            for video_frame_idx, img_file in enumerate(segment_files):
                img = cv2.imread(str(img_file))
                if img is not None:
                    video_writer.write(img)
                
                # 加载标注
                frame_id = img_file.stem
                annotations = self.load_frame_annotation(seq_id, frame_id, seq_info['size'])
                if annotations:
                    segment_annotations[video_frame_idx] = {
                        'frame_id': frame_id,
                        'original_frame_idx': frame_indices[video_frame_idx],
                        'annotations': annotations
                    }
            
            video_writer.release()
            
            if segment_annotations:
                video_info = {
                    'video_path': str(video_path),
                    'seq_id': seq_id,
                    'start_frame_idx': frame_indices[0],
                    'end_frame_idx': frame_indices[-1],
                    'annotations': segment_annotations,
                    'augmentation_type': 'sliding_window'
                }
                video_files.append(video_info)
            else:
                video_path.unlink()
        
        logger.info(f"序列 {seq_id}: 滑动窗口创建了 {len(video_files)} 个视频")
        return video_files
    
    def create_augmented_videos(self, seq_id: str, output_dir: Path) -> List[Dict]:
        """创建视觉增强的视频"""
        logger.info(f"为序列 {seq_id} 创建视觉增强视频...")
        
        # 先获取原始视频
        original_videos = self.create_sliding_window_videos(seq_id, output_dir / "original")
        
        augmented_videos = []
        
        # 对每个原始视频进行视觉增强
        for video_info in original_videos[:20]:  # 限制数量避免过多
            original_path = video_info['video_path']
            
            # 亮度增强
            bright_path = self.apply_brightness_augmentation(original_path, output_dir / "bright", 1.2)
            if bright_path:
                bright_info = video_info.copy()
                bright_info['video_path'] = bright_path
                bright_info['augmentation_type'] = 'brightness'
                augmented_videos.append(bright_info)
            
            # 对比度增强
            contrast_path = self.apply_contrast_augmentation(original_path, output_dir / "contrast", 1.3)
            if contrast_path:
                contrast_info = video_info.copy()
                contrast_info['video_path'] = contrast_path
                contrast_info['augmentation_type'] = 'contrast'
                augmented_videos.append(contrast_info)
            
            # 噪声增强
            noise_path = self.apply_noise_augmentation(original_path, output_dir / "noise")
            if noise_path:
                noise_info = video_info.copy()
                noise_info['video_path'] = noise_path
                noise_info['augmentation_type'] = 'noise'
                augmented_videos.append(noise_info)
        
        logger.info(f"序列 {seq_id}: 视觉增强创建了 {len(augmented_videos)} 个视频")
        return original_videos + augmented_videos
    
    def apply_brightness_augmentation(self, video_path: str, output_dir: Path, factor: float) -> str:
        """应用亮度增强"""
        output_dir.mkdir(parents=True, exist_ok=True)
        output_path = output_dir / f"bright_{Path(video_path).name}"
        
        try:
            cap = cv2.VideoCapture(video_path)
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            
            # 获取视频属性
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            writer = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 亮度增强
                bright_frame = cv2.convertScaleAbs(frame, alpha=factor, beta=0)
                writer.write(bright_frame)
            
            cap.release()
            writer.release()
            return str(output_path)
        except:
            return None
    
    def apply_contrast_augmentation(self, video_path: str, output_dir: Path, factor: float) -> str:
        """应用对比度增强"""
        output_dir.mkdir(parents=True, exist_ok=True)
        output_path = output_dir / f"contrast_{Path(video_path).name}"
        
        try:
            cap = cv2.VideoCapture(video_path)
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            writer = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 对比度增强
                contrast_frame = cv2.convertScaleAbs(frame, alpha=factor, beta=0)
                writer.write(contrast_frame)
            
            cap.release()
            writer.release()
            return str(output_path)
        except:
            return None
    
    def apply_noise_augmentation(self, video_path: str, output_dir: Path) -> str:
        """应用噪声增强"""
        output_dir.mkdir(parents=True, exist_ok=True)
        output_path = output_dir / f"noise_{Path(video_path).name}"
        
        try:
            cap = cv2.VideoCapture(video_path)
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            writer = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 添加高斯噪声
                noise = np.random.normal(0, 5, frame.shape).astype(np.uint8)
                noisy_frame = cv2.add(frame, noise)
                writer.write(noisy_frame)
            
            cap.release()
            writer.release()
            return str(output_path)
        except:
            return None
    
    def load_frame_annotation(self, seq_id: str, frame_id: str, image_size: tuple) -> List[Dict]:
        """加载帧标注"""
        annotation_file = Path(f"dataset/labels/{seq_id}/{frame_id}.txt")
        if not annotation_file.exists():
            return []
        
        annotations = []
        with open(annotation_file, 'r') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) != 5:
                    continue
                
                class_id = int(parts[0])
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                
                abs_w = width * image_size[0]
                abs_h = height * image_size[1]
                
                if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                    # 加载类别映射
                    with open('dataset/class.json', 'r', encoding='utf-8') as cf:
                        class_map = json.load(cf)
                    
                    class_name = class_map.get(str(class_id), 'unknown')
                    annotations.append({
                        'class_id': class_id,
                        'class_name': class_name,
                        'x_center': x_center,
                        'y_center': y_center,
                        'width': width,
                        'height': height,
                        'frame_id': frame_id
                    })
        
        return annotations
    
    def create_augmented_training_data(self) -> tuple:
        """创建增强的训练数据"""
        logger.info("开始创建大规模增强训练数据...")
        
        output_base_dir = Path("dataset/augmented_videos")
        all_video_data = []
        
        # 为每个序列创建增强数据
        for seq_id in self.sequences.keys():
            seq_output_dir = output_base_dir / seq_id
            augmented_videos = self.create_augmented_videos(seq_id, seq_output_dir)
            all_video_data.extend(augmented_videos)
        
        # 创建训练样本
        all_samples = []
        for video_info in all_video_data:
            annotations_text = self.format_video_annotations(video_info['annotations'])
            
            sample = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "video",
                                "video": video_info['video_path']
                            },
                            {
                                "type": "text",
                                "text": f"""你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。

视频信息:
- 序列: {video_info['seq_id']}
- 增强类型: {video_info.get('augmentation_type', 'original')}
- 采样方式: 隔10帧取1帧
- 帧数: 5帧

请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
video_frame_idx class_name x_center y_center width height confidence

注意：
1. video_frame_idx为视频中的帧索引（0-4）
2. 坐标为相对坐标（0-1之间）
3. 只检测尺寸在3-80像素之间的弱小目标
4. 利用5帧视频的时序信息提高检测精度
5. confidence为检测置信度（0-1之间）"""
                            }
                        ]
                    },
                    {
                        "role": "assistant",
                        "content": annotations_text
                    }
                ]
            }
            all_samples.append(sample)
        
        # 随机打乱并划分
        random.shuffle(all_samples)
        split_idx = int(len(all_samples) * 0.8)
        
        train_data = all_samples[:split_idx]
        test_data = all_samples[split_idx:]
        
        logger.info(f"增强后训练数据: {len(train_data)} 个样本")
        logger.info(f"增强后测试数据: {len(test_data)} 个样本")
        
        return train_data, test_data
    
    def format_video_annotations(self, annotations: Dict[int, Dict]) -> str:
        """格式化视频标注为文本"""
        result_lines = []
        
        for video_frame_idx, frame_data in annotations.items():
            for ann in frame_data['annotations']:
                line = f"{video_frame_idx} {ann['class_name']} {ann['x_center']:.6f} {ann['y_center']:.6f} {ann['width']:.6f} {ann['height']:.6f} 1.0"
                result_lines.append(line)
        
        return '\n'.join(result_lines)

def main():
    """主函数"""
    print("=== 🚀 数据增强器 - 大幅提升训练数据量 ===")
    print("🎯 优化点1: 从110个样本扩增到500+样本")
    print("📈 预期效果: F1分数提升5-10%")
    
    augmenter = DataAugmenter()
    train_data, test_data = augmenter.create_augmented_training_data()
    
    # 保存增强数据
    os.makedirs("data", exist_ok=True)
    
    with open("data/augmented_train_data.json", 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    with open("data/augmented_test_data.json", 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 数据增强完成！")
    print(f"📊 原始训练样本: 110个")
    print(f"📊 增强后训练样本: {len(train_data)}个 ({len(train_data)/110:.1f}倍增长)")
    print(f"📊 增强后测试样本: {len(test_data)}个")
    print(f"💾 保存到: data/augmented_train_data.json")
    print(f"🚀 准备开始增强训练！")

if __name__ == "__main__":
    main()
