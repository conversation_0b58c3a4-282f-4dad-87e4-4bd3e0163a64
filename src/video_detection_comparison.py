#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频检测方法对比分析
对比抽帧方式和真正视频方式的检测效果
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoDetectionComparison:
    """视频检测方法对比分析"""
    
    def __init__(self):
        pass
    
    def load_evaluation_results(self, file_path: str) -> Dict[str, Any]:
        """加载评估结果"""
        if not os.path.exists(file_path):
            logger.warning(f"评估文件不存在: {file_path}")
            return {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def compare_methods(self) -> Dict[str, Any]:
        """对比不同方法"""
        logger.info("开始对比不同视频检测方法...")
        
        # 加载不同方法的评估结果
        methods = {
            'interval_frame': {
                'name': '抽帧序列方式 (Interval Frame)',
                'description': '每隔10帧取1帧组成训练数据，时序插值检测',
                'file': 'results/temporal_aware_evaluation.json'
            },
            'true_video': {
                'name': '真正视频方式 (True Video)',
                'description': '使用Qwen2.5-VL原生视频处理能力',
                'file': 'results/true_video_evaluation.json'
            }
        }
        
        comparison_results = {}
        
        for method_key, method_info in methods.items():
            results = self.load_evaluation_results(method_info['file'])
            
            if results:
                comparison_results[method_key] = {
                    'name': method_info['name'],
                    'description': method_info['description'],
                    'overall_performance': results.get('overall', {}),
                    'individual_results': results.get('individual_results', [])
                }
            else:
                comparison_results[method_key] = {
                    'name': method_info['name'],
                    'description': method_info['description'],
                    'overall_performance': {},
                    'individual_results': []
                }
        
        return comparison_results
    
    def analyze_performance_differences(self, comparison_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析性能差异"""
        analysis = {
            'summary': {},
            'detailed_analysis': {},
            'recommendations': []
        }
        
        # 提取性能指标
        methods_performance = {}
        for method_key, method_data in comparison_results.items():
            overall = method_data.get('overall_performance', {})
            methods_performance[method_key] = {
                'name': method_data['name'],
                'f1': overall.get('f1', 0),
                'precision': overall.get('precision', 0),
                'recall': overall.get('recall', 0),
                'total_gt': overall.get('total_gt', 0),
                'total_pred': overall.get('total_pred', 0),
                'total_tp': overall.get('total_tp', 0)
            }
        
        # 找出最佳方法
        best_method = None
        best_f1 = 0
        for method_key, perf in methods_performance.items():
            if perf['f1'] > best_f1:
                best_f1 = perf['f1']
                best_method = method_key
        
        analysis['summary'] = {
            'best_method': best_method,
            'best_f1': best_f1,
            'methods_performance': methods_performance
        }
        
        # 详细分析
        if 'interval_frame' in methods_performance and 'true_video' in methods_performance:
            interval_perf = methods_performance['interval_frame']
            video_perf = methods_performance['true_video']
            
            analysis['detailed_analysis'] = {
                'f1_difference': interval_perf['f1'] - video_perf['f1'],
                'precision_difference': interval_perf['precision'] - video_perf['precision'],
                'recall_difference': interval_perf['recall'] - video_perf['recall'],
                'detection_count_difference': interval_perf['total_pred'] - video_perf['total_pred']
            }
            
            # 生成建议
            if interval_perf['f1'] > video_perf['f1']:
                analysis['recommendations'].append(
                    "抽帧序列方式表现更好，建议继续优化这种方法"
                )
                analysis['recommendations'].append(
                    "真正视频方式可能需要更多的训练数据或调整超参数"
                )
            else:
                analysis['recommendations'].append(
                    "真正视频方式表现更好，建议深入优化视频处理流程"
                )
            
            if video_perf['total_pred'] < interval_perf['total_pred']:
                analysis['recommendations'].append(
                    "真正视频方式检测到的目标较少，可能需要调整检测阈值或增强数据"
                )
        
        return analysis
    
    def generate_report(self, comparison_results: Dict[str, Any], analysis: Dict[str, Any]) -> str:
        """生成对比报告"""
        report_lines = []
        
        report_lines.append("# 视频检测方法对比分析报告")
        report_lines.append("")
        report_lines.append("## 方法概述")
        report_lines.append("")
        
        for method_key, method_data in comparison_results.items():
            report_lines.append(f"### {method_data['name']}")
            report_lines.append(f"**描述**: {method_data['description']}")
            
            overall = method_data.get('overall_performance', {})
            if overall:
                report_lines.append(f"**总体性能**:")
                report_lines.append(f"- F1分数: {overall.get('f1', 0):.3f}")
                report_lines.append(f"- 精确度: {overall.get('precision', 0):.3f}")
                report_lines.append(f"- 召回率: {overall.get('recall', 0):.3f}")
                report_lines.append(f"- 检测目标数: {overall.get('total_pred', 0)}")
                report_lines.append(f"- 真实目标数: {overall.get('total_gt', 0)}")
            else:
                report_lines.append("**总体性能**: 暂无数据")
            
            report_lines.append("")
        
        report_lines.append("## 性能对比")
        report_lines.append("")
        
        summary = analysis.get('summary', {})
        if summary.get('best_method'):
            best_method_name = comparison_results[summary['best_method']]['name']
            report_lines.append(f"**最佳方法**: {best_method_name} (F1={summary['best_f1']:.3f})")
        
        detailed = analysis.get('detailed_analysis', {})
        if detailed:
            report_lines.append("")
            report_lines.append("**详细对比**:")
            report_lines.append(f"- F1分数差异: {detailed.get('f1_difference', 0):.3f}")
            report_lines.append(f"- 精确度差异: {detailed.get('precision_difference', 0):.3f}")
            report_lines.append(f"- 召回率差异: {detailed.get('recall_difference', 0):.3f}")
            report_lines.append(f"- 检测数量差异: {detailed.get('detection_count_difference', 0)}")
        
        report_lines.append("")
        report_lines.append("## 建议和结论")
        report_lines.append("")
        
        recommendations = analysis.get('recommendations', [])
        for i, rec in enumerate(recommendations, 1):
            report_lines.append(f"{i}. {rec}")
        
        if not recommendations:
            report_lines.append("暂无具体建议，需要更多数据进行分析。")
        
        return "\n".join(report_lines)
    
    def save_comparison_results(self, comparison_results: Dict[str, Any], 
                              analysis: Dict[str, Any], 
                              output_dir: str = "results"):
        """保存对比结果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存JSON格式结果
        full_results = {
            'comparison_results': comparison_results,
            'analysis': analysis,
            'timestamp': str(Path().cwd())
        }
        
        json_file = output_path / "video_detection_comparison.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(full_results, f, ensure_ascii=False, indent=2)
        
        # 生成并保存报告
        report = self.generate_report(comparison_results, analysis)
        report_file = output_path / "video_detection_comparison_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"对比结果保存到: {json_file}")
        logger.info(f"对比报告保存到: {report_file}")

def main():
    """主函数"""
    print("=== 视频检测方法对比分析 ===")
    print("🔍 对比抽帧方式 vs 真正视频方式")
    print("📊 分析性能差异和优劣势")
    print("💡 提供优化建议")
    
    # 创建对比分析器
    comparator = VideoDetectionComparison()
    
    # 进行对比分析
    comparison_results = comparator.compare_methods()
    analysis = comparator.analyze_performance_differences(comparison_results)
    
    # 保存结果
    comparator.save_comparison_results(comparison_results, analysis)
    
    # 显示结果
    print(f"\n📊 对比分析结果:")
    
    summary = analysis.get('summary', {})
    if summary.get('best_method'):
        best_method_data = comparison_results[summary['best_method']]
        print(f"🏆 最佳方法: {best_method_data['name']}")
        print(f"📈 最佳F1分数: {summary['best_f1']:.3f}")
    
    print(f"\n📋 各方法性能:")
    methods_perf = summary.get('methods_performance', {})
    for method_key, perf in methods_perf.items():
        print(f"   {perf['name']}: F1={perf['f1']:.3f} (P={perf['precision']:.3f}, R={perf['recall']:.3f})")
    
    print(f"\n💡 主要建议:")
    recommendations = analysis.get('recommendations', [])
    for i, rec in enumerate(recommendations[:3], 1):  # 显示前3个建议
        print(f"   {i}. {rec}")
    
    print(f"\n💾 详细结果保存到: results/video_detection_comparison.json")
    print(f"📄 对比报告保存到: results/video_detection_comparison_report.md")

if __name__ == "__main__":
    main()
