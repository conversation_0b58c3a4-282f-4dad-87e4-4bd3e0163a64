#!/usr/bin/env python3
"""
测试模型是否真的学会了检测，还是只是在背诵
"""

import torch
from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
from qwen_vl_utils import process_vision_info
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_model_understanding():
    print("=== 测试模型是否真的学会了检测 ===")
    
    # 加载模型
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"使用设备: {device}")
    
    model_path = "output/interval_5frame_video_lora"
    base_model = "qwen2.5-vl-7b"
    
    logger.info(f"加载模型: {model_path}")
    model = Qwen2VLForConditionalGeneration.from_pretrained(
        base_model,
        torch_dtype=torch.bfloat16,
        device_map="auto"
    )
    
    processor = AutoProcessor.from_pretrained(base_model)
    
    # 加载LoRA权重
    from peft import PeftModel
    model = PeftModel.from_pretrained(model, model_path)
    model.eval()
    
    # 测试1: 空白提示
    print("\n=== 测试1: 空白提示 ===")
    test_empty_prompt(model, processor)
    
    # 测试2: 不同的文本提示
    print("\n=== 测试2: 不同文本提示 ===")
    test_different_prompts(model, processor)
    
    # 测试3: 无视频输入
    print("\n=== 测试3: 无视频输入 ===")
    test_no_video(model, processor)

def test_empty_prompt(model, processor):
    """测试空白提示"""
    messages = [
        {
            "role": "user", 
            "content": [
                {
                    "type": "text",
                    "text": ""
                }
            ]
        }
    ]
    
    try:
        text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        image_inputs, video_inputs = process_vision_info(messages)
        
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        
        inputs = inputs.to(model.device)
        
        with torch.no_grad():
            generated_ids = model.generate(**inputs, max_new_tokens=100, temperature=0.1)
        
        generated_ids_trimmed = [
            out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )[0]
        
        print(f"空白提示输出: '{output_text}'")
        
    except Exception as e:
        print(f"空白提示测试失败: {e}")

def test_different_prompts(model, processor):
    """测试不同的文本提示"""
    test_prompts = [
        "你好",
        "今天天气怎么样？",
        "请检测图像中的目标",
        "1+1等于几？",
        "请输出: 0 0 drone 0.5 0.5 0.1 0.1 1.0"
    ]
    
    for prompt in test_prompts:
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text", 
                        "text": prompt
                    }
                ]
            }
        ]
        
        try:
            text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            image_inputs, video_inputs = process_vision_info(messages)
            
            inputs = processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            
            inputs = inputs.to(model.device)
            
            with torch.no_grad():
                generated_ids = model.generate(**inputs, max_new_tokens=50, temperature=0.1)
            
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            print(f"提示: '{prompt}' -> 输出: '{output_text}'")
            
        except Exception as e:
            print(f"提示测试失败 '{prompt}': {e}")

def test_no_video(model, processor):
    """测试无视频输入时的行为"""
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "请检测视频中的弱小目标，输出格式：video_idx frame_idx class_name x y w h confidence"
                }
            ]
        }
    ]
    
    try:
        text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        image_inputs, video_inputs = process_vision_info(messages)
        
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        
        inputs = inputs.to(model.device)
        
        with torch.no_grad():
            generated_ids = model.generate(**inputs, max_new_tokens=100, temperature=0.1)
        
        generated_ids_trimmed = [
            out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )[0]
        
        print(f"无视频检测提示输出: '{output_text}'")
        
    except Exception as e:
        print(f"无视频测试失败: {e}")

if __name__ == "__main__":
    test_model_understanding()
