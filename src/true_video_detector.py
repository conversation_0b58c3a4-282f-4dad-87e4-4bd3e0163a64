#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的视频检测器
使用微调后的视频模型进行完整视频检测
"""

import os
import sys
import json
import torch
import logging
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
import re
from qwen_vl_utils import process_vision_info

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrueVideoDetector:
    """真正的视频检测器"""
    
    def __init__(self, base_model_path: str, lora_model_path: str):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        logger.info(f"视频LoRA模型: {lora_model_path}")
        
        # 加载基础模型
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # 加载视频LoRA微调模型
        logger.info("加载视频LoRA模型...")
        self.model = PeftModel.from_pretrained(self.model, lora_model_path)
        logger.info("真正的视频检测器加载完成")
        
        # 序列信息
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def create_video_from_images(self, seq_id: str, start_frame: int = 0, 
                               num_frames: int = 30, fps: int = 10) -> str:
        """从图像序列创建临时视频文件"""
        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        
        # 获取图像文件
        if seq_info['naming'] == 'simple':
            image_files = list(images_dir.glob(f"*{seq_info['format']}"))
            image_files = sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(images_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            image_files = sorted(image_files, key=complex_sort_key)
        
        # 选择帧范围
        end_frame = min(start_frame + num_frames, len(image_files))
        selected_files = image_files[start_frame:end_frame]
        
        if len(selected_files) == 0:
            return None
        
        # 创建临时视频文件
        temp_video_path = f"temp_video_{seq_id}_{start_frame}_{end_frame}.mp4"
        
        # 获取图像尺寸
        first_img = cv2.imread(str(selected_files[0]))
        height, width = first_img.shape[:2]
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))
        
        for img_file in selected_files:
            img = cv2.imread(str(img_file))
            if img is not None:
                video_writer.write(img)
        
        video_writer.release()
        return temp_video_path
    
    def load_video_for_detection(self, video_path: str) -> str:
        """返回视频路径，让processor直接处理"""
        try:
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                return None
            return video_path
        except Exception as e:
            logger.error(f"视频路径处理失败 {video_path}: {e}")
            return None
    
    def video_detection(self, video_path: str, seq_id: str) -> List[Dict]:
        """视频检测"""
        try:
            # 获取视频路径
            video_file = self.load_video_for_detection(video_path)
            if video_file is None:
                return []
            
            # 构建检测消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video",
                            "video": video_path
                        },
                        {
                            "type": "text",
                            "text": f"""你是专业的红外弱小目标视频检测专家。请检测这个视频中的弱小目标。

视频信息:
- 序列: {seq_id}

请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
frame_id class_name x_center y_center width height confidence

注意：
1. 坐标为相对坐标（0-1之间）
2. 只检测尺寸在3-80像素之间的弱小目标
3. 利用视频的时序信息提高检测精度
4. confidence为检测置信度（0-1之间）
5. frame_id从0开始编号"""
                        }
                    ]
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )

            # 使用process_vision_info处理视频
            image_inputs, video_inputs = process_vision_info(messages)

            # 处理输入
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            inputs = inputs.to(self.device)
            
            # 生成检测结果
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=512,
                    do_sample=False,
                    temperature=0.1,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            # 解码结果
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            # 解析检测结果
            detections = self.parse_detection_output(output_text, seq_id)
            
            return detections
            
        except Exception as e:
            logger.error(f"视频检测失败: {e}")
            return []
    
    def parse_detection_output(self, output_text: str, seq_id: str) -> List[Dict]:
        """解析检测输出"""
        detections = []
        lines = output_text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 解析检测结果行
            parts = line.split()
            if len(parts) >= 6:
                try:
                    frame_id = parts[0]
                    class_name = parts[1]
                    x_center = float(parts[2])
                    y_center = float(parts[3])
                    width = float(parts[4])
                    height = float(parts[5])
                    confidence = float(parts[6]) if len(parts) > 6 else 1.0
                    
                    # 验证坐标范围
                    if 0 <= x_center <= 1 and 0 <= y_center <= 1 and 0 < width <= 1 and 0 < height <= 1:
                        # 验证是否为弱小目标
                        seq_info = self.sequences[seq_id]
                        abs_w = width * seq_info['size'][0]
                        abs_h = height * seq_info['size'][1]
                        
                        if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                            detections.append({
                                'frame_id': frame_id,
                                'class_name': class_name,
                                'x_center': x_center,
                                'y_center': y_center,
                                'width': width,
                                'height': height,
                                'confidence': confidence,
                                'abs_w': abs_w,
                                'abs_h': abs_h
                            })
                
                except (ValueError, IndexError) as e:
                    continue
        
        return detections
    
    def detect_sequence_videos(self, seq_id: str, video_length: int = 30, 
                             overlap: int = 10) -> List[Dict]:
        """检测序列的多个视频片段"""
        logger.info(f"开始检测序列 {seq_id}...")
        
        all_detections = []
        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        
        # 获取总帧数
        if seq_info['naming'] == 'simple':
            image_files = list(images_dir.glob(f"*{seq_info['format']}"))
            total_frames = len(image_files)
        else:
            image_files = list(images_dir.glob(f"*{seq_info['format']}"))
            total_frames = len(image_files)
        
        # 分段检测
        start_frame = 0
        segment_id = 0
        
        while start_frame < total_frames:
            # 创建视频片段
            temp_video_path = self.create_video_from_images(
                seq_id, start_frame, video_length
            )
            
            if temp_video_path:
                logger.info(f"检测视频片段 {segment_id}: 帧 {start_frame}-{start_frame+video_length}")
                
                # 进行检测
                detections = self.video_detection(temp_video_path, seq_id)
                
                # 调整frame_id
                for det in detections:
                    try:
                        frame_idx = int(det['frame_id'])
                        det['frame_id'] = str(start_frame + frame_idx)
                        det['segment_id'] = segment_id
                    except:
                        pass
                
                all_detections.extend(detections)
                
                # 清理临时文件
                try:
                    os.remove(temp_video_path)
                except:
                    pass
            
            # 移动到下一个片段
            start_frame += video_length - overlap
            segment_id += 1
        
        logger.info(f"序列 {seq_id} 检测完成，共检测到 {len(all_detections)} 个目标")
        return all_detections
    
    def save_detection_results(self, seq_id: str, detections: List[Dict], 
                             output_dir: str = "results/true_video_detection"):
        """保存检测结果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存JSON格式结果
        json_file = output_path / f"{seq_id}_detections.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(detections, f, ensure_ascii=False, indent=2)
        
        # 保存YOLO格式结果
        yolo_dir = output_path / "yolo_format" / seq_id
        yolo_dir.mkdir(parents=True, exist_ok=True)
        
        # 按帧组织检测结果
        frame_detections = {}
        for det in detections:
            frame_id = det['frame_id']
            if frame_id not in frame_detections:
                frame_detections[frame_id] = []
            frame_detections[frame_id].append(det)
        
        # 保存每帧的检测结果
        for frame_id, frame_dets in frame_detections.items():
            yolo_file = yolo_dir / f"{frame_id}.txt"
            with open(yolo_file, 'w') as f:
                for det in frame_dets:
                    # 获取类别ID
                    class_id = None
                    for cid, cname in self.class_map.items():
                        if cname == det['class_name']:
                            class_id = cid
                            break
                    
                    if class_id is not None:
                        line = f"{class_id} {det['x_center']:.6f} {det['y_center']:.6f} {det['width']:.6f} {det['height']:.6f}\n"
                        f.write(line)
        
        logger.info(f"检测结果保存到: {output_path}")

def main():
    """主函数"""
    print("=== 真正的视频检测器 ===")
    print("🎯 使用微调后的视频模型")
    print("📹 基于完整视频进行检测")
    print("🔧 利用视频时序连续性")
    print("💡 真正的视频输入检测")
    
    # 检查模型
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/true_video_lora"
    
    if not os.path.exists(lora_model_path):
        print(f"\n❌ 视频LoRA模型不存在: {lora_model_path}")
        print("请先运行: python src/true_video_trainer.py")
        return False
    
    # 创建检测器
    detector = TrueVideoDetector(base_model_path, lora_model_path)
    
    # 检测所有序列
    test_sequences = ['data02', 'data19']  # 先测试这两个序列
    
    for seq_id in test_sequences:
        print(f"\n🔍 检测序列: {seq_id}")
        detections = detector.detect_sequence_videos(seq_id)
        detector.save_detection_results(seq_id, detections)
        print(f"✅ {seq_id}: 检测到 {len(detections)} 个目标")
    
    print(f"\n🎉 真正的视频检测完成！")
    print(f"📁 结果保存到: results/true_video_detection/")

if __name__ == "__main__":
    main()
