#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
隔10取1，五帧一个的视频数据预处理器
按照用户要求：隔10帧取1帧，每5帧组成一个视频片段
"""

import os
import sys
import json
import cv2
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Interval5FrameVideoPreprocessor:
    """隔10取1，五帧一个的视频数据预处理器"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data24': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data25': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def load_frame_annotation(self, seq_id: str, frame_id: str, image_size: Tuple[int, int]) -> List[Dict]:
        """加载帧标注"""
        annotation_file = Path(f"dataset/labels/{seq_id}/{frame_id}.txt")
        if not annotation_file.exists():
            return []
        
        annotations = []
        with open(annotation_file, 'r') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) != 5:
                    continue
                
                class_id = int(parts[0])
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                
                # 转换为绝对坐标
                abs_x = x_center * image_size[0]
                abs_y = y_center * image_size[1]
                abs_w = width * image_size[0]
                abs_h = height * image_size[1]
                
                # 检查是否为弱小目标（3-80像素）
                if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                    class_name = self.class_map.get(str(class_id), 'unknown')
                    annotations.append({
                        'class_id': class_id,
                        'class_name': class_name,
                        'x_center': x_center,
                        'y_center': y_center,
                        'width': width,
                        'height': height,
                        'abs_x': abs_x,
                        'abs_y': abs_y,
                        'abs_w': abs_w,
                        'abs_h': abs_h,
                        'frame_id': frame_id
                    })
        
        return annotations
    
    def create_interval_5frame_videos(self, seq_id: str, output_dir: Path,
                                    interval: int = 10, frames_per_video: int = 5,
                                    fps: int = 10) -> List[Dict]:
        """创建隔10取1，五帧一个的视频片段"""
        logger.info(f"处理序列 {seq_id} - 隔{interval}取1，每{frames_per_video}帧一个视频...")
        
        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        all_image_files = self.get_image_files(images_dir, seq_info)
        
        if len(all_image_files) == 0:
            logger.warning(f"序列 {seq_id} 没有图像文件")
            return []
        
        # 隔10取1：从第0帧开始，每隔10帧取1帧
        selected_indices = list(range(0, len(all_image_files), interval))
        selected_image_files = [all_image_files[i] for i in selected_indices]
        
        logger.info(f"序列 {seq_id}: 总帧数 {len(all_image_files)}, 隔{interval}取1后 {len(selected_image_files)} 帧")
        
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 每5帧组成一个视频片段
        video_files = []
        
        for start_idx in range(0, len(selected_image_files), frames_per_video):
            end_idx = min(start_idx + frames_per_video, len(selected_image_files))
            segment_files = selected_image_files[start_idx:end_idx]
            frame_indices = [selected_indices[start_idx + i] for i in range(len(segment_files))]
            
            if len(segment_files) < frames_per_video:
                # 如果不足5帧，用最后一帧补齐到5帧
                last_file = segment_files[-1] if segment_files else None
                last_idx = frame_indices[-1] if frame_indices else 0

                while len(segment_files) < frames_per_video and last_file:
                    segment_files.append(last_file)  # 重复最后一帧
                    frame_indices.append(last_idx)   # 重复最后一个索引
            
            # 创建视频文件名
            start_frame_idx = selected_indices[start_idx]
            end_frame_idx = selected_indices[end_idx-1]
            video_filename = f"{seq_id}_interval{interval}_5frames_{start_frame_idx:04d}_{end_frame_idx:04d}.mp4"
            video_path = output_dir / video_filename
            
            # 获取图像尺寸
            first_img = cv2.imread(str(segment_files[0]))
            if first_img is None:
                continue
            height, width = first_img.shape[:2]
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(str(video_path), fourcc, fps, (width, height))
            
            # 收集该片段的标注信息
            segment_annotations = {}
            frame_mapping = {}  # 视频帧索引 -> 原始帧索引
            
            for video_frame_idx, img_file in enumerate(segment_files):
                # 读取图像
                img = cv2.imread(str(img_file))
                if img is None:
                    continue
                
                # 写入视频
                video_writer.write(img)
                
                # 记录帧映射
                original_frame_idx = frame_indices[video_frame_idx]
                frame_mapping[video_frame_idx] = original_frame_idx
                
                # 收集标注
                frame_id = img_file.stem
                annotations = self.load_frame_annotation(seq_id, frame_id, seq_info['size'])
                if annotations:
                    segment_annotations[video_frame_idx] = {
                        'frame_id': frame_id,
                        'original_frame_idx': original_frame_idx,
                        'annotations': annotations
                    }
            
            video_writer.release()
            
            # 保留所有视频片段（包括无标注的，用于完整覆盖）
            # 按照用户思路：所有帧都要覆盖，无标注的通过线性推理
            if True:  # 保留所有片段
                video_info = {
                    'video_path': str(video_path),
                    'seq_id': seq_id,
                    'start_frame_idx': start_frame_idx,
                    'end_frame_idx': end_frame_idx,
                    'interval': interval,
                    'frames_per_video': frames_per_video,
                    'annotations': segment_annotations,
                    'frame_mapping': frame_mapping,
                    'fps': fps,
                    'total_frames': len(segment_files)
                }
                video_files.append(video_info)
                logger.info(f"创建视频: {video_filename} (帧{start_frame_idx}-{end_frame_idx}, {len(segment_annotations)}个标注帧)")
            else:
                # 删除没有标注的视频文件
                video_path.unlink()
        
        return video_files
    
    def create_all_interval_videos(self, output_base_dir: str = "dataset/interval_5frame_videos") -> Dict[str, List]:
        """为所有序列创建隔10取1，五帧一个的视频文件"""
        logger.info("开始创建隔10取1，五帧一个的视频文件...")
        
        output_dir = Path(output_base_dir)
        all_video_data = {}
        
        for seq_id in self.sequences.keys():
            seq_output_dir = output_dir / seq_id
            video_files = self.create_interval_5frame_videos(seq_id, seq_output_dir)
            all_video_data[seq_id] = video_files
            
            logger.info(f"序列 {seq_id}: 创建了 {len(video_files)} 个5帧视频片段")
        
        # 保存视频数据信息
        video_data_file = output_dir / "interval_5frame_video_data.json"
        with open(video_data_file, 'w', encoding='utf-8') as f:
            json.dump(all_video_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"隔10取1，五帧视频数据信息保存到: {video_data_file}")
        return all_video_data
    
    def create_interval_video_training_data(self, video_data: Dict[str, List], 
                                          train_ratio: float = 0.8) -> Tuple[List[Dict], List[Dict]]:
        """创建隔10取1，五帧视频格式的训练数据"""
        logger.info("创建隔10取1，五帧视频格式训练数据...")
        
        all_samples = []
        
        for seq_id, video_files in video_data.items():
            for video_info in video_files:
                # 为每个5帧视频片段创建训练样本
                annotations_text = self.format_interval_video_annotations(video_info['annotations'])
                
                sample = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "video",
                                    "video": video_info['video_path']
                                },
                                {
                                    "type": "text",
                                    "text": f"""你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。

视频信息:
- 序列: {video_info['seq_id']}
- 采样方式: 隔{video_info['interval']}帧取1帧
- 帧数: {video_info['frames_per_video']}帧
- 原始帧范围: {video_info['start_frame_idx']}-{video_info['end_frame_idx']}

请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
video_frame_idx class_name x_center y_center width height confidence

注意：
1. video_frame_idx为视频中的帧索引（0-4）
2. 坐标为相对坐标（0-1之间）
3. 只检测尺寸在3-80像素之间的弱小目标
4. 利用5帧视频的时序信息提高检测精度
5. confidence为检测置信度（0-1之间）"""
                                }
                            ]
                        },
                        {
                            "role": "assistant",
                            "content": annotations_text
                        }
                    ]
                }
                
                all_samples.append(sample)
        
        # 划分训练集和测试集
        np.random.shuffle(all_samples)
        split_idx = int(len(all_samples) * train_ratio)
        
        train_data = all_samples[:split_idx]
        test_data = all_samples[split_idx:]
        
        logger.info(f"创建了 {len(train_data)} 个训练样本，{len(test_data)} 个测试样本")
        
        return train_data, test_data
    
    def format_interval_video_annotations(self, annotations: Dict[int, Dict]) -> str:
        """格式化隔10取1，五帧视频标注为文本"""
        result_lines = []
        
        for video_frame_idx, frame_data in annotations.items():
            for ann in frame_data['annotations']:
                line = f"{video_frame_idx} {ann['class_name']} {ann['x_center']:.6f} {ann['y_center']:.6f} {ann['width']:.6f} {ann['height']:.6f} 1.0"
                result_lines.append(line)
        
        return '\n'.join(result_lines)

def main():
    """主函数"""
    print("=== 隔10取1，五帧一个的视频数据预处理器 ===")
    print("🎯 隔10帧取1帧，每5帧组成一个视频片段")
    print("📹 符合用户要求的精确实现")
    print("🔧 用于后续的5帧视频微调和检测")
    
    # 创建预处理器
    preprocessor = Interval5FrameVideoPreprocessor()
    
    # 创建隔10取1，五帧视频文件
    video_data = preprocessor.create_all_interval_videos()
    
    # 创建训练数据
    train_data, test_data = preprocessor.create_interval_video_training_data(video_data)
    
    # 保存训练数据
    os.makedirs("data", exist_ok=True)
    
    with open("data/interval_5frame_video_train_data.json", 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    with open("data/interval_5frame_video_test_data.json", 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 隔10取1，五帧视频数据预处理完成！")
    print(f"📹 创建视频文件: {sum(len(files) for files in video_data.values())} 个")
    print(f"📊 训练样本: {len(train_data)} 个")
    print(f"📊 测试样本: {len(test_data)} 个")
    print(f"💾 训练数据保存到: data/interval_5frame_video_train_data.json")
    print(f"💾 测试数据保存到: data/interval_5frame_video_test_data.json")

if __name__ == "__main__":
    main()
