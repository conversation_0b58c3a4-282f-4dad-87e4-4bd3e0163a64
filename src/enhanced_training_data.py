#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强训练数据生成器
解决模型只学会固定输出的问题
"""

import os
import json
import random
import logging
from pathlib import Path
from typing import List, Dict, Any

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedTrainingDataGenerator:
    """增强训练数据生成器"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
    
    def load_original_data(self) -> List[Dict]:
        """加载原始训练数据"""
        with open("data/interval_5frame_video_train_data.json", 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def create_negative_samples(self, original_data: List[Dict], ratio: float = 0.3) -> List[Dict]:
        """创建负样本（无目标的视频）"""
        logger.info(f"创建负样本，比例: {ratio}")
        
        negative_samples = []
        num_negatives = int(len(original_data) * ratio)
        
        for i in range(num_negatives):
            # 随机选择一个序列
            seq_id = random.choice(list(self.sequences.keys()))
            
            sample = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "video",
                                "video": f"dataset/interval_5frame_videos/{seq_id}/negative_sample_{i}.mp4"  # 虚拟路径
                            },
                            {
                                "type": "text",
                                "text": f"""你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。

视频信息:
- 序列: {seq_id}
- 采样方式: 隔10帧取1帧
- 帧数: 5帧
- 类型: 背景片段（可能无目标）

请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
video_frame_idx class_name x_center y_center width height confidence

注意：
1. video_frame_idx为视频中的帧索引（0-4）
2. 坐标为相对坐标（0-1之间）
3. 只检测尺寸在3-80像素之间的弱小目标
4. 如果没有检测到目标，输出空行
5. confidence为检测置信度（0-1之间）"""
                            }
                        ]
                    },
                    {
                        "role": "assistant",
                        "content": ""  # 空输出，表示无目标
                    }
                ]
            }
            negative_samples.append(sample)
        
        logger.info(f"创建了 {len(negative_samples)} 个负样本")
        return negative_samples
    
    def create_partial_samples(self, original_data: List[Dict], ratio: float = 0.2) -> List[Dict]:
        """创建部分帧有目标的样本"""
        logger.info(f"创建部分目标样本，比例: {ratio}")
        
        partial_samples = []
        num_partials = int(len(original_data) * ratio)
        
        for i in range(num_partials):
            # 随机选择一个原始样本作为基础
            base_sample = random.choice(original_data)
            original_content = base_sample['messages'][1]['content']
            
            # 解析原始检测结果
            lines = original_content.strip().split('\n')
            if not lines or not lines[0]:
                continue
            
            # 随机保留1-3帧的检测结果
            num_frames_to_keep = random.randint(1, 3)
            frames_to_keep = random.sample(range(5), num_frames_to_keep)
            
            # 构建新的检测结果
            new_lines = []
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 6:
                        frame_idx = int(parts[0])
                        if frame_idx in frames_to_keep:
                            new_lines.append(line)
            
            if new_lines:  # 确保有检测结果
                new_sample = {
                    "messages": [
                        base_sample['messages'][0],  # 保持原始用户输入
                        {
                            "role": "assistant",
                            "content": '\n'.join(new_lines)
                        }
                    ]
                }
                partial_samples.append(new_sample)
        
        logger.info(f"创建了 {len(partial_samples)} 个部分目标样本")
        return partial_samples
    
    def create_multi_target_samples(self, original_data: List[Dict], ratio: float = 0.15) -> List[Dict]:
        """创建多目标样本"""
        logger.info(f"创建多目标样本，比例: {ratio}")
        
        multi_samples = []
        num_multis = int(len(original_data) * ratio)
        
        classes = ['drone', 'car', 'ship', 'bus', 'pedestrian', 'cyclist']
        
        for i in range(num_multis):
            base_sample = random.choice(original_data)
            original_content = base_sample['messages'][1]['content']
            
            lines = original_content.strip().split('\n')
            if not lines or not lines[0]:
                continue
            
            # 为每帧添加额外的目标
            new_lines = []
            for line in lines:
                if line.strip():
                    new_lines.append(line)  # 保留原始目标
                    
                    # 随机添加1-2个额外目标
                    num_extra = random.randint(1, 2)
                    for _ in range(num_extra):
                        parts = line.split()
                        if len(parts) >= 6:
                            frame_idx = parts[0]
                            new_class = random.choice(classes)
                            # 随机生成新的坐标
                            x_center = random.uniform(0.1, 0.9)
                            y_center = random.uniform(0.1, 0.9)
                            width = random.uniform(0.01, 0.05)
                            height = random.uniform(0.01, 0.05)
                            confidence = random.uniform(0.7, 1.0)
                            
                            extra_line = f"{frame_idx} {new_class} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f} {confidence:.1f}"
                            new_lines.append(extra_line)
            
            if new_lines:
                new_sample = {
                    "messages": [
                        base_sample['messages'][0],
                        {
                            "role": "assistant",
                            "content": '\n'.join(new_lines)
                        }
                    ]
                }
                multi_samples.append(new_sample)
        
        logger.info(f"创建了 {len(multi_samples)} 个多目标样本")
        return multi_samples
    
    def create_diverse_coordinate_samples(self, original_data: List[Dict], ratio: float = 0.2) -> List[Dict]:
        """创建坐标多样化样本"""
        logger.info(f"创建坐标多样化样本，比例: {ratio}")
        
        diverse_samples = []
        num_diverse = int(len(original_data) * ratio)
        
        for i in range(num_diverse):
            base_sample = random.choice(original_data)
            original_content = base_sample['messages'][1]['content']
            
            lines = original_content.strip().split('\n')
            new_lines = []
            
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 6:
                        frame_idx = parts[0]
                        class_name = parts[1]
                        
                        # 大幅度随机化坐标
                        x_center = random.uniform(0.05, 0.95)
                        y_center = random.uniform(0.05, 0.95)
                        width = random.uniform(0.008, 0.08)  # 3-80像素范围
                        height = random.uniform(0.008, 0.08)
                        confidence = random.uniform(0.6, 1.0)
                        
                        new_line = f"{frame_idx} {class_name} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f} {confidence:.1f}"
                        new_lines.append(new_line)
            
            if new_lines:
                new_sample = {
                    "messages": [
                        base_sample['messages'][0],
                        {
                            "role": "assistant",
                            "content": '\n'.join(new_lines)
                        }
                    ]
                }
                diverse_samples.append(new_sample)
        
        logger.info(f"创建了 {len(diverse_samples)} 个坐标多样化样本")
        return diverse_samples
    
    def generate_enhanced_training_data(self) -> tuple:
        """生成增强的训练数据"""
        logger.info("开始生成增强训练数据...")
        
        # 加载原始数据
        original_data = self.load_original_data()
        logger.info(f"原始训练样本: {len(original_data)} 个")
        
        # 创建各种增强样本
        negative_samples = self.create_negative_samples(original_data, 0.3)
        partial_samples = self.create_partial_samples(original_data, 0.2)
        multi_samples = self.create_multi_target_samples(original_data, 0.15)
        diverse_samples = self.create_diverse_coordinate_samples(original_data, 0.2)
        
        # 合并所有样本
        all_samples = original_data + negative_samples + partial_samples + multi_samples + diverse_samples
        
        # 随机打乱
        random.shuffle(all_samples)
        
        # 划分训练集和测试集
        split_idx = int(len(all_samples) * 0.8)
        train_data = all_samples[:split_idx]
        test_data = all_samples[split_idx:]
        
        logger.info(f"增强后总样本: {len(all_samples)} 个")
        logger.info(f"  - 原始样本: {len(original_data)} 个")
        logger.info(f"  - 负样本: {len(negative_samples)} 个")
        logger.info(f"  - 部分目标样本: {len(partial_samples)} 个")
        logger.info(f"  - 多目标样本: {len(multi_samples)} 个")
        logger.info(f"  - 坐标多样化样本: {len(diverse_samples)} 个")
        logger.info(f"训练集: {len(train_data)} 个")
        logger.info(f"测试集: {len(test_data)} 个")
        
        return train_data, test_data

def main():
    """主函数"""
    print("=== 🚀 增强训练数据生成器 ===")
    print("🎯 解决模型只学会固定输出的问题")
    print("📊 创建多样化训练样本")
    print("🔧 包含负样本、部分目标、多目标、坐标多样化")
    
    generator = EnhancedTrainingDataGenerator()
    train_data, test_data = generator.generate_enhanced_training_data()
    
    # 保存增强数据
    with open("data/enhanced_train_data.json", 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    with open("data/enhanced_test_data.json", 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 增强训练数据生成完成！")
    print(f"📊 训练样本: {len(train_data)} 个 (增长 {len(train_data)/117:.1f}倍)")
    print(f"📊 测试样本: {len(test_data)} 个")
    print(f"💾 保存到: data/enhanced_train_data.json")
    print(f"🎯 现在模型将学会真正的检测能力！")

if __name__ == "__main__":
    main()
