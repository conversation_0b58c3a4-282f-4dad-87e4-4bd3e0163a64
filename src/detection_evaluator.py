#!/usr/bin/env python3
"""
检测结果评估器 - 计算精确度、召回率、F1分数等指标
"""

import json
import os
import numpy as np
from pathlib import Path
import logging
from typing import Dict, List, Tuple
import glob

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DetectionEvaluator:
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data24': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data25': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        self.class_mapping = {
            'drone': 0, 'car': 1, 'ship': 2, 'bus': 3, 
            'pedestrian': 4, 'cyclist': 5
        }
        
    def load_ground_truth(self, seq_id: str) -> Dict[int, List[Dict]]:
        """加载真实标注"""
        gt_annotations = {}
        seq_info = self.sequences[seq_id]
        
        # 获取序列的所有图像文件
        img_dir = Path(f"dataset/images/{seq_id}")
        if not img_dir.exists():
            logger.warning(f"图像目录不存在: {img_dir}")
            return gt_annotations
            
        # 获取所有图像文件
        if seq_info['format'] == '.bmp':
            img_files = sorted(img_dir.glob("*.bmp"))
        else:
            img_files = sorted(img_dir.glob("*.jpg"))
            
        logger.info(f"序列 {seq_id} 找到 {len(img_files)} 个图像文件")
        
        # 为每个图像文件加载标注
        for img_file in img_files:
            frame_id = img_file.stem
            original_frame_idx = self.get_original_frame_idx(frame_id, seq_info['naming'])
            
            # 加载该帧的标注
            annotations = self.load_frame_annotation(seq_id, frame_id, seq_info['size'])
            if annotations:
                gt_annotations[original_frame_idx] = annotations
                
        logger.info(f"序列 {seq_id} 加载了 {len(gt_annotations)} 帧的真实标注")
        return gt_annotations
        
    def get_original_frame_idx(self, frame_id: str, naming: str) -> int:
        """从帧ID获取原始帧索引"""
        if naming == 'simple':
            return int(frame_id)
        else:  # complex naming
            # 处理复杂的文件名格式
            if 'merged_dataset_data_transform' in frame_id:
                # 格式: merged_dataset_data_transform_1_01_1751_0250-1750_000001
                # 提取最后的数字部分
                parts = frame_id.split('_')
                if len(parts) > 0:
                    last_part = parts[-1]
                    try:
                        return int(last_part) - 1  # 转换为0-based索引
                    except ValueError:
                        pass
            elif frame_id.startswith('img'):
                # 格式: img00001
                return int(frame_id[3:]) - 1  # 转换为0-based索引

            # 如果都不匹配，尝试直接转换
            try:
                return int(frame_id)
            except ValueError:
                logger.warning(f"无法解析帧ID: {frame_id}")
                return 0
                
    def load_frame_annotation(self, seq_id: str, frame_id: str, img_size: Tuple[int, int]) -> List[Dict]:
        """加载单帧标注"""
        annotations = []
        
        # 标注文件路径
        label_file = Path(f"dataset/labels/{seq_id}/{frame_id}.txt")
        
        if not label_file.exists():
            return annotations
            
        try:
            with open(label_file, 'r') as f:
                lines = f.readlines()
                
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                parts = line.split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    abs_w = width * img_size[0]
                    abs_h = height * img_size[1]
                    
                    # 只保留弱小目标 (3-80像素)
                    if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                        annotations.append({
                            'class_id': class_id,
                            'x_center': x_center,
                            'y_center': y_center,
                            'width': width,
                            'height': height,
                            'abs_w': abs_w,
                            'abs_h': abs_h
                        })
                        
        except Exception as e:
            logger.warning(f"读取标注文件失败 {label_file}: {e}")
            
        return annotations
        
    def load_detections(self, seq_id: str) -> Dict[int, List[Dict]]:
        """加载检测结果"""
        detection_file = Path(f"results/interval_5frame_detection/{seq_id}_detections.json")
        
        if not detection_file.exists():
            logger.warning(f"检测结果文件不存在: {detection_file}")
            return {}
            
        try:
            with open(detection_file, 'r') as f:
                detections_list = json.load(f)
                
            # 按帧索引组织检测结果
            detections = {}
            for det in detections_list:
                frame_idx = det['original_frame_idx']
                if frame_idx not in detections:
                    detections[frame_idx] = []
                detections[frame_idx].append(det)
                
            logger.info(f"序列 {seq_id} 加载了 {len(detections)} 帧的检测结果")
            return detections
            
        except Exception as e:
            logger.error(f"读取检测结果失败 {detection_file}: {e}")
            return {}
            
    def calculate_iou(self, box1: Dict, box2: Dict) -> float:
        """计算两个边界框的IoU"""
        # 转换为边界框坐标
        x1_1 = box1['x_center'] - box1['width'] / 2
        y1_1 = box1['y_center'] - box1['height'] / 2
        x2_1 = box1['x_center'] + box1['width'] / 2
        y2_1 = box1['y_center'] + box1['height'] / 2
        
        x1_2 = box2['x_center'] - box2['width'] / 2
        y1_2 = box2['y_center'] - box2['height'] / 2
        x2_2 = box2['x_center'] + box2['width'] / 2
        y2_2 = box2['y_center'] + box2['height'] / 2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
            
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = box1['width'] * box1['height']
        area2 = box2['width'] * box2['height']
        union_area = area1 + area2 - inter_area
        
        if union_area <= 0:
            return 0.0
            
        return inter_area / union_area
        
    def evaluate_sequence(self, seq_id: str, iou_threshold: float = 0.3) -> Dict:
        """评估单个序列"""
        logger.info(f"评估序列 {seq_id}...")
        
        # 加载真实标注和检测结果
        gt_annotations = self.load_ground_truth(seq_id)
        detections = self.load_detections(seq_id)
        
        if not gt_annotations and not detections:
            logger.warning(f"序列 {seq_id} 没有标注和检测结果")
            return {'tp': 0, 'fp': 0, 'fn': 0, 'precision': 0, 'recall': 0, 'f1': 0}
            
        tp = 0  # True Positive
        fp = 0  # False Positive
        fn = 0  # False Negative
        
        # 获取所有帧的索引
        all_frames = set(gt_annotations.keys()) | set(detections.keys())
        
        for frame_idx in all_frames:
            gt_boxes = gt_annotations.get(frame_idx, [])
            det_boxes = detections.get(frame_idx, [])
            
            # 匹配检测结果和真实标注
            matched_gt = set()
            matched_det = set()
            
            for i, det_box in enumerate(det_boxes):
                best_iou = 0
                best_gt_idx = -1
                
                for j, gt_box in enumerate(gt_boxes):
                    if j in matched_gt:
                        continue
                        
                    iou = self.calculate_iou(det_box, gt_box)
                    if iou > best_iou:
                        best_iou = iou
                        best_gt_idx = j
                        
                if best_iou >= iou_threshold:
                    tp += 1
                    matched_gt.add(best_gt_idx)
                    matched_det.add(i)
                else:
                    fp += 1
                    
            # 未匹配的真实标注为漏检
            fn += len(gt_boxes) - len(matched_gt)
            
        # 计算指标
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        result = {
            'tp': tp,
            'fp': fp,
            'fn': fn,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'gt_frames': len(gt_annotations),
            'det_frames': len(detections),
            'total_gt_objects': sum(len(boxes) for boxes in gt_annotations.values()),
            'total_det_objects': sum(len(boxes) for boxes in detections.values())
        }
        
        logger.info(f"序列 {seq_id} 评估完成: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}")
        return result
        
    def evaluate_all_sequences(self, iou_threshold: float = 0.3) -> Dict:
        """评估所有序列"""
        logger.info("开始评估所有序列...")
        
        all_results = {}
        total_tp = 0
        total_fp = 0
        total_fn = 0
        
        for seq_id in self.sequences.keys():
            result = self.evaluate_sequence(seq_id, iou_threshold)
            all_results[seq_id] = result
            
            total_tp += result['tp']
            total_fp += result['fp']
            total_fn += result['fn']
            
        # 计算总体指标
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
        
        all_results['overall'] = {
            'tp': total_tp,
            'fp': total_fp,
            'fn': total_fn,
            'precision': overall_precision,
            'recall': overall_recall,
            'f1': overall_f1
        }
        
        return all_results

def main():
    """主函数"""
    print("=== 检测结果评估器 ===")
    print("📊 计算精确度、召回率、F1分数等指标")
    print("🎯 IoU阈值: 0.3")
    print()
    
    evaluator = DetectionEvaluator()
    
    # 评估所有序列
    results = evaluator.evaluate_all_sequences(iou_threshold=0.3)
    
    # 打印结果
    print("📈 各序列检测指标:")
    print("-" * 80)
    print(f"{'序列':<8} {'精确度':<8} {'召回率':<8} {'F1分数':<8} {'TP':<6} {'FP':<6} {'FN':<6} {'GT目标':<8} {'检测目标':<8}")
    print("-" * 80)
    
    for seq_id in evaluator.sequences.keys():
        if seq_id in results:
            r = results[seq_id]
            print(f"{seq_id:<8} {r['precision']:<8.3f} {r['recall']:<8.3f} {r['f1']:<8.3f} "
                  f"{r['tp']:<6} {r['fp']:<6} {r['fn']:<6} {r['total_gt_objects']:<8} {r['total_det_objects']:<8}")
    
    print("-" * 80)
    r = results['overall']
    print(f"{'总体':<8} {r['precision']:<8.3f} {r['recall']:<8.3f} {r['f1']:<8.3f} "
          f"{r['tp']:<6} {r['fp']:<6} {r['fn']:<6}")
    print("-" * 80)
    
    # 保存结果
    output_file = "results/detection_metrics.json"
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📁 详细结果已保存到: {output_file}")
    print("✅ 评估完成!")

if __name__ == "__main__":
    main()
