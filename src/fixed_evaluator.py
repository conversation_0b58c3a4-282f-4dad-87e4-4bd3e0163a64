#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版评估器
解决帧索引映射、IoU阈值、坐标匹配等问题
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixedEvaluator:
    """修复版评估器"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def create_frame_mapping(self, seq_id: str) -> Dict[int, str]:
        """创建帧索引到帧ID的映射"""
        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = self.get_image_files(images_dir, seq_info)
        
        # 创建索引到frame_id的映射
        frame_mapping = {}
        for idx, img_file in enumerate(image_files):
            frame_mapping[idx] = img_file.stem
        
        return frame_mapping
    
    def load_ground_truth_with_mapping(self, seq_id: str) -> Dict[str, List[Dict]]:
        """加载真实标注，使用正确的帧映射"""
        gt_annotations = {}
        labels_dir = Path(f"dataset/labels/{seq_id}")
        
        if not labels_dir.exists():
            return {}
        
        seq_info = self.sequences[seq_id]
        frame_mapping = self.create_frame_mapping(seq_id)
        
        for label_file in labels_dir.glob("*.txt"):
            frame_id = label_file.stem
            annotations = []
            
            with open(label_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) != 5:
                        continue
                    
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    abs_w = width * seq_info['size'][0]
                    abs_h = height * seq_info['size'][1]
                    
                    # 检查是否为弱小目标（3-80像素）
                    if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                        class_name = self.class_map.get(str(class_id), 'unknown')
                        annotations.append({
                            'class_id': class_id,
                            'class_name': class_name,
                            'x_center': x_center,
                            'y_center': y_center,
                            'width': width,
                            'height': height,
                            'abs_w': abs_w,
                            'abs_h': abs_h
                        })
            
            if annotations:
                gt_annotations[frame_id] = annotations
        
        return gt_annotations
    
    def load_predictions_with_mapping(self, seq_id: str) -> List[Dict]:
        """加载预测结果，修复帧映射"""
        pred_file = Path(f"results/interval_5frame_detection/{seq_id}_detections.json")
        
        if not pred_file.exists():
            logger.warning(f"预测文件不存在: {pred_file}")
            return []
        
        with open(pred_file, 'r', encoding='utf-8') as f:
            predictions = json.load(f)
        
        # 修复帧映射
        frame_mapping = self.create_frame_mapping(seq_id)
        
        for pred in predictions:
            original_idx = pred.get('original_frame_idx', 0)
            if original_idx in frame_mapping:
                pred['corrected_frame_id'] = frame_mapping[original_idx]
            else:
                pred['corrected_frame_id'] = str(original_idx)
        
        return predictions
    
    def calculate_iou_flexible(self, box1: Dict, box2: Dict, coord_tolerance: float = 0.01) -> float:
        """计算IoU，增加坐标容错"""
        x1_center, y1_center, w1, h1 = box1['x_center'], box1['y_center'], box1['width'], box1['height']
        x2_center, y2_center, w2, h2 = box2['x_center'], box2['y_center'], box2['width'], box2['height']
        
        # 坐标容错处理
        if abs(x1_center - x2_center) < coord_tolerance and abs(y1_center - y2_center) < coord_tolerance:
            # 如果中心点很接近，给予额外的IoU奖励
            center_bonus = 0.1
        else:
            center_bonus = 0.0
        
        # 转换为左上角和右下角坐标
        x1_min, y1_min = x1_center - w1/2, y1_center - h1/2
        x1_max, y1_max = x1_center + w1/2, y1_center + h1/2
        
        x2_min, y2_min = x2_center - w2/2, y2_center - h2/2
        x2_max, y2_max = x2_center + w2/2, y2_center + h2/2
        
        # 计算交集
        inter_x_min = max(x1_min, x2_min)
        inter_y_min = max(y1_min, y2_min)
        inter_x_max = min(x1_max, x2_max)
        inter_y_max = min(y1_max, y2_max)
        
        if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
            return center_bonus
        
        inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)
        
        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union_area = area1 + area2 - inter_area
        
        iou = inter_area / union_area if union_area > 0 else 0.0
        return iou + center_bonus
    
    def evaluate_with_multiple_thresholds(self, seq_id: str) -> Dict[str, Any]:
        """使用多个IoU阈值评估"""
        logger.info(f"评估序列 {seq_id} (多阈值)...")
        
        # 加载数据
        gt_annotations = self.load_ground_truth_with_mapping(seq_id)
        predictions = self.load_predictions_with_mapping(seq_id)
        
        # 按帧组织预测结果
        pred_by_frame = {}
        for pred in predictions:
            frame_id = pred.get('corrected_frame_id', str(pred.get('original_frame_idx', 0)))
            if frame_id not in pred_by_frame:
                pred_by_frame[frame_id] = []
            pred_by_frame[frame_id].append(pred)
        
        # 多个IoU阈值
        thresholds = [0.1, 0.3, 0.5]
        results = {}
        
        for threshold in thresholds:
            total_gt = sum(len(gt_list) for gt_list in gt_annotations.values())
            total_pred = len(predictions)
            true_positives = 0
            
            # 计算匹配
            for frame_id, gt_list in gt_annotations.items():
                pred_list = pred_by_frame.get(frame_id, []).copy()
                
                for gt_box in gt_list:
                    best_iou = 0
                    best_match = None
                    
                    for pred_box in pred_list:
                        # 类别匹配检查（宽松）
                        if (pred_box['class_name'].lower() == gt_box['class_name'].lower() or
                            pred_box['class_name'] in gt_box['class_name'] or
                            gt_box['class_name'] in pred_box['class_name']):
                            
                            iou = self.calculate_iou_flexible(gt_box, pred_box)
                            if iou > best_iou:
                                best_iou = iou
                                best_match = pred_box
                    
                    if best_iou >= threshold:
                        true_positives += 1
                        if best_match in pred_list:
                            pred_list.remove(best_match)
            
            # 计算指标
            precision = true_positives / total_pred if total_pred > 0 else 0
            recall = true_positives / total_gt if total_gt > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            results[f'iou_{threshold}'] = {
                'threshold': threshold,
                'total_gt': total_gt,
                'total_pred': total_pred,
                'true_positives': true_positives,
                'precision': precision,
                'recall': recall,
                'f1': f1
            }
            
            logger.info(f"{seq_id} @IoU{threshold}: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}, TP={true_positives}")
        
        return {
            'sequence': seq_id,
            'results_by_threshold': results,
            'best_threshold': max(results.keys(), key=lambda k: results[k]['f1']),
            'best_f1': max(results[k]['f1'] for k in results.keys())
        }
    
    def evaluate_all_sequences(self) -> Dict[str, Any]:
        """评估所有序列"""
        test_sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
        
        logger.info("开始修复版多阈值评估...")
        
        all_results = []
        summary_by_threshold = {f'iou_{t}': {'total_gt': 0, 'total_pred': 0, 'total_tp': 0} 
                               for t in [0.1, 0.3, 0.5]}
        
        for seq_id in test_sequences:
            result = self.evaluate_with_multiple_thresholds(seq_id)
            all_results.append(result)
            
            # 累计统计
            for threshold_key, threshold_result in result['results_by_threshold'].items():
                summary_by_threshold[threshold_key]['total_gt'] += threshold_result['total_gt']
                summary_by_threshold[threshold_key]['total_pred'] += threshold_result['total_pred']
                summary_by_threshold[threshold_key]['total_tp'] += threshold_result['true_positives']
        
        # 计算总体指标
        overall_results = {}
        for threshold_key, summary in summary_by_threshold.items():
            precision = summary['total_tp'] / summary['total_pred'] if summary['total_pred'] > 0 else 0
            recall = summary['total_tp'] / summary['total_gt'] if summary['total_gt'] > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            overall_results[threshold_key] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'total_gt': summary['total_gt'],
                'total_pred': summary['total_pred'],
                'total_tp': summary['total_tp']
            }
        
        return {
            'method': 'Fixed Multi-Threshold Evaluation',
            'individual_results': all_results,
            'overall_results': overall_results,
            'best_overall_threshold': max(overall_results.keys(), key=lambda k: overall_results[k]['f1'])
        }

def main():
    """主函数"""
    print("=== 修复版多阈值评估器 ===")
    print("🔧 修复帧索引映射问题")
    print("📊 使用多个IoU阈值评估")
    print("🎯 增加坐标容错机制")
    
    evaluator = FixedEvaluator()
    results = evaluator.evaluate_all_sequences()
    
    # 保存结果
    with open("results/fixed_evaluation.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 显示结果
    print(f"\n📊 修复版评估结果:")
    print(f"🎯 方法: {results['method']}")
    
    print(f"\n📈 不同IoU阈值下的总体性能:")
    for threshold_key, result in results['overall_results'].items():
        threshold = threshold_key.split('_')[1]
        print(f"   IoU@{threshold}: F1={result['f1']:.3f} (P={result['precision']:.3f}, R={result['recall']:.3f})")
    
    best_threshold = results['best_overall_threshold']
    best_result = results['overall_results'][best_threshold]
    print(f"\n🏆 最佳阈值: {best_threshold.split('_')[1]} (F1={best_result['f1']:.3f})")
    
    print(f"\n📋 各序列在最佳阈值下的表现:")
    for result in results['individual_results']:
        seq_id = result['sequence']
        best_seq_result = result['results_by_threshold'][best_threshold]
        print(f"   {seq_id}: F1={best_seq_result['f1']:.3f}")
    
    print(f"\n💾 详细结果保存到: results/fixed_evaluation.json")

if __name__ == "__main__":
    main()
