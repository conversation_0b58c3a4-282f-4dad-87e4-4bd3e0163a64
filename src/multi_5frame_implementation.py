#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多个5帧视频实现
按照用户思路：五帧一个视频，输入给模型多个视频
"""

import os
import sys
import json
import cv2
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Multi5FrameImplementation:
    """多个5帧视频实现"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def create_multi_5frame_videos(self, seq_id: str, output_dir: Path, 
                                 interval: int = 10, frames_per_video: int = 5) -> List[Dict]:
        """
        创建多个5帧视频
        每隔10帧抽取1帧，每5帧组成一个视频
        """
        logger.info(f"为序列 {seq_id} 创建多个5帧视频...")
        
        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        all_image_files = self.get_image_files(images_dir, seq_info)
        
        if len(all_image_files) == 0:
            logger.warning(f"序列 {seq_id} 没有图像文件")
            return []
        
        # 每隔10帧抽取1帧
        selected_indices = list(range(0, len(all_image_files), interval))
        selected_image_files = [all_image_files[i] for i in selected_indices]
        
        logger.info(f"序列 {seq_id}: 总帧数 {len(all_image_files)}, 每隔10帧抽取后 {len(selected_image_files)} 帧")
        
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 每5帧组成一个视频
        video_files = []
        
        for start_idx in range(0, len(selected_image_files), frames_per_video):
            end_idx = min(start_idx + frames_per_video, len(selected_image_files))
            if end_idx - start_idx < frames_per_video:
                continue  # 跳过不足5帧的
            
            segment_files = selected_image_files[start_idx:end_idx]
            frame_indices = [selected_indices[start_idx + i] for i in range(frames_per_video)]
            
            # 创建视频文件名
            video_filename = f"{seq_id}_5frames_{start_idx:03d}_{end_idx:03d}.mp4"
            video_path = output_dir / video_filename
            
            # 获取图像尺寸
            first_img = cv2.imread(str(segment_files[0]))
            if first_img is None:
                continue
            height, width = first_img.shape[:2]
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(str(video_path), fourcc, 10, (width, height))
            
            # 收集该5帧视频的标注信息
            segment_annotations = {}
            
            for video_frame_idx, img_file in enumerate(segment_files):
                # 读取图像
                img = cv2.imread(str(img_file))
                if img is None:
                    continue
                
                # 写入视频
                video_writer.write(img)
                
                # 收集标注
                frame_id = img_file.stem
                original_frame_idx = frame_indices[video_frame_idx]
                annotations = self.load_frame_annotation(seq_id, frame_id, seq_info['size'])
                
                if annotations:
                    segment_annotations[video_frame_idx] = {
                        'frame_id': frame_id,
                        'original_frame_idx': original_frame_idx,
                        'annotations': annotations
                    }
            
            video_writer.release()
            
            # 保留所有视频（包括无标注的，用于完整性）
            video_info = {
                'video_path': str(video_path),
                'seq_id': seq_id,
                'start_frame_idx': frame_indices[0],
                'end_frame_idx': frame_indices[-1],
                'frame_indices': frame_indices,
                'annotations': segment_annotations,
                'video_index': len(video_files),  # 在序列中的索引
                'fps': 10,
                'total_frames': frames_per_video
            }
            video_files.append(video_info)
            
            logger.info(f"创建5帧视频: {video_filename} (帧{frame_indices[0]}-{frame_indices[-1]}, {len(segment_annotations)}个标注帧)")
        
        return video_files
    
    def load_frame_annotation(self, seq_id: str, frame_id: str, image_size: Tuple[int, int]) -> List[Dict]:
        """加载帧标注"""
        annotation_file = Path(f"dataset/labels/{seq_id}/{frame_id}.txt")
        if not annotation_file.exists():
            return []
        
        annotations = []
        with open(annotation_file, 'r') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) != 5:
                    continue
                
                class_id = int(parts[0])
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                
                # 转换为绝对坐标
                abs_w = width * image_size[0]
                abs_h = height * image_size[1]
                
                # 检查是否为弱小目标（3-80像素）
                if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                    class_name = self.class_map.get(str(class_id), 'unknown')
                    annotations.append({
                        'class_id': class_id,
                        'class_name': class_name,
                        'x_center': x_center,
                        'y_center': y_center,
                        'width': width,
                        'height': height,
                        'frame_id': frame_id
                    })
        
        return annotations
    
    def create_all_multi_5frame_videos(self, output_base_dir: str = "dataset/multi_5frame_videos") -> Dict[str, List]:
        """为所有序列创建多个5帧视频"""
        logger.info("开始创建多个5帧视频...")
        
        output_dir = Path(output_base_dir)
        all_video_data = {}
        
        for seq_id in self.sequences.keys():
            seq_output_dir = output_dir / seq_id
            video_files = self.create_multi_5frame_videos(seq_id, seq_output_dir)
            all_video_data[seq_id] = video_files
            
            logger.info(f"序列 {seq_id}: 创建了 {len(video_files)} 个5帧视频")
        
        # 保存视频数据信息
        video_data_file = output_dir / "multi_5frame_video_data.json"
        with open(video_data_file, 'w', encoding='utf-8') as f:
            json.dump(all_video_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"多个5帧视频数据信息保存到: {video_data_file}")
        return all_video_data
    
    def create_multi_video_training_data(self, video_data: Dict[str, List], 
                                       videos_per_sample: int = 3,
                                       train_ratio: float = 0.8) -> Tuple[List[Dict], List[Dict]]:
        """
        创建多视频训练数据
        每个训练样本包含多个连续的5帧视频
        """
        logger.info(f"创建多视频训练数据，每个样本包含 {videos_per_sample} 个5帧视频...")
        
        all_samples = []
        
        for seq_id, video_files in video_data.items():
            # 为每个序列创建多视频样本
            for start_idx in range(0, len(video_files) - videos_per_sample + 1):
                # 选择连续的多个5帧视频
                selected_videos = video_files[start_idx:start_idx + videos_per_sample]
                
                # 收集所有视频的标注
                all_annotations = {}
                video_paths = []
                
                for video_idx, video_info in enumerate(selected_videos):
                    video_paths.append(video_info['video_path'])
                    
                    # 调整标注的帧索引（相对于多视频样本）
                    for frame_idx, frame_data in video_info['annotations'].items():
                        global_frame_idx = video_idx * 5 + frame_idx  # 全局帧索引
                        all_annotations[global_frame_idx] = frame_data
                
                if all_annotations:  # 只保留有标注的样本
                    annotations_text = self.format_multi_video_annotations(all_annotations)
                    
                    sample = {
                        "messages": [
                            {
                                "role": "user",
                                "content": [
                                    # 多个视频输入
                                    *[{"type": "video", "video": path} for path in video_paths],
                                    {
                                        "type": "text",
                                        "text": f"""你是专业的红外弱小目标视频检测专家。请检测这{videos_per_sample}个连续的5帧视频中的弱小目标。

视频信息:
- 序列: {seq_id}
- 采样方式: 每隔10帧抽取1帧
- 视频数量: {videos_per_sample}个5帧视频
- 总帧数: {videos_per_sample * 5}帧

请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
frame_idx class_name x_center y_center width height confidence

注意：
1. frame_idx为全局帧索引（0-{videos_per_sample * 5 - 1}）
2. 坐标为相对坐标（0-1之间）
3. 只检测尺寸在3-80像素之间的弱小目标
4. 利用多个5帧视频的时序信息提高检测精度
5. confidence为检测置信度（0-1之间）"""
                                    }
                                ]
                            },
                            {
                                "role": "assistant",
                                "content": annotations_text
                            }
                        ]
                    }
                    
                    all_samples.append(sample)
        
        # 随机打乱并划分
        np.random.shuffle(all_samples)
        split_idx = int(len(all_samples) * train_ratio)
        
        train_data = all_samples[:split_idx]
        test_data = all_samples[split_idx:]
        
        logger.info(f"创建了 {len(train_data)} 个训练样本，{len(test_data)} 个测试样本")
        
        return train_data, test_data
    
    def format_multi_video_annotations(self, annotations: Dict[int, Dict]) -> str:
        """格式化多视频标注为文本"""
        result_lines = []
        
        for frame_idx, frame_data in annotations.items():
            for ann in frame_data['annotations']:
                line = f"{frame_idx} {ann['class_name']} {ann['x_center']:.6f} {ann['y_center']:.6f} {ann['width']:.6f} {ann['height']:.6f} 1.0"
                result_lines.append(line)
        
        return '\n'.join(result_lines)

def main():
    """主函数"""
    print("=== 多个5帧视频实现 ===")
    print("🎯 每隔10帧抽取1帧，每5帧组成一个视频")
    print("📹 训练时输入多个连续的5帧视频")
    print("🔧 避免视频序列过长的问题")
    
    # 创建实现
    implementer = Multi5FrameImplementation()
    
    # 创建多个5帧视频
    video_data = implementer.create_all_multi_5frame_videos()
    
    # 创建多视频训练数据
    train_data, test_data = implementer.create_multi_video_training_data(video_data, videos_per_sample=3)
    
    # 保存训练数据
    os.makedirs("data", exist_ok=True)
    
    with open("data/multi_5frame_train_data.json", 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    with open("data/multi_5frame_test_data.json", 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 多个5帧视频实现完成！")
    print(f"📹 创建5帧视频: {sum(len(videos) for videos in video_data.values())} 个")
    print(f"📊 训练样本: {len(train_data)} 个 (每个包含3个5帧视频)")
    print(f"📊 测试样本: {len(test_data)} 个")
    print(f"💾 训练数据保存到: data/multi_5frame_train_data.json")
    print(f"🎯 符合用户要求：五帧一个视频，输入多个视频")

if __name__ == "__main__":
    main()
