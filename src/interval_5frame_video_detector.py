#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
隔10取1，五帧视频检测器 + 线性推理
按照用户要求：隔10帧取1帧，每5帧检测，然后线性推理中间帧
"""

import os
import sys
import json
import torch
import logging
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
import re
from qwen_vl_utils import process_vision_info

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Interval5FrameVideoDetector:
    """隔10取1，五帧视频检测器 + 线性推理"""
    
    def __init__(self, base_model_path: str, lora_model_path: str):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        logger.info(f"5帧视频LoRA模型: {lora_model_path}")
        
        # 加载基础模型
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # 加载5帧视频LoRA微调模型
        logger.info("加载隔10取1，五帧视频LoRA模型...")
        self.model = PeftModel.from_pretrained(self.model, lora_model_path)
        logger.info("隔10取1，五帧视频检测器加载完成")
        
        # 序列信息 - small文件夹的所有序列
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data24': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data25': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def create_5frame_video_from_images(self, seq_id: str, frame_indices: List[int],
                                      fps: int = 10, image_files: List = None) -> str:
        """从指定帧索引创建5帧视频文件"""
        seq_info = self.sequences[seq_id]

        if image_files is None:
            images_dir = Path(f"dataset/images/{seq_id}")
            all_image_files = self.get_image_files(images_dir, seq_info)
        else:
            all_image_files = image_files
        
        # 选择指定的帧
        selected_files = []
        for idx in frame_indices:
            if 0 <= idx < len(all_image_files):
                selected_files.append(all_image_files[idx])
        
        if len(selected_files) != 5:
            return None
        
        # 创建临时视频文件
        temp_video_path = f"temp_5frame_{seq_id}_{frame_indices[0]}_{frame_indices[-1]}.mp4"
        
        # 获取图像尺寸
        first_img = cv2.imread(str(selected_files[0]))
        height, width = first_img.shape[:2]
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))
        
        for img_file in selected_files:
            img = cv2.imread(str(img_file))
            if img is not None:
                video_writer.write(img)
        
        video_writer.release()
        return temp_video_path
    
    def detect_multi_5frame_videos(self, video_paths: List[str], seq_id: str,
                                 all_frame_indices: List[List[int]]) -> List[Dict]:
        """检测多个5帧视频 - 支持多视频输入"""
        try:
            # 构建多视频检测消息
            content = []

            # 添加所有视频
            for video_path in video_paths:
                content.append({
                    "type": "video",
                    "video": video_path
                })

            # 添加检测指令
            content.append({
                "type": "text",
                "text": f"""你是专业的红外弱小目标视频检测专家。请检测这{len(video_paths)}个连续的5帧视频片段中的弱小目标。

视频信息:
- 序列: {seq_id}
- 视频数量: {len(video_paths)}个
- 每个视频: 5帧，隔10帧采样
- 原始帧索引: {all_frame_indices}

请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
video_idx frame_idx class_name x_center y_center width height confidence

注意：
1. video_idx为视频索引（0-{len(video_paths)-1}）
2. frame_idx为视频内帧索引（0-4）
3. 坐标为相对坐标（0-1之间）
4. 只检测尺寸在3-80像素之间的弱小目标
5. 利用多视频时序信息提高检测精度
6. confidence为检测置信度（0-1之间）"""
            })

            messages = [
                {
                    "role": "user",
                    "content": content
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 使用process_vision_info处理视频
            image_inputs, video_inputs = process_vision_info(messages)

            # 处理输入
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            inputs = inputs.to(self.device)
            
            # 生成检测结果
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=256,
                    do_sample=False,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            # 解码结果
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            # 解析多视频检测结果
            logger.info(f"模型输出: {output_text[:200]}...")  # 显示前200字符
            detections = self.parse_multi_video_detection_output(output_text, seq_id, all_frame_indices)
            logger.info(f"解析得到 {len(detections)} 个检测结果")
            
            return detections
            
        except Exception as e:
            logger.error(f"5帧视频检测失败: {e}")
            return []
    
    def parse_multi_video_detection_output(self, output_text: str, seq_id: str,
                                         all_frame_indices: List[List[int]]) -> List[Dict]:
        """解析多视频检测输出"""
        detections = []
        lines = output_text.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 解析多视频检测结果行: video_idx frame_idx class_name x y w h confidence
            parts = line.split()
            if len(parts) >= 7:
                try:
                    video_idx = int(parts[0])
                    frame_idx = int(parts[1])
                    class_name = parts[2]
                    x_center = float(parts[3])
                    y_center = float(parts[4])
                    width = float(parts[5])
                    height = float(parts[6])
                    confidence = float(parts[7]) if len(parts) > 7 else 1.0

                    # 验证索引范围
                    if 0 <= video_idx < len(all_frame_indices) and 0 <= frame_idx < 5:
                        # 转换为原始帧索引
                        frame_indices = all_frame_indices[video_idx]
                        original_frame_idx = frame_indices[frame_idx]

                        # 获取对应的图像文件名作为frame_id
                        seq_info = self.sequences[seq_id]
                        images_dir = Path(f"dataset/images/{seq_id}")
                        all_image_files = self.get_image_files(images_dir, seq_info)
                        if original_frame_idx < len(all_image_files):
                            frame_id = all_image_files[original_frame_idx].stem
                        else:
                            frame_id = str(original_frame_idx)
                        
                        # 暂时不做坐标校正，看看原始结果
                        corrected_x = x_center  # 原始X坐标
                        corrected_y = y_center  # 原始Y坐标

                        # 确保校正后的坐标在有效范围内
                        corrected_x = max(0, min(1, corrected_x))
                        corrected_y = max(0, min(1, corrected_y))

                        # 验证坐标范围
                        if 0 <= corrected_x <= 1 and 0 <= corrected_y <= 1 and 0 < width <= 1 and 0 < height <= 1:
                            # 验证是否为弱小目标
                            seq_info = self.sequences[seq_id]
                            abs_w = width * seq_info['size'][0]
                            abs_h = height * seq_info['size'][1]

                            if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                                detections.append({
                                    'video_idx': video_idx,
                                    'frame_idx': frame_idx,
                                    'original_frame_idx': original_frame_idx,
                                    'frame_id': frame_id,  # 添加正确的frame_id
                                    'class_name': class_name,
                                    'x_center': corrected_x,  # 使用校正后的坐标
                                    'y_center': corrected_y,  # 使用校正后的坐标
                                    'width': width,
                                    'height': height,
                                    'confidence': confidence,
                                    'abs_w': abs_w,
                                    'abs_h': abs_h,
                                    'original_x': x_center,  # 保留原始坐标用于调试
                                    'original_y': y_center
                                })
                
                except (ValueError, IndexError) as e:
                    continue
        
        return detections
    
    def linear_interpolate_detections(self, detections: List[Dict], 
                                    frame_indices: List[int], 
                                    interval: int = 10) -> List[Dict]:
        """线性推理中间帧的检测结果"""
        interpolated_detections = []
        
        # 按类别和置信度分组检测结果
        detection_groups = {}
        for det in detections:
            key = f"{det['class_name']}_{det['confidence']:.2f}"
            if key not in detection_groups:
                detection_groups[key] = []
            detection_groups[key].append(det)
        
        # 对每个检测组进行线性插值
        for group_key, group_detections in detection_groups.items():
            if len(group_detections) < 2:
                # 如果只有一个检测，直接添加
                for det in group_detections:
                    interpolated_detections.append(det)
                continue
            
            # 按frame_idx排序（兼容新的多视频格式）
            group_detections.sort(key=lambda x: x.get('frame_idx', x.get('video_frame_idx', 0)))
            
            # 在相邻检测之间进行线性插值
            for i in range(len(group_detections) - 1):
                det1 = group_detections[i]
                det2 = group_detections[i + 1]
                
                # 添加第一个检测
                interpolated_detections.append(det1)
                
                # 计算中间帧
                start_frame = det1['original_frame_idx']
                end_frame = det2['original_frame_idx']
                
                # 线性插值中间帧
                for frame_idx in range(start_frame + 1, end_frame):
                    # 计算插值权重
                    total_frames = end_frame - start_frame
                    current_offset = frame_idx - start_frame
                    weight = current_offset / total_frames
                    
                    # 线性插值坐标 (使用校正后的坐标)
                    interp_x = det1['x_center'] + weight * (det2['x_center'] - det1['x_center'])
                    interp_y = det1['y_center'] + weight * (det2['y_center'] - det1['y_center'])
                    interp_w = det1['width'] + weight * (det2['width'] - det1['width'])
                    interp_h = det1['height'] + weight * (det2['height'] - det1['height'])
                    interp_conf = det1['confidence'] + weight * (det2['confidence'] - det1['confidence'])
                    
                    # 创建插值检测结果
                    seq_info = self.sequences[det1.get('seq_id', 'data02')]
                    abs_w = interp_w * seq_info['size'][0]
                    abs_h = interp_h * seq_info['size'][1]
                    
                    interpolated_det = {
                        'original_frame_idx': frame_idx,
                        'class_name': det1['class_name'],
                        'x_center': interp_x,
                        'y_center': interp_y,
                        'width': interp_w,
                        'height': interp_h,
                        'confidence': interp_conf,
                        'abs_w': abs_w,
                        'abs_h': abs_h,
                        'interpolated': True
                    }
                    interpolated_detections.append(interpolated_det)
            
            # 添加最后一个检测
            if group_detections:
                interpolated_detections.append(group_detections[-1])
        
        return interpolated_detections

    def detect_sequence_with_linear_interpolation(self, seq_id: str,
                                                interval: int = 10,
                                                frames_per_video: int = 5,
                                                test_ratio: float = 0.05) -> List[Dict]:
        """检测序列并进行线性推理"""
        logger.info(f"开始检测序列 {seq_id} - 隔{interval}取1，每{frames_per_video}帧检测...")
        logger.info(f"测试前{test_ratio*100:.1f}%的帧")
        logger.info(f"调试: 检查检测逻辑和模型输出")

        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        all_image_files = self.get_image_files(images_dir, seq_info)

        # 先取前5%的帧
        test_frame_count = max(10, int(len(all_image_files) * test_ratio))  # 至少10帧
        test_image_files = all_image_files[:test_frame_count]

        # 从前5%的帧中隔10取1
        selected_indices = list(range(0, len(test_image_files), interval))
        logger.info(f"总帧数: {len(all_image_files)}, 前5%: {len(test_image_files)}帧, 隔10取1后: {len(selected_indices)}帧")

        all_detections = []

        # 创建多个5帧视频进行批量检测
        video_groups = []
        all_frame_indices = []

        # 每5帧创建一个视频，收集多个视频
        for start_idx in range(0, len(selected_indices), frames_per_video):
            end_idx = min(start_idx + frames_per_video, len(selected_indices))
            if end_idx - start_idx < frames_per_video:
                continue

            # 获取5帧的原始帧索引（相对于前5%的帧）
            frame_indices = [selected_indices[i] for i in range(start_idx, end_idx)]

            # 创建5帧视频（使用前5%的图像文件）
            temp_video_path = self.create_5frame_video_from_images(seq_id, frame_indices, image_files=test_image_files)

            if temp_video_path:
                video_groups.append(temp_video_path)
                all_frame_indices.append(frame_indices)

                # 限制最多3个视频一组（与训练时一致）
                if len(video_groups) >= 3:
                    break

        # 如果有视频，进行多视频检测
        if video_groups:
            logger.info(f"检测{len(video_groups)}个5帧视频: 帧 {all_frame_indices}")

            # 进行多视频检测
            detections = self.detect_multi_5frame_videos(video_groups, seq_id, all_frame_indices)

            # 添加序列信息
            for det in detections:
                det['seq_id'] = seq_id

            # 对每个视频的检测结果进行线性推理
            for video_idx, frame_indices in enumerate(all_frame_indices):
                # 获取该视频的检测结果
                video_detections = [det for det in detections if det.get('video_idx', 0) == video_idx]

                # 线性推理中间帧
                interpolated_detections = self.linear_interpolate_detections(
                    video_detections, frame_indices, interval
                )

                all_detections.extend(interpolated_detections)

            # 清理临时文件
            for temp_video_path in video_groups:
                try:
                    os.remove(temp_video_path)
                except:
                    pass

        logger.info(f"序列 {seq_id} 检测完成，共检测到 {len(all_detections)} 个目标")
        return all_detections

    def save_detection_results(self, seq_id: str, detections: List[Dict],
                             output_dir: str = "results/interval_5frame_detection"):
        """保存检测结果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 保存JSON格式结果
        json_file = output_path / f"{seq_id}_detections.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(detections, f, ensure_ascii=False, indent=2)

        # 保存YOLO格式结果
        yolo_dir = output_path / "yolo_format" / seq_id
        yolo_dir.mkdir(parents=True, exist_ok=True)

        # 按帧组织检测结果
        frame_detections = {}
        for det in detections:
            frame_idx = det['original_frame_idx']
            if frame_idx not in frame_detections:
                frame_detections[frame_idx] = []
            frame_detections[frame_idx].append(det)

        # 保存每帧的检测结果
        for frame_idx, frame_dets in frame_detections.items():
            yolo_file = yolo_dir / f"{frame_idx}.txt"
            with open(yolo_file, 'w') as f:
                for det in frame_dets:
                    # 获取类别ID
                    class_id = None
                    for cid, cname in self.class_map.items():
                        if cname == det['class_name']:
                            class_id = cid
                            break

                    if class_id is not None:
                        line = f"{class_id} {det['x_center']:.6f} {det['y_center']:.6f} {det['width']:.6f} {det['height']:.6f}\n"
                        f.write(line)

        logger.info(f"检测结果保存到: {output_path}")

def main():
    """主函数"""
    print("=== 隔10取1，五帧视频检测器 + 线性推理 ===")
    print("🎯 隔10帧取1帧，每5帧检测")
    print("📹 线性推理中间帧检测结果")
    print("🔧 完全按照用户要求实现")
    print("💡 检测关键帧 + 推理全部帧")

    # 检查模型
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/interval_5frame_video_lora"

    if not os.path.exists(lora_model_path):
        print(f"\n❌ 5帧视频LoRA模型不存在: {lora_model_path}")
        print("请先运行: python src/interval_5frame_video_trainer.py")
        return False

    # 创建检测器
    detector = Interval5FrameVideoDetector(base_model_path, lora_model_path)

    # 检测所有small文件夹的序列
    test_sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data24', 'data25', 'data26']

    for seq_id in test_sequences:
        print(f"\n🔍 检测序列: {seq_id} (前10%帧)")
        detections = detector.detect_sequence_with_linear_interpolation(seq_id, test_ratio=0.10)
        detector.save_detection_results(seq_id, detections)

        # 统计检测结果
        direct_detections = [d for d in detections if not d.get('interpolated', False)]
        interpolated_detections = [d for d in detections if d.get('interpolated', False)]

        print(f"✅ {seq_id}: 总检测 {len(detections)} 个目标")
        print(f"   - 直接检测: {len(direct_detections)} 个")
        print(f"   - 线性推理: {len(interpolated_detections)} 个")

    print(f"\n🎉 隔10取1，五帧视频检测 + 线性推理完成！")
    print(f"📁 结果保存到: results/interval_5frame_detection/")
    print(f"🚀 完全按照用户要求：隔10取1，五帧检测，线性推理")

if __name__ == "__main__":
    main()
