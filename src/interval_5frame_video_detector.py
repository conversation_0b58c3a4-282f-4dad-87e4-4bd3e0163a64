#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
隔10取1，五帧视频检测器 + 线性推理
按照用户要求：隔10帧取1帧，每5帧检测，然后线性推理中间帧
"""

import os
import sys
import json
import torch
import logging
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PIL import Image
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
import re
from qwen_vl_utils import process_vision_info

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Interval5FrameVideoDetector:
    """隔10取1，五帧视频检测器 + 线性推理"""
    
    def __init__(self, base_model_path: str, lora_model_path: str):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        logger.info(f"5帧视频LoRA模型: {lora_model_path}")
        
        # 加载基础模型
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # 加载5帧视频LoRA微调模型
        logger.info("加载隔10取1，五帧视频LoRA模型...")
        self.model = PeftModel.from_pretrained(self.model, lora_model_path)
        logger.info("隔10取1，五帧视频检测器加载完成")
        
        # 序列信息
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def create_5frame_video_from_images(self, seq_id: str, frame_indices: List[int], 
                                      fps: int = 10) -> str:
        """从指定帧索引创建5帧视频文件"""
        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        all_image_files = self.get_image_files(images_dir, seq_info)
        
        # 选择指定的帧
        selected_files = []
        for idx in frame_indices:
            if 0 <= idx < len(all_image_files):
                selected_files.append(all_image_files[idx])
        
        if len(selected_files) != 5:
            return None
        
        # 创建临时视频文件
        temp_video_path = f"temp_5frame_{seq_id}_{frame_indices[0]}_{frame_indices[-1]}.mp4"
        
        # 获取图像尺寸
        first_img = cv2.imread(str(selected_files[0]))
        height, width = first_img.shape[:2]
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))
        
        for img_file in selected_files:
            img = cv2.imread(str(img_file))
            if img is not None:
                video_writer.write(img)
        
        video_writer.release()
        return temp_video_path
    
    def detect_5frame_video(self, video_path: str, seq_id: str, 
                          frame_indices: List[int]) -> List[Dict]:
        """检测5帧视频"""
        try:
            # 构建检测消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video",
                            "video": video_path
                        },
                        {
                            "type": "text",
                            "text": f"""你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。

视频信息:
- 序列: {seq_id}
- 采样方式: 隔10帧取1帧
- 帧数: 5帧
- 原始帧索引: {frame_indices}

请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
video_frame_idx class_name x_center y_center width height confidence

注意：
1. video_frame_idx为视频中的帧索引（0-4）
2. 坐标为相对坐标（0-1之间）
3. 只检测尺寸在3-80像素之间的弱小目标
4. 利用5帧视频的时序信息提高检测精度
5. confidence为检测置信度（0-1之间）"""
                        }
                    ]
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 使用process_vision_info处理视频
            image_inputs, video_inputs = process_vision_info(messages)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            inputs = inputs.to(self.device)
            
            # 生成检测结果
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=256,
                    do_sample=False,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            # 解码结果
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            # 解析检测结果
            detections = self.parse_5frame_detection_output(output_text, seq_id, frame_indices)
            
            return detections
            
        except Exception as e:
            logger.error(f"5帧视频检测失败: {e}")
            return []
    
    def parse_5frame_detection_output(self, output_text: str, seq_id: str, 
                                    frame_indices: List[int]) -> List[Dict]:
        """解析5帧检测输出"""
        detections = []
        lines = output_text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 解析检测结果行
            parts = line.split()
            if len(parts) >= 6:
                try:
                    video_frame_idx = int(parts[0])
                    class_name = parts[1]
                    x_center = float(parts[2])
                    y_center = float(parts[3])
                    width = float(parts[4])
                    height = float(parts[5])
                    confidence = float(parts[6]) if len(parts) > 6 else 1.0
                    
                    # 验证video_frame_idx范围
                    if 0 <= video_frame_idx < 5:
                        # 转换为原始帧索引
                        original_frame_idx = frame_indices[video_frame_idx]

                        # 获取对应的图像文件名作为frame_id
                        seq_info = self.sequences[seq_id]
                        images_dir = Path(f"dataset/images/{seq_id}")
                        all_image_files = self.get_image_files(images_dir, seq_info)
                        if original_frame_idx < len(all_image_files):
                            frame_id = all_image_files[original_frame_idx].stem
                        else:
                            frame_id = str(original_frame_idx)
                        
                        # 验证坐标范围
                        if 0 <= x_center <= 1 and 0 <= y_center <= 1 and 0 < width <= 1 and 0 < height <= 1:
                            # 验证是否为弱小目标
                            seq_info = self.sequences[seq_id]
                            abs_w = width * seq_info['size'][0]
                            abs_h = height * seq_info['size'][1]
                            
                            if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                                detections.append({
                                    'video_frame_idx': video_frame_idx,
                                    'original_frame_idx': original_frame_idx,
                                    'frame_id': frame_id,  # 添加正确的frame_id
                                    'class_name': class_name,
                                    'x_center': x_center,
                                    'y_center': y_center,
                                    'width': width,
                                    'height': height,
                                    'confidence': confidence,
                                    'abs_w': abs_w,
                                    'abs_h': abs_h
                                })
                
                except (ValueError, IndexError) as e:
                    continue
        
        return detections
    
    def linear_interpolate_detections(self, detections: List[Dict], 
                                    frame_indices: List[int], 
                                    interval: int = 10) -> List[Dict]:
        """线性推理中间帧的检测结果"""
        interpolated_detections = []
        
        # 按类别和置信度分组检测结果
        detection_groups = {}
        for det in detections:
            key = f"{det['class_name']}_{det['confidence']:.2f}"
            if key not in detection_groups:
                detection_groups[key] = []
            detection_groups[key].append(det)
        
        # 对每个检测组进行线性插值
        for group_key, group_detections in detection_groups.items():
            if len(group_detections) < 2:
                # 如果只有一个检测，直接添加
                for det in group_detections:
                    interpolated_detections.append(det)
                continue
            
            # 按video_frame_idx排序
            group_detections.sort(key=lambda x: x['video_frame_idx'])
            
            # 在相邻检测之间进行线性插值
            for i in range(len(group_detections) - 1):
                det1 = group_detections[i]
                det2 = group_detections[i + 1]
                
                # 添加第一个检测
                interpolated_detections.append(det1)
                
                # 计算中间帧
                start_frame = det1['original_frame_idx']
                end_frame = det2['original_frame_idx']
                
                # 线性插值中间帧
                for frame_idx in range(start_frame + 1, end_frame):
                    # 计算插值权重
                    total_frames = end_frame - start_frame
                    current_offset = frame_idx - start_frame
                    weight = current_offset / total_frames
                    
                    # 线性插值坐标
                    interp_x = det1['x_center'] + weight * (det2['x_center'] - det1['x_center'])
                    interp_y = det1['y_center'] + weight * (det2['y_center'] - det1['y_center'])
                    interp_w = det1['width'] + weight * (det2['width'] - det1['width'])
                    interp_h = det1['height'] + weight * (det2['height'] - det1['height'])
                    interp_conf = det1['confidence'] + weight * (det2['confidence'] - det1['confidence'])
                    
                    # 创建插值检测结果
                    seq_info = self.sequences[det1.get('seq_id', 'data02')]
                    abs_w = interp_w * seq_info['size'][0]
                    abs_h = interp_h * seq_info['size'][1]
                    
                    interpolated_det = {
                        'original_frame_idx': frame_idx,
                        'class_name': det1['class_name'],
                        'x_center': interp_x,
                        'y_center': interp_y,
                        'width': interp_w,
                        'height': interp_h,
                        'confidence': interp_conf,
                        'abs_w': abs_w,
                        'abs_h': abs_h,
                        'interpolated': True
                    }
                    interpolated_detections.append(interpolated_det)
            
            # 添加最后一个检测
            if group_detections:
                interpolated_detections.append(group_detections[-1])
        
        return interpolated_detections

    def detect_sequence_with_linear_interpolation(self, seq_id: str,
                                                interval: int = 10,
                                                frames_per_video: int = 5,
                                                test_ratio: float = 0.05) -> List[Dict]:
        """检测序列并进行线性推理"""
        logger.info(f"开始检测序列 {seq_id} - 隔{interval}取1，每{frames_per_video}帧检测...")
        logger.info(f"测试前{test_ratio*100:.1f}%的帧")

        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        all_image_files = self.get_image_files(images_dir, seq_info)

        # 隔10取1：从第0帧开始，每隔10帧取1帧
        selected_indices = list(range(0, len(all_image_files), interval))

        # 只测试前5%的帧
        test_frame_count = max(1, int(len(selected_indices) * test_ratio))
        test_selected_indices = selected_indices[:test_frame_count]
        logger.info(f"总帧数: {len(all_image_files)}, 隔10取1后: {len(selected_indices)}帧, 测试前5%: {len(test_selected_indices)}帧")

        all_detections = []

        # 每5帧进行一次检测（只处理前5%）
        for start_idx in range(0, len(test_selected_indices), frames_per_video):
            end_idx = min(start_idx + frames_per_video, len(test_selected_indices))
            if end_idx - start_idx < frames_per_video:
                continue

            # 获取5帧的原始帧索引
            frame_indices = [test_selected_indices[i] for i in range(start_idx, end_idx)]

            # 创建5帧视频
            temp_video_path = self.create_5frame_video_from_images(seq_id, frame_indices)

            if temp_video_path:
                logger.info(f"检测5帧视频: 帧 {frame_indices}")

                # 进行5帧检测
                detections = self.detect_5frame_video(temp_video_path, seq_id, frame_indices)

                # 添加序列信息
                for det in detections:
                    det['seq_id'] = seq_id

                # 线性推理中间帧
                interpolated_detections = self.linear_interpolate_detections(
                    detections, frame_indices, interval
                )

                all_detections.extend(interpolated_detections)

                # 清理临时文件
                try:
                    os.remove(temp_video_path)
                except:
                    pass

        logger.info(f"序列 {seq_id} 检测完成，共检测到 {len(all_detections)} 个目标")
        return all_detections

    def save_detection_results(self, seq_id: str, detections: List[Dict],
                             output_dir: str = "results/interval_5frame_detection"):
        """保存检测结果"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 保存JSON格式结果
        json_file = output_path / f"{seq_id}_detections.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(detections, f, ensure_ascii=False, indent=2)

        # 保存YOLO格式结果
        yolo_dir = output_path / "yolo_format" / seq_id
        yolo_dir.mkdir(parents=True, exist_ok=True)

        # 按帧组织检测结果
        frame_detections = {}
        for det in detections:
            frame_idx = det['original_frame_idx']
            if frame_idx not in frame_detections:
                frame_detections[frame_idx] = []
            frame_detections[frame_idx].append(det)

        # 保存每帧的检测结果
        for frame_idx, frame_dets in frame_detections.items():
            yolo_file = yolo_dir / f"{frame_idx}.txt"
            with open(yolo_file, 'w') as f:
                for det in frame_dets:
                    # 获取类别ID
                    class_id = None
                    for cid, cname in self.class_map.items():
                        if cname == det['class_name']:
                            class_id = cid
                            break

                    if class_id is not None:
                        line = f"{class_id} {det['x_center']:.6f} {det['y_center']:.6f} {det['width']:.6f} {det['height']:.6f}\n"
                        f.write(line)

        logger.info(f"检测结果保存到: {output_path}")

def main():
    """主函数"""
    print("=== 隔10取1，五帧视频检测器 + 线性推理 ===")
    print("🎯 隔10帧取1帧，每5帧检测")
    print("📹 线性推理中间帧检测结果")
    print("🔧 完全按照用户要求实现")
    print("💡 检测关键帧 + 推理全部帧")

    # 检查模型
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/interval_5frame_video_lora"

    if not os.path.exists(lora_model_path):
        print(f"\n❌ 5帧视频LoRA模型不存在: {lora_model_path}")
        print("请先运行: python src/interval_5frame_video_trainer.py")
        return False

    # 创建检测器
    detector = Interval5FrameVideoDetector(base_model_path, lora_model_path)

    # 检测所有序列
    test_sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']

    for seq_id in test_sequences:
        print(f"\n🔍 检测序列: {seq_id}")
        detections = detector.detect_sequence_with_linear_interpolation(seq_id)
        detector.save_detection_results(seq_id, detections)

        # 统计检测结果
        direct_detections = [d for d in detections if not d.get('interpolated', False)]
        interpolated_detections = [d for d in detections if d.get('interpolated', False)]

        print(f"✅ {seq_id}: 总检测 {len(detections)} 个目标")
        print(f"   - 直接检测: {len(direct_detections)} 个")
        print(f"   - 线性推理: {len(interpolated_detections)} 个")

    print(f"\n🎉 隔10取1，五帧视频检测 + 线性推理完成！")
    print(f"📁 结果保存到: results/interval_5frame_detection/")
    print(f"🚀 完全按照用户要求：隔10取1，五帧检测，线性推理")

if __name__ == "__main__":
    main()
