#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
隔10取1，五帧视频检测评估器
评估基于5帧视频微调和线性推理的检测性能
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Interval5FrameEvaluator:
    """隔10取1，五帧视频检测评估器"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def load_ground_truth(self, seq_id: str) -> Dict[str, List[Dict]]:
        """加载真实标注"""
        gt_annotations = {}
        labels_dir = Path(f"dataset/labels/{seq_id}")
        
        if not labels_dir.exists():
            return {}
        
        seq_info = self.sequences[seq_id]
        
        for label_file in labels_dir.glob("*.txt"):
            frame_id = label_file.stem
            annotations = []
            
            with open(label_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) != 5:
                        continue
                    
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    abs_w = width * seq_info['size'][0]
                    abs_h = height * seq_info['size'][1]
                    
                    # 检查是否为弱小目标（3-80像素）
                    if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                        class_name = self.class_map.get(str(class_id), 'unknown')
                        annotations.append({
                            'class_id': class_id,
                            'class_name': class_name,
                            'x_center': x_center,
                            'y_center': y_center,
                            'width': width,
                            'height': height,
                            'abs_w': abs_w,
                            'abs_h': abs_h
                        })
            
            if annotations:
                gt_annotations[str(int(frame_id))] = annotations
        
        return gt_annotations
    
    def load_predictions(self, seq_id: str, results_dir: str = "results/interval_5frame_detection") -> List[Dict]:
        """加载预测结果"""
        pred_file = Path(results_dir) / f"{seq_id}_detections.json"
        
        if not pred_file.exists():
            logger.warning(f"预测文件不存在: {pred_file}")
            return []
        
        with open(pred_file, 'r', encoding='utf-8') as f:
            predictions = json.load(f)
        
        return predictions
    
    def calculate_iou(self, box1: Dict, box2: Dict) -> float:
        """计算IoU"""
        x1_center, y1_center, w1, h1 = box1['x_center'], box1['y_center'], box1['width'], box1['height']
        x2_center, y2_center, w2, h2 = box2['x_center'], box2['y_center'], box2['width'], box2['height']
        
        # 转换为左上角和右下角坐标
        x1_min, y1_min = x1_center - w1/2, y1_center - h1/2
        x1_max, y1_max = x1_center + w1/2, y1_center + h1/2
        
        x2_min, y2_min = x2_center - w2/2, y2_center - h2/2
        x2_max, y2_max = x2_center + w2/2, y2_center + h2/2
        
        # 计算交集
        inter_x_min = max(x1_min, x2_min)
        inter_y_min = max(y1_min, y2_min)
        inter_x_max = min(x1_max, x2_max)
        inter_y_max = min(y1_max, y2_max)
        
        if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
            return 0.0
        
        inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)
        
        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def evaluate_sequence(self, seq_id: str, iou_threshold: float = 0.5) -> Dict[str, Any]:
        """评估单个序列"""
        logger.info(f"评估序列 {seq_id}...")
        
        # 加载真实标注和预测结果
        gt_annotations = self.load_ground_truth(seq_id)
        predictions = self.load_predictions(seq_id)
        
        # 按帧组织预测结果
        pred_by_frame = {}
        for pred in predictions:
            frame_idx = str(pred['original_frame_idx'])
            if frame_idx not in pred_by_frame:
                pred_by_frame[frame_idx] = []
            pred_by_frame[frame_idx].append(pred)
        
        # 计算指标
        total_gt = 0
        total_pred = 0
        true_positives = 0
        
        # 统计所有真实目标
        for frame_id, gt_list in gt_annotations.items():
            total_gt += len(gt_list)
        
        # 统计所有预测目标
        total_pred = len(predictions)
        
        # 计算匹配
        for frame_id, gt_list in gt_annotations.items():
            pred_list = pred_by_frame.get(frame_id, []).copy()
            
            # 为每个真实目标找最佳匹配
            for gt_box in gt_list:
                best_iou = 0
                best_match = None
                
                for pred_box in pred_list:
                    if pred_box['class_name'] == gt_box['class_name']:
                        iou = self.calculate_iou(gt_box, pred_box)
                        if iou > best_iou:
                            best_iou = iou
                            best_match = pred_box
                
                if best_iou >= iou_threshold:
                    true_positives += 1
                    # 移除已匹配的预测，避免重复匹配
                    if best_match in pred_list:
                        pred_list.remove(best_match)
        
        # 计算指标
        precision = true_positives / total_pred if total_pred > 0 else 0
        recall = true_positives / total_gt if total_gt > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        # 统计直接检测和插值检测
        direct_detections = [d for d in predictions if not d.get('interpolated', False)]
        interpolated_detections = [d for d in predictions if d.get('interpolated', False)]
        
        result = {
            'sequence': seq_id,
            'total_gt': total_gt,
            'total_pred': total_pred,
            'direct_detections': len(direct_detections),
            'interpolated_detections': len(interpolated_detections),
            'true_positives': true_positives,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'iou_threshold': iou_threshold
        }
        
        logger.info(f"{seq_id}: GT={total_gt}, Pred={total_pred} (直接:{len(direct_detections)}, 插值:{len(interpolated_detections)}), TP={true_positives}")
        logger.info(f"{seq_id}: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}")
        
        return result
    
    def evaluate_all_sequences(self, test_sequences: List[str] = None) -> Dict[str, Any]:
        """评估所有序列"""
        if test_sequences is None:
            test_sequences = ['data02', 'data19']  # 测试序列
        
        logger.info("开始评估隔10取1，五帧视频检测性能...")
        
        all_results = []
        total_gt = 0
        total_pred = 0
        total_tp = 0
        
        for seq_id in test_sequences:
            result = self.evaluate_sequence(seq_id)
            all_results.append(result)
            
            total_gt += result['total_gt']
            total_pred += result['total_pred']
            total_tp += result['true_positives']
        
        # 计算总体指标
        overall_precision = total_tp / total_pred if total_pred > 0 else 0
        overall_recall = total_tp / total_gt if total_gt > 0 else 0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
        
        summary = {
            'method': 'Interval 5-Frame Video Detection + Linear Interpolation',
            'description': '隔10取1，每5帧检测 + 线性推理',
            'sequences': test_sequences,
            'individual_results': all_results,
            'overall': {
                'total_gt': total_gt,
                'total_pred': total_pred,
                'total_tp': total_tp,
                'precision': overall_precision,
                'recall': overall_recall,
                'f1': overall_f1
            }
        }
        
        return summary
    
    def save_evaluation_results(self, results: Dict[str, Any], output_file: str = "results/interval_5frame_evaluation.json"):
        """保存评估结果"""
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"评估结果保存到: {output_path}")

def main():
    """主函数"""
    print("=== 隔10取1，五帧视频检测评估器 ===")
    print("🎯 评估基于5帧视频微调 + 线性推理的检测性能")
    print("📹 隔10取1，每5帧检测，线性推理中间帧")
    print("🔧 计算精确度、召回率和F1分数")
    
    # 创建评估器
    evaluator = Interval5FrameEvaluator()
    
    # 评估所有序列
    results = evaluator.evaluate_all_sequences()
    
    # 保存结果
    evaluator.save_evaluation_results(results)
    
    # 显示结果
    print(f"\n📊 隔10取1，五帧视频检测评估结果:")
    print(f"🎯 方法: {results['method']}")
    print(f"📝 描述: {results['description']}")
    print(f"📈 总体性能:")
    print(f"   - 真实目标: {results['overall']['total_gt']}")
    print(f"   - 预测目标: {results['overall']['total_pred']}")
    print(f"   - 正确检测: {results['overall']['total_tp']}")
    print(f"   - 精确度: {results['overall']['precision']:.3f}")
    print(f"   - 召回率: {results['overall']['recall']:.3f}")
    print(f"   - F1分数: {results['overall']['f1']:.3f}")
    
    print(f"\n📋 各序列详细结果:")
    for result in results['individual_results']:
        print(f"   {result['sequence']}: F1={result['f1']:.3f} (P={result['precision']:.3f}, R={result['recall']:.3f})")
        print(f"      检测: {result['total_pred']} (直接:{result['direct_detections']}, 插值:{result['interpolated_detections']})")
    
    print(f"\n💾 详细结果保存到: results/interval_5frame_evaluation.json")

if __name__ == "__main__":
    main()
