#!/usr/bin/env python3
"""
简化的检测结果分析器 - 分析检测结果与真实标注的差异
"""

import json
import os
import numpy as np
from pathlib import Path

def calculate_iou(box1, box2):
    """计算两个边界框的IoU"""
    # 转换为边界框坐标
    x1_1 = box1['x_center'] - box1['width'] / 2
    y1_1 = box1['y_center'] - box1['height'] / 2
    x2_1 = box1['x_center'] + box1['width'] / 2
    y2_1 = box1['y_center'] + box1['height'] / 2
    
    x1_2 = box2['x_center'] - box2['width'] / 2
    y1_2 = box2['y_center'] - box2['height'] / 2
    x2_2 = box2['x_center'] + box2['width'] / 2
    y2_2 = box2['y_center'] + box2['height'] / 2
    
    # 计算交集
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)
    
    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0
        
    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
    
    # 计算并集
    area1 = box1['width'] * box1['height']
    area2 = box2['width'] * box2['height']
    union_area = area1 + area2 - inter_area
    
    if union_area <= 0:
        return 0.0
        
    return inter_area / union_area

def load_gt_annotation(seq_id, frame_idx):
    """加载真实标注"""
    label_file = Path(f"dataset/labels/{seq_id}/{frame_idx}.txt")
    
    if not label_file.exists():
        return []
        
    annotations = []
    try:
        with open(label_file, 'r') as f:
            lines = f.readlines()
            
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            parts = line.split()
            if len(parts) >= 5:
                class_id = int(parts[0])
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                
                annotations.append({
                    'class_id': class_id,
                    'x_center': x_center,
                    'y_center': y_center,
                    'width': width,
                    'height': height
                })
                
    except Exception as e:
        print(f"读取标注文件失败 {label_file}: {e}")
        
    return annotations

def load_detections(seq_id):
    """加载检测结果"""
    detection_file = Path(f"results/interval_5frame_detection/{seq_id}_detections.json")
    
    if not detection_file.exists():
        print(f"检测结果文件不存在: {detection_file}")
        return {}
        
    try:
        with open(detection_file, 'r') as f:
            detections_list = json.load(f)
            
        # 按帧索引组织检测结果
        detections = {}
        for det in detections_list:
            frame_idx = det['original_frame_idx']
            if frame_idx not in detections:
                detections[frame_idx] = []
            detections[frame_idx].append(det)
            
        return detections
        
    except Exception as e:
        print(f"读取检测结果失败 {detection_file}: {e}")
        return {}

def analyze_sequence(seq_id, max_frames=10):
    """分析单个序列的前几帧"""
    print(f"\n=== 分析序列 {seq_id} ===")
    
    detections = load_detections(seq_id)
    
    for frame_idx in range(max_frames):
        gt_boxes = load_gt_annotation(seq_id, frame_idx)
        det_boxes = detections.get(frame_idx, [])
        
        print(f"\n帧 {frame_idx}:")
        print(f"  真实标注: {len(gt_boxes)} 个目标")
        for i, gt in enumerate(gt_boxes):
            print(f"    GT{i}: center=({gt['x_center']:.6f}, {gt['y_center']:.6f}), size=({gt['width']:.6f}, {gt['height']:.6f})")
            
        print(f"  检测结果: {len(det_boxes)} 个目标")
        for i, det in enumerate(det_boxes):
            print(f"    DET{i}: center=({det['x_center']:.6f}, {det['y_center']:.6f}), size=({det['width']:.6f}, {det['height']:.6f})")
            
        # 计算IoU
        if gt_boxes and det_boxes:
            for i, gt in enumerate(gt_boxes):
                for j, det in enumerate(det_boxes):
                    iou = calculate_iou(gt, det)
                    distance = np.sqrt((gt['x_center'] - det['x_center'])**2 + (gt['y_center'] - det['y_center'])**2)
                    print(f"    GT{i} vs DET{j}: IoU={iou:.6f}, 距离={distance:.6f}")

def main():
    """主函数"""
    print("=== 检测结果分析器 ===")
    print("🔍 分析检测结果与真实标注的差异")
    
    # 分析几个有目标的序列
    sequences = ['data01', 'data02']
    
    for seq_id in sequences:
        analyze_sequence(seq_id, max_frames=5)
    
    print("\n✅ 分析完成!")

if __name__ == "__main__":
    main()
