#!/usr/bin/env python3
"""
调试模型输出 - 分析模型的实际输出内容
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
from qwen_vl_utils import process_vision_info
import cv2
import numpy as np

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelOutputDebugger:
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # 加载模型和处理器
        logger.info(f"加载LoRA模型: {model_path}")

        # 先加载基础模型
        base_model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            "qwen2.5-vl-7b",
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )

        # 加载LoRA适配器
        self.model = PeftModel.from_pretrained(base_model, model_path)
        self.processor = AutoProcessor.from_pretrained("qwen2.5-vl-7b")
        
        # 序列信息
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        }
        
    def create_test_video(self, seq_id: str, start_frame: int = 0, num_frames: int = 5) -> str:
        """创建测试视频"""
        seq_info = self.sequences[seq_id]
        img_dir = Path(f"dataset/images/{seq_id}")
        
        # 获取图像文件
        if seq_info['format'] == '.bmp':
            img_files = sorted(img_dir.glob("*.bmp"))
        else:
            img_files = sorted(img_dir.glob("*.jpg"))
            
        # 选择帧
        selected_files = img_files[start_frame:start_frame+num_frames]
        if len(selected_files) < num_frames:
            # 用最后一帧补齐
            while len(selected_files) < num_frames:
                selected_files.append(selected_files[-1])
                
        # 创建临时视频
        temp_video_path = f"temp_debug_video_{seq_id}.mp4"
        
        # 读取第一帧获取尺寸
        first_img = cv2.imread(str(selected_files[0]))
        height, width = first_img.shape[:2]
        
        # 创建视频
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(temp_video_path, fourcc, 10, (width, height))
        
        for img_file in selected_files:
            img = cv2.imread(str(img_file))
            video_writer.write(img)
            
        video_writer.release()
        
        logger.info(f"创建测试视频: {temp_video_path}")
        return temp_video_path
        
    def test_model_output(self, seq_id: str, start_frame: int = 0):
        """测试模型输出"""
        logger.info(f"测试序列 {seq_id} 从帧 {start_frame} 开始的5帧...")
        
        # 创建测试视频
        video_path = self.create_test_video(seq_id, start_frame)
        
        try:
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video",
                            "video": video_path
                        },
                        {
                            "type": "text",
                            "text": f"""你是专业的红外弱小目标视频检测专家。请检测这个5帧视频片段中的弱小目标。

视频信息:
- 序列: {seq_id}
- 采样方式: 隔10帧取1帧
- 帧数: 5帧
- 原始帧范围: {start_frame}-{start_frame+40}

请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
video_frame_idx class_name x_center y_center width height confidence

注意：
1. video_frame_idx为视频中的帧索引（0-4）
2. 坐标为相对坐标（0-1之间）
3. 只检测尺寸在3-80像素之间的弱小目标
4. 利用5帧视频的时序信息提高检测精度
5. confidence为检测置信度（0-1之间）"""
                        }
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            image_inputs, video_inputs = process_vision_info(messages)
            
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            
            inputs = inputs.to(self.device)
            
            # 生成输出
            logger.info("开始生成...")
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=512,
                    do_sample=False,
                    temperature=0.1
                )
                
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            print(f"\n=== 序列 {seq_id} 帧 {start_frame}-{start_frame+4} ===")
            print("模型完整输出:")
            print("-" * 50)
            print(output_text)
            print("-" * 50)
            
            # 分析输出
            self.analyze_output(output_text, seq_id, start_frame)
            
        finally:
            # 清理临时文件
            if os.path.exists(video_path):
                os.remove(video_path)
                
    def analyze_output(self, output_text: str, seq_id: str, start_frame: int):
        """分析模型输出"""
        print("\n输出分析:")
        
        lines = output_text.strip().split('\n')
        valid_detections = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            print(f"  行{i+1}: '{line}'")
            
            # 尝试解析
            parts = line.split()
            if len(parts) >= 6:
                try:
                    video_frame_idx = int(parts[0])
                    class_name = parts[1]
                    x_center = float(parts[2])
                    y_center = float(parts[3])
                    width = float(parts[4])
                    height = float(parts[5])
                    confidence = float(parts[6]) if len(parts) > 6 else 1.0
                    
                    print(f"    ✓ 解析成功: frame={video_frame_idx}, class={class_name}")
                    print(f"      坐标: ({x_center:.6f}, {y_center:.6f}), 尺寸: ({width:.6f}, {height:.6f})")
                    print(f"      置信度: {confidence:.3f}")
                    
                    # 检查坐标范围
                    if 0 <= x_center <= 1 and 0 <= y_center <= 1:
                        print(f"      ✓ 坐标范围正确")
                    else:
                        print(f"      ✗ 坐标范围错误")
                        
                    valid_detections += 1
                    
                except ValueError as e:
                    print(f"    ✗ 解析失败: {e}")
            else:
                print(f"    ✗ 格式错误: 期望至少6个字段，得到{len(parts)}个")
                
        print(f"\n有效检测数量: {valid_detections}")
        
        # 加载真实标注进行对比
        self.compare_with_ground_truth(seq_id, start_frame, valid_detections)
        
    def compare_with_ground_truth(self, seq_id: str, start_frame: int, detected_count: int):
        """与真实标注对比"""
        print(f"\n真实标注对比:")
        
        total_gt = 0
        for frame_offset in range(5):
            frame_idx = start_frame + frame_offset * 10  # 隔10取1
            label_file = Path(f"dataset/labels/{seq_id}/{frame_idx}.txt")
            
            if label_file.exists():
                with open(label_file, 'r') as f:
                    lines = [line.strip() for line in f if line.strip()]
                    gt_count = len(lines)
                    total_gt += gt_count
                    
                    if gt_count > 0:
                        print(f"  帧{frame_idx}: {gt_count}个真实目标")
                        for line in lines:
                            parts = line.split()
                            if len(parts) >= 5:
                                print(f"    GT: class={parts[0]}, center=({parts[1]}, {parts[2]}), size=({parts[3]}, {parts[4]})")
                    else:
                        print(f"  帧{frame_idx}: 无目标")
            else:
                print(f"  帧{frame_idx}: 标注文件不存在")
                
        print(f"\n总计: 检测到{detected_count}个，真实{total_gt}个")

def main():
    """主函数"""
    print("=== 模型输出调试器 ===")
    
    # 使用微调后的模型
    model_path = "output/interval_5frame_video_lora"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return
        
    debugger = ModelOutputDebugger(model_path)
    
    # 测试几个样本
    test_cases = [
        ('data01', 0),   # data01的前5帧
        ('data01', 10),  # data01的第10-50帧
        ('data02', 0),   # data02的前5帧
    ]
    
    for seq_id, start_frame in test_cases:
        debugger.test_model_output(seq_id, start_frame)
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    main()
