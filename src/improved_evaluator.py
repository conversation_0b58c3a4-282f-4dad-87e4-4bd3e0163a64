#!/usr/bin/env python3
"""
改进的评估器 - 使用更合理的评估指标
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedEvaluator:
    def __init__(self):
        self.sequences = ['data01', 'data02', 'data04', 'data06', 'data07',
                         'data19', 'data23', 'data24', 'data25', 'data26']

    def extract_frame_index(self, filename, seq_id):
        """根据不同序列的命名格式提取帧索引"""
        try:
            # data01-data07: 简单数字命名
            if seq_id in ['data01', 'data02', 'data04', 'data06', 'data07']:
                return int(filename)

            # data19: merged_dataset_data_transform_1_01_1751_0250-1750_000001
            elif seq_id == 'data19':
                if filename.startswith('merged_dataset_data_transform_1_01_1751_0250-1750_'):
                    frame_str = filename.split('_')[-1]
                    return int(frame_str) - 1  # 转换为0-based索引

            # data23: 类似data19的格式
            elif seq_id == 'data23':
                if 'merged_dataset_data_transform' in filename:
                    parts = filename.split('_')
                    if len(parts) > 0:
                        frame_str = parts[-1]
                        return int(frame_str) - 1

            # data24: merged_dataset_data_transform_1_wg2022_ir_024_split_03_000001
            elif seq_id == 'data24':
                if filename.startswith('merged_dataset_data_transform_1_wg2022_ir_024_split_03_'):
                    frame_str = filename.split('_')[-1]
                    return int(frame_str) - 1

            # data25, data26: 类似data24的格式
            elif seq_id in ['data25', 'data26']:
                if 'merged_dataset_data_transform' in filename and 'wg2022_ir' in filename:
                    parts = filename.split('_')
                    if len(parts) > 0:
                        frame_str = parts[-1]
                        return int(frame_str) - 1

            return None

        except (ValueError, IndexError):
            return None
    
    def load_detections(self, seq_id: str) -> List[Dict]:
        """加载检测结果"""
        detection_file = Path(f"results/interval_5frame_detection/{seq_id}_detections.json")
        if not detection_file.exists():
            return []
        
        with open(detection_file, 'r') as f:
            return json.load(f)
    
    def load_ground_truth(self, seq_id: str) -> Dict[int, List[Dict]]:
        """加载真实标注"""
        label_dir = Path(f"dataset/labels/{seq_id}")
        ground_truth = {}
        
        for label_file in label_dir.glob("*.txt"):
            # 使用改进的帧索引提取函数
            frame_idx = self.extract_frame_index(label_file.stem, seq_id)
            if frame_idx is None:
                continue
            
            with open(label_file, 'r') as f:
                lines = [line.strip() for line in f if line.strip()]
            
            frame_gt = []
            for line in lines:
                parts = line.split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 映射类别
                    class_names = {0: 'drone', 1: 'car', 2: 'ship', 3: 'bus', 4: 'pedestrian', 5: 'cyclist'}
                    class_name = class_names.get(class_id, 'unknown')
                    
                    frame_gt.append({
                        'class_name': class_name,
                        'x_center': x_center,
                        'y_center': y_center,
                        'width': width,
                        'height': height
                    })
            
            if frame_gt:
                ground_truth[frame_idx] = frame_gt
        
        return ground_truth
    
    def calculate_distance(self, det: Dict, gt: Dict) -> float:
        """计算检测结果与真实标注的距离"""
        det_x, det_y = det['x_center'], det['y_center']
        gt_x, gt_y = gt['x_center'], gt['y_center']
        
        return np.sqrt((det_x - gt_x)**2 + (det_y - gt_y)**2)
    
    def calculate_iou(self, det: Dict, gt: Dict) -> float:
        """计算IoU"""
        # 检测框
        det_x1 = det['x_center'] - det['width'] / 2
        det_y1 = det['y_center'] - det['height'] / 2
        det_x2 = det['x_center'] + det['width'] / 2
        det_y2 = det['y_center'] + det['height'] / 2
        
        # 真实框
        gt_x1 = gt['x_center'] - gt['width'] / 2
        gt_y1 = gt['y_center'] - gt['height'] / 2
        gt_x2 = gt['x_center'] + gt['width'] / 2
        gt_y2 = gt['y_center'] + gt['height'] / 2
        
        # 交集
        inter_x1 = max(det_x1, gt_x1)
        inter_y1 = max(det_y1, gt_y1)
        inter_x2 = min(det_x2, gt_x2)
        inter_y2 = min(det_y2, gt_y2)
        
        if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
            return 0.0
        
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        
        # 并集
        det_area = det['width'] * det['height']
        gt_area = gt['width'] * gt['height']
        union_area = det_area + gt_area - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def evaluate_with_multiple_thresholds(self, seq_id: str) -> Dict:
        """使用多个阈值评估"""
        detections = self.load_detections(seq_id)
        ground_truth = self.load_ground_truth(seq_id)
        
        if not detections:
            return {
                'distance_metrics': {},
                'iou_metrics': {},
                'detection_count': 0,
                'gt_count': sum(len(gts) for gts in ground_truth.values())
            }
        
        # 距离阈值评估
        distance_thresholds = [0.05, 0.1, 0.15, 0.2, 0.25]
        distance_metrics = {}
        
        # IoU阈值评估
        iou_thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]
        iou_metrics = {}
        
        for dist_thresh in distance_thresholds:
            tp, fp, fn = self.calculate_metrics_by_distance(detections, ground_truth, dist_thresh)
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            distance_metrics[f'dist_{dist_thresh}'] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'tp': tp,
                'fp': fp,
                'fn': fn
            }
        
        for iou_thresh in iou_thresholds:
            tp, fp, fn = self.calculate_metrics_by_iou(detections, ground_truth, iou_thresh)
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            iou_metrics[f'iou_{iou_thresh}'] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'tp': tp,
                'fp': fp,
                'fn': fn
            }
        
        return {
            'distance_metrics': distance_metrics,
            'iou_metrics': iou_metrics,
            'detection_count': len(detections),
            'gt_count': sum(len(gts) for gts in ground_truth.values())
        }
    
    def calculate_metrics_by_distance(self, detections: List[Dict], ground_truth: Dict, threshold: float) -> Tuple[int, int, int]:
        """基于距离计算指标"""
        tp = 0
        fp = 0
        matched_gt = set()
        
        for det in detections:
            frame_idx = det['original_frame_idx']
            
            if frame_idx not in ground_truth:
                fp += 1
                continue
            
            best_distance = float('inf')
            best_gt_idx = -1
            
            for gt_idx, gt in enumerate(ground_truth[frame_idx]):
                if det['class_name'] == gt['class_name']:
                    distance = self.calculate_distance(det, gt)
                    if distance < best_distance:
                        best_distance = distance
                        best_gt_idx = gt_idx
            
            if best_distance <= threshold and (frame_idx, best_gt_idx) not in matched_gt:
                tp += 1
                matched_gt.add((frame_idx, best_gt_idx))
            else:
                fp += 1
        
        # 计算FN
        total_gt = sum(len(gts) for gts in ground_truth.values())
        fn = total_gt - tp
        
        return tp, fp, fn
    
    def calculate_metrics_by_iou(self, detections: List[Dict], ground_truth: Dict, threshold: float) -> Tuple[int, int, int]:
        """基于IoU计算指标"""
        tp = 0
        fp = 0
        matched_gt = set()
        
        for det in detections:
            frame_idx = det['original_frame_idx']
            
            if frame_idx not in ground_truth:
                fp += 1
                continue
            
            best_iou = 0
            best_gt_idx = -1
            
            for gt_idx, gt in enumerate(ground_truth[frame_idx]):
                if det['class_name'] == gt['class_name']:
                    iou = self.calculate_iou(det, gt)
                    if iou > best_iou:
                        best_iou = iou
                        best_gt_idx = gt_idx
            
            if best_iou >= threshold and (frame_idx, best_gt_idx) not in matched_gt:
                tp += 1
                matched_gt.add((frame_idx, best_gt_idx))
            else:
                fp += 1
        
        # 计算FN
        total_gt = sum(len(gts) for gts in ground_truth.values())
        fn = total_gt - tp
        
        return tp, fp, fn
    
    def evaluate_all_sequences(self) -> Dict:
        """评估所有序列"""
        all_results = {}
        
        for seq_id in self.sequences:
            logger.info(f"评估序列 {seq_id}")
            results = self.evaluate_with_multiple_thresholds(seq_id)
            all_results[seq_id] = results
        
        return all_results
    
    def print_results(self, results: Dict):
        """打印评估结果"""
        print("\n" + "="*100)
        print("📊 改进的检测评估结果")
        print("="*100)
        
        # 距离阈值结果
        print("\n🎯 基于距离阈值的评估:")
        print("-"*80)
        print(f"{'序列':<8} {'阈值':<8} {'精确度':<8} {'召回率':<8} {'F1分数':<8} {'TP':<6} {'FP':<6} {'FN':<6}")
        print("-"*80)
        
        for seq_id, seq_results in results.items():
            for metric_name, metrics in seq_results['distance_metrics'].items():
                threshold = metric_name.split('_')[1]
                print(f"{seq_id:<8} {threshold:<8} {metrics['precision']:<8.3f} {metrics['recall']:<8.3f} "
                      f"{metrics['f1']:<8.3f} {metrics['tp']:<6} {metrics['fp']:<6} {metrics['fn']:<6}")
        
        # IoU阈值结果
        print("\n🎯 基于IoU阈值的评估:")
        print("-"*80)
        print(f"{'序列':<8} {'阈值':<8} {'精确度':<8} {'召回率':<8} {'F1分数':<8} {'TP':<6} {'FP':<6} {'FN':<6}")
        print("-"*80)
        
        for seq_id, seq_results in results.items():
            for metric_name, metrics in seq_results['iou_metrics'].items():
                threshold = metric_name.split('_')[1]
                print(f"{seq_id:<8} {threshold:<8} {metrics['precision']:<8.3f} {metrics['recall']:<8.3f} "
                      f"{metrics['f1']:<8.3f} {metrics['tp']:<6} {metrics['fp']:<6} {metrics['fn']:<6}")
        
        # 总体统计
        print("\n📈 总体统计:")
        print("-"*50)
        total_detections = sum(seq_results['detection_count'] for seq_results in results.values())
        total_gt = sum(seq_results['gt_count'] for seq_results in results.values())
        print(f"总检测数: {total_detections}")
        print(f"总真实目标数: {total_gt}")
        
        # 最佳性能
        print("\n🏆 最佳性能 (距离阈值0.15):")
        best_metrics = self.calculate_overall_metrics(results, 'dist_0.15')
        print(f"精确度: {best_metrics['precision']:.3f}")
        print(f"召回率: {best_metrics['recall']:.3f}")
        print(f"F1分数: {best_metrics['f1']:.3f}")
    
    def calculate_overall_metrics(self, results: Dict, metric_key: str) -> Dict:
        """计算总体指标"""
        total_tp = sum(seq_results['distance_metrics'][metric_key]['tp'] for seq_results in results.values() if metric_key in seq_results['distance_metrics'])
        total_fp = sum(seq_results['distance_metrics'][metric_key]['fp'] for seq_results in results.values() if metric_key in seq_results['distance_metrics'])
        total_fn = sum(seq_results['distance_metrics'][metric_key]['fn'] for seq_results in results.values() if metric_key in seq_results['distance_metrics'])
        
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'tp': total_tp,
            'fp': total_fp,
            'fn': total_fn
        }

def main():
    """主函数"""
    print("=== 改进的检测评估器 ===")
    
    evaluator = ImprovedEvaluator()
    results = evaluator.evaluate_all_sequences()
    evaluator.print_results(results)

if __name__ == "__main__":
    main()
