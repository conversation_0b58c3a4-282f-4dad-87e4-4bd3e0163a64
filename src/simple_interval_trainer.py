#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化间隔视频训练器
每个样本一张图像，但包含完整的时序上下文信息
"""

import os
import sys
import json
import random
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleIntervalTrainer:
    """简化间隔视频训练器"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def create_simple_interval_samples(self, seq_id: str, frame_interval: int = 10) -> List[Dict]:
        """创建简化间隔样本 - 每个样本一张图像但包含时序上下文"""
        seq_info = self.sequences[seq_id]
        seq_dir = Path(f"dataset/images/{seq_id}")
        labels_dir = Path(f"dataset/labels/{seq_id}")
        
        if not seq_dir.exists() or not labels_dir.exists():
            logger.warning(f"序列 {seq_id} 目录不存在")
            return []
        
        image_files = self.get_image_files(seq_dir, seq_info)
        samples = []
        
        logger.info(f"序列 {seq_id}: 总帧数 {len(image_files)}")
        
        # 每隔10帧创建一个样本，包含前后时序上下文信息
        for i in range(0, len(image_files), frame_interval):
            img_file = image_files[i]
            
            # 加载当前帧的标注
            if seq_info['naming'] == 'simple':
                label_file = labels_dir / f"{img_file.stem}.txt"
            else:
                label_file = labels_dir / f"{img_file.stem}.txt"
            
            current_annotations = []
            if label_file.exists():
                try:
                    with open(label_file, 'r') as f:
                        lines = f.readlines()
                    
                    for line in lines:
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            class_id = int(parts[0])
                            x_center = float(parts[1])
                            y_center = float(parts[2])
                            width = float(parts[3])
                            height = float(parts[4])
                            
                            # 转换为绝对坐标
                            img_width, img_height = seq_info['size']
                            x1 = int((x_center - width/2) * img_width)
                            y1 = int((y_center - height/2) * img_height)
                            x2 = int((x_center + width/2) * img_width)
                            y2 = int((y_center + height/2) * img_height)
                            
                            # 验证弱小目标尺寸
                            bbox_width = x2 - x1
                            bbox_height = y2 - y1
                            
                            if (bbox_width >= 3 and bbox_height >= 3 and 
                                bbox_width <= 80 and bbox_height <= 80):
                                
                                class_name = self.class_map.get(str(class_id), "drone")
                                current_annotations.append({
                                    "bbox": [x1, y1, x2, y2],
                                    "label": class_name
                                })
                
                except Exception as e:
                    logger.warning(f"加载标注失败 {label_file}: {e}")
            
            # 获取前后帧的上下文信息
            context_frames = []
            for offset in [-2*frame_interval, -frame_interval, 0, frame_interval, 2*frame_interval]:
                context_idx = i + offset
                if 0 <= context_idx < len(image_files):
                    context_frames.append({
                        "frame_idx": context_idx,
                        "relative_position": offset // frame_interval,
                        "is_current": offset == 0
                    })
            
            # 只有当前帧有标注时才创建样本
            if current_annotations:
                samples.append({
                    "sequence_id": seq_id,
                    "current_frame_idx": i,
                    "image_path": str(img_file),
                    "annotations": current_annotations,
                    "context_frames": context_frames,
                    "frame_interval": frame_interval
                })
        
        logger.info(f"序列 {seq_id}: 创建了 {len(samples)} 个简化间隔样本")
        
        return samples
    
    def split_simple_interval_data(self, test_ratio: float = 0.2) -> Tuple[List[Dict], List[Dict]]:
        """按序列划分简化间隔数据"""
        logger.info("开始划分简化间隔训练数据...")
        
        all_sequences = list(self.sequences.keys())
        random.shuffle(all_sequences)
        
        # 按序列划分
        num_test_sequences = max(1, int(len(all_sequences) * test_ratio))
        test_sequences = all_sequences[:num_test_sequences]
        train_sequences = all_sequences[num_test_sequences:]
        
        logger.info(f"训练序列: {train_sequences}")
        logger.info(f"测试序列: {test_sequences}")
        
        # 加载简化间隔训练数据
        train_data = []
        for seq_id in train_sequences:
            seq_data = self.create_simple_interval_samples(seq_id, frame_interval=10)
            train_data.extend(seq_data)
        
        # 加载简化间隔测试数据
        test_data = []
        for seq_id in test_sequences:
            seq_data = self.create_simple_interval_samples(seq_id, frame_interval=10)
            test_data.extend(seq_data)
        
        logger.info(f"简化间隔训练数据: {len(train_data)} 个样本")
        logger.info(f"简化间隔测试数据: {len(test_data)} 个样本")
        
        return train_data, test_data
    
    def create_simple_interval_training_samples(self, data: List[Dict]) -> List[Dict]:
        """创建简化间隔训练样本"""
        samples = []
        
        for item in data:
            seq_id = item["sequence_id"]
            current_frame_idx = item["current_frame_idx"]
            annotations = item["annotations"]
            context_frames = item["context_frames"]
            frame_interval = item["frame_interval"]
            seq_info = self.sequences[seq_id]
            
            # 构建时序上下文信息
            context_info = []
            for ctx in context_frames:
                if ctx["is_current"]:
                    context_info.append(f"当前帧{ctx['frame_idx']} (检测目标)")
                else:
                    pos = ctx["relative_position"]
                    if pos < 0:
                        context_info.append(f"前{abs(pos)*frame_interval}帧: 帧{ctx['frame_idx']}")
                    else:
                        context_info.append(f"后{pos*frame_interval}帧: 帧{ctx['frame_idx']}")
            
            context_str = ", ".join(context_info)
            
            # 构建简化间隔提示词
            interval_prompt = f"""这是一个间隔采样的弱小目标检测任务。

序列信息：
- 序列ID: {seq_id}
- 图像尺寸: {seq_info['size']}
- 当前帧: {current_frame_idx}
- 采样间隔: 每隔{frame_interval}帧
- 时序上下文: {context_str}
- 目标特征: 3-80像素的弱小目标

任务要求：
1. 检测当前帧中的弱小目标
2. 理解这是间隔采样序列中的一帧
3. 学习时序上下文对检测的影响
4. 为后续的时序插值提供基础

输出格式：
{{
  "frame_id": {current_frame_idx},
  "detections": [{{"bbox":[x1,y1,x2,y2],"label":"类别"}}],
  "temporal_context": "interval_sampling",
  "interpolation_ready": true
}}

开始检测当前帧："""
            
            # 构建真实标注
            frame_detections = []
            for ann in annotations:
                frame_detections.append({
                    "bbox": ann["bbox"],
                    "label": ann["label"]
                })
            
            ground_truth = {
                "frame_id": current_frame_idx,
                "detections": frame_detections,
                "temporal_context": "interval_sampling",
                "interpolation_ready": True
            }
            
            ground_truth_json = json.dumps(ground_truth, ensure_ascii=False)
            
            sample = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "image": item["image_path"]
                            },
                            {
                                "type": "text",
                                "text": interval_prompt
                            }
                        ]
                    },
                    {
                        "role": "assistant",
                        "content": ground_truth_json
                    }
                ]
            }
            samples.append(sample)
        
        return samples
    
    def prepare_simple_interval_training_data(self):
        """准备简化间隔训练数据"""
        logger.info("开始准备简化间隔训练数据...")
        
        # 设置随机种子
        random.seed(42)
        
        # 划分简化间隔数据
        train_data, test_data = self.split_simple_interval_data(test_ratio=0.25)
        
        # 创建简化间隔训练样本
        train_samples = self.create_simple_interval_training_samples(train_data)
        test_samples = self.create_simple_interval_training_samples(test_data)
        
        logger.info(f"生成简化间隔训练样本: {len(train_samples)} 个")
        logger.info(f"生成简化间隔测试样本: {len(test_samples)} 个")
        
        # 保存简化间隔训练数据
        os.makedirs("data", exist_ok=True)
        
        with open("data/simple_interval_train_data.json", 'w', encoding='utf-8') as f:
            json.dump(train_samples, f, ensure_ascii=False, indent=2)
        
        with open("data/simple_interval_test_data.json", 'w', encoding='utf-8') as f:
            json.dump(test_samples, f, ensure_ascii=False, indent=2)
        
        logger.info("简化间隔训练数据已保存")
        logger.info(f"简化间隔训练数据: data/simple_interval_train_data.json ({len(train_samples)} 样本)")
        logger.info(f"简化间隔测试数据: data/simple_interval_test_data.json ({len(test_samples)} 样本)")
        
        return train_samples, test_samples

def main():
    """主函数"""
    print("=== 简化间隔视频序列微调训练系统 ===")
    print("🎯 特点: 每隔10帧取1帧，单图像+时序上下文")
    print("📊 策略: 保持时序信息但简化输入")
    print("🔧 技术: 间隔采样 + 上下文学习")
    print("💡 解决多图像处理问题")
    
    # 创建简化间隔训练器
    trainer = SimpleIntervalTrainer()
    
    # 准备简化间隔训练数据
    train_samples, test_samples = trainer.prepare_simple_interval_training_data()
    
    print(f"\n✅ 简化间隔数据准备完成:")
    print(f"   简化间隔训练样本: {len(train_samples)}")
    print(f"   简化间隔测试样本: {len(test_samples)}")
    print(f"   每个样本: 1张图像 + 时序上下文信息")
    print(f"   学习内容: 间隔检测 + 时序理解")
    
    print("\n💡 下一步: 使用简化数据进行LoRA微调")
    print("🎯 保持用户的间隔采样思路，但解决技术问题")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
