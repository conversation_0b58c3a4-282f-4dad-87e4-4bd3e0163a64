#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格按照用户思路的正确实现
第一：每个data的数据每隔10帧抽取1帧组成视频序列进行微调
第二：每隔10帧抽取1帧组成视频序列输入模型检测，缺失帧线性推测
"""

import os
import sys
import json
import cv2
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrectImplementation:
    """严格按照用户思路的正确实现"""
    
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def create_interval_10_video_sequence(self, seq_id: str, output_dir: Path) -> Dict:
        """
        思路1实现：每个data的数据每隔10帧抽取1帧组成视频序列进行微调
        为每个序列创建一个完整的视频序列（不是片段）
        """
        logger.info(f"为序列 {seq_id} 创建每隔10帧抽取1帧的完整视频序列...")
        
        seq_info = self.sequences[seq_id]
        images_dir = Path(f"dataset/images/{seq_id}")
        all_image_files = self.get_image_files(images_dir, seq_info)
        
        if len(all_image_files) == 0:
            logger.warning(f"序列 {seq_id} 没有图像文件")
            return None
        
        # 严格按照思路：每隔10帧抽取1帧
        selected_indices = list(range(0, len(all_image_files), 10))
        selected_image_files = [all_image_files[i] for i in selected_indices]
        
        logger.info(f"序列 {seq_id}: 总帧数 {len(all_image_files)}, 每隔10帧抽取后 {len(selected_image_files)} 帧")
        
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建完整的视频序列文件
        video_filename = f"{seq_id}_interval10_complete_sequence.mp4"
        video_path = output_dir / video_filename
        
        # 获取图像尺寸
        first_img = cv2.imread(str(selected_image_files[0]))
        if first_img is None:
            return None
        height, width = first_img.shape[:2]
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(str(video_path), fourcc, 10, (width, height))
        
        # 收集所有帧的标注信息
        sequence_annotations = {}
        
        for video_frame_idx, img_file in enumerate(selected_image_files):
            # 读取图像
            img = cv2.imread(str(img_file))
            if img is None:
                continue
            
            # 写入视频
            video_writer.write(img)
            
            # 收集标注
            frame_id = img_file.stem
            original_frame_idx = selected_indices[video_frame_idx]
            annotations = self.load_frame_annotation(seq_id, frame_id, seq_info['size'])
            
            if annotations:
                sequence_annotations[video_frame_idx] = {
                    'frame_id': frame_id,
                    'original_frame_idx': original_frame_idx,
                    'annotations': annotations
                }
        
        video_writer.release()
        
        if sequence_annotations:
            video_info = {
                'video_path': str(video_path),
                'seq_id': seq_id,
                'total_frames': len(selected_image_files),
                'interval': 10,
                'selected_indices': selected_indices,
                'annotations': sequence_annotations,
                'fps': 10
            }
            logger.info(f"创建完整视频序列: {video_filename} ({len(selected_image_files)}帧, {len(sequence_annotations)}个标注帧)")
            return video_info
        else:
            # 删除没有标注的视频文件
            video_path.unlink()
            return None
    
    def load_frame_annotation(self, seq_id: str, frame_id: str, image_size: Tuple[int, int]) -> List[Dict]:
        """加载帧标注"""
        annotation_file = Path(f"dataset/labels/{seq_id}/{frame_id}.txt")
        if not annotation_file.exists():
            return []
        
        annotations = []
        with open(annotation_file, 'r') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) != 5:
                    continue
                
                class_id = int(parts[0])
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                
                # 转换为绝对坐标
                abs_w = width * image_size[0]
                abs_h = height * image_size[1]
                
                # 检查是否为弱小目标（3-80像素）
                if 3 <= abs_w <= 80 and 3 <= abs_h <= 80:
                    class_name = self.class_map.get(str(class_id), 'unknown')
                    annotations.append({
                        'class_id': class_id,
                        'class_name': class_name,
                        'x_center': x_center,
                        'y_center': y_center,
                        'width': width,
                        'height': height,
                        'frame_id': frame_id
                    })
        
        return annotations
    
    def create_all_correct_videos(self, output_base_dir: str = "dataset/correct_videos") -> Dict[str, Dict]:
        """为所有序列创建正确的视频序列"""
        logger.info("开始创建严格按照思路的视频序列...")
        
        output_dir = Path(output_base_dir)
        all_video_data = {}
        
        for seq_id in self.sequences.keys():
            seq_output_dir = output_dir / seq_id
            video_info = self.create_interval_10_video_sequence(seq_id, seq_output_dir)
            if video_info:
                all_video_data[seq_id] = video_info
                logger.info(f"序列 {seq_id}: 创建了完整视频序列")
        
        # 保存视频数据信息
        video_data_file = output_dir / "correct_video_data.json"
        with open(video_data_file, 'w', encoding='utf-8') as f:
            json.dump(all_video_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"正确视频数据信息保存到: {video_data_file}")
        return all_video_data
    
    def create_correct_training_data(self, video_data: Dict[str, Dict], 
                                   train_ratio: float = 0.8) -> Tuple[List[Dict], List[Dict]]:
        """创建正确的训练数据"""
        logger.info("创建严格按照思路的训练数据...")
        
        all_samples = []
        
        for seq_id, video_info in video_data.items():
            # 为每个完整视频序列创建训练样本
            annotations_text = self.format_sequence_annotations(video_info['annotations'])
            
            sample = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "video",
                                "video": video_info['video_path']
                            },
                            {
                                "type": "text",
                                "text": f"""你是专业的红外弱小目标视频检测专家。请检测这个完整视频序列中的弱小目标。

视频信息:
- 序列: {video_info['seq_id']}
- 采样方式: 每隔10帧抽取1帧
- 总帧数: {video_info['total_frames']}帧
- 这是完整的序列，不是片段

请检测视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
frame_idx class_name x_center y_center width height confidence

注意：
1. frame_idx为视频中的帧索引（从0开始）
2. 坐标为相对坐标（0-1之间）
3. 只检测尺寸在3-80像素之间的弱小目标
4. 利用完整视频序列的时序信息提高检测精度
5. confidence为检测置信度（0-1之间）"""
                            }
                        ]
                    },
                    {
                        "role": "assistant",
                        "content": annotations_text
                    }
                ]
            }
            
            all_samples.append(sample)
        
        # 划分训练集和测试集
        np.random.shuffle(all_samples)
        split_idx = int(len(all_samples) * train_ratio)
        
        train_data = all_samples[:split_idx]
        test_data = all_samples[split_idx:]
        
        logger.info(f"创建了 {len(train_data)} 个训练样本，{len(test_data)} 个测试样本")
        
        return train_data, test_data
    
    def format_sequence_annotations(self, annotations: Dict[int, Dict]) -> str:
        """格式化完整序列标注为文本"""
        result_lines = []
        
        for frame_idx, frame_data in annotations.items():
            for ann in frame_data['annotations']:
                line = f"{frame_idx} {ann['class_name']} {ann['x_center']:.6f} {ann['y_center']:.6f} {ann['width']:.6f} {ann['height']:.6f} 1.0"
                result_lines.append(line)
        
        return '\n'.join(result_lines)

def main():
    """主函数"""
    print("=== 严格按照用户思路的正确实现 ===")
    print("🎯 思路1: 每个data的数据每隔10帧抽取1帧组成视频序列进行微调")
    print("🎯 思路2: 每隔10帧抽取1帧组成视频序列输入模型检测")
    print("🎯 思路3: 缺失的10帧位置信息由上下帧线性推测")
    print("📹 创建完整的视频序列，不是片段")
    
    # 创建正确实现
    implementer = CorrectImplementation()
    
    # 创建正确的视频序列
    video_data = implementer.create_all_correct_videos()
    
    # 创建正确的训练数据
    train_data, test_data = implementer.create_correct_training_data(video_data)
    
    # 保存训练数据
    os.makedirs("data", exist_ok=True)
    
    with open("data/correct_train_data.json", 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    with open("data/correct_test_data.json", 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 严格按照思路的实现完成！")
    print(f"📹 创建视频序列: {len(video_data)} 个完整序列")
    print(f"📊 训练样本: {len(train_data)} 个")
    print(f"📊 测试样本: {len(test_data)} 个")
    print(f"💾 训练数据保存到: data/correct_train_data.json")
    print(f"🎯 完全符合用户思路：每隔10帧抽取1帧的完整序列")

if __name__ == "__main__":
    main()
