#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时序感知检测器
使用微调后的间隔视频模型进行检测
采用与微调数据相似的格式，充分利用时序性
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image, ImageEnhance
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
import re
import numpy as np

# 固定使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TemporalAwareDetector:
    """时序感知检测器"""
    
    def __init__(self, base_model_path: str, lora_model_path: str):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        logger.info(f"使用设备: {self.device}")
        logger.info(f"基础模型: {base_model_path}")
        logger.info(f"间隔视频LoRA模型: {lora_model_path}")
        
        # 加载基础模型
        self.processor = AutoProcessor.from_pretrained(base_model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # 加载间隔视频LoRA微调模型
        logger.info("加载间隔视频LoRA模型...")
        self.model = PeftModel.from_pretrained(self.model, lora_model_path)
        logger.info("时序感知检测器加载完成")
        
        # 序列信息
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
            'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
            'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
        }
        
        # 加载类别映射
        with open('dataset/class.json', 'r', encoding='utf-8') as f:
            self.class_map = json.load(f)
    
    def get_image_files(self, seq_dir: Path, seq_info: Dict) -> List[Path]:
        """获取图像文件列表"""
        if seq_info['naming'] == 'simple':
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            return sorted(image_files, key=lambda x: int(x.stem))
        else:
            image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
            def complex_sort_key(path):
                numbers = re.findall(r'(\d+)', path.name)
                return int(numbers[-1]) if numbers else 0
            return sorted(image_files, key=complex_sort_key)
    
    def enhanced_image_processing(self, image_path: str, seq_info: Dict) -> Image.Image:
        """增强图像处理"""
        img = Image.open(image_path).convert('RGB')
        
        # 适度的图像增强
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.3)
        
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.2)
        
        # 调整图像尺寸
        img.thumbnail((448, 448), Image.Resampling.LANCZOS)
        
        return img
    
    def temporal_aware_detection(self, image_path: str, frame_idx: int, seq_id: str, context_frames: List[Dict]) -> List[Dict]:
        """时序感知检测 - 使用与微调数据相似的格式"""
        try:
            seq_info = self.sequences[seq_id]
            frame_interval = 10
            
            # 构建时序上下文信息（与微调数据格式一致）
            context_info = []
            for ctx in context_frames:
                if ctx["is_current"]:
                    context_info.append(f"当前帧{ctx['frame_idx']} (检测目标)")
                else:
                    pos = ctx["relative_position"]
                    if pos < 0:
                        context_info.append(f"前{abs(pos)*frame_interval}帧: 帧{ctx['frame_idx']}")
                    else:
                        context_info.append(f"后{pos*frame_interval}帧: 帧{ctx['frame_idx']}")
            
            context_str = ", ".join(context_info)
            
            # 使用与微调数据完全一致的提示词格式
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image_path
                        },
                        {
                            "type": "text",
                            "text": f"""这是一个间隔采样的弱小目标检测任务。

序列信息：
- 序列ID: {seq_id}
- 图像尺寸: {seq_info['size']}
- 当前帧: {frame_idx}
- 采样间隔: 每隔{frame_interval}帧
- 时序上下文: {context_str}
- 目标特征: 3-80像素的弱小目标

任务要求：
1. 检测当前帧中的弱小目标
2. 理解这是间隔采样序列中的一帧
3. 学习时序上下文对检测的影响
4. 为后续的时序插值提供基础

输出格式：
{{
  "frame_id": {frame_idx},
  "detections": [{{"bbox":[x1,y1,x2,y2],"label":"类别"}}],
  "temporal_context": "interval_sampling",
  "interpolation_ready": true
}}

开始检测当前帧："""
                        }
                    ]
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 使用增强图像处理
            image = self.enhanced_image_processing(image_path, seq_info)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=[image],
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 生成检测结果
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=512,
                    do_sample=False,  # 使用贪婪解码确保稳定性
                    temperature=1.0,
                    pad_token_id=self.processor.tokenizer.eos_token_id,
                )
            
            # 解码输出
            generated_text = self.processor.decode(
                outputs[0][inputs["input_ids"].shape[1]:], 
                skip_special_tokens=True
            )
            
            logger.info(f"帧 {frame_idx} 原始输出: {generated_text[:200]}...")
            
            # 解析检测结果
            detections = self.parse_temporal_detection_result(generated_text, seq_info, frame_idx)
            
            logger.info(f"帧 {frame_idx} 时序感知检测: {len(detections)} 个目标")
            return detections
                
        except Exception as e:
            logger.error(f"检测帧 {frame_idx} 时出错: {e}")
            return []
    
    def parse_temporal_detection_result(self, text: str, seq_info: Dict, frame_idx: int) -> List[Dict]:
        """解析时序检测结果"""
        try:
            # 清理文本
            clean_text = text.strip()
            
            # 移除markdown标记
            if clean_text.startswith('```json'):
                clean_text = clean_text[7:]
            elif clean_text.startswith('```'):
                clean_text = clean_text[3:]
            if clean_text.endswith('```'):
                clean_text = clean_text[:-3]
            
            clean_text = clean_text.strip()
            
            # 查找JSON
            json_start = clean_text.find('{')
            json_end = clean_text.rfind('}')
            
            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_str = clean_text[json_start:json_end+1]
                
                try:
                    result = json.loads(json_str)
                    if isinstance(result, dict) and 'detections' in result:
                        detections = result['detections']
                        if isinstance(detections, list):
                            valid_detections = []
                            for det in detections:
                                if isinstance(det, dict) and 'bbox' in det:
                                    bbox = det['bbox']
                                    if isinstance(bbox, list) and len(bbox) == 4:
                                        # 验证坐标
                                        img_w, img_h = seq_info['size']
                                        bbox[0] = max(0, min(int(bbox[0]), img_w))
                                        bbox[1] = max(0, min(int(bbox[1]), img_h))
                                        bbox[2] = max(bbox[0], min(int(bbox[2]), img_w))
                                        bbox[3] = max(bbox[1], min(int(bbox[3]), img_h))
                                        
                                        # 验证尺寸
                                        width = bbox[2] - bbox[0]
                                        height = bbox[3] - bbox[1]
                                        
                                        if (width > 0 and height > 0 and 
                                            width <= 100 and height <= 100 and
                                            width >= 2 and height >= 2):
                                            
                                            label = det.get('label', 'drone').lower()
                                            
                                            valid_detections.append({
                                                'bbox': bbox,
                                                'label': label,
                                                'frame_id': frame_idx,
                                                'frame_idx': frame_idx,
                                                'temporal_context': True
                                            })
                            
                            return valid_detections
                except:
                    pass
            
            # 正则表达式提取
            bbox_patterns = [
                r'"bbox"\s*:\s*\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]',
                r'\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]'
            ]
            
            for pattern in bbox_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    detections = []
                    for match in matches:
                        try:
                            bbox_coords = [int(match[0]), int(match[1]), int(match[2]), int(match[3])]
                            
                            # 验证坐标
                            img_w, img_h = seq_info['size']
                            bbox_coords[0] = max(0, min(bbox_coords[0], img_w))
                            bbox_coords[1] = max(0, min(bbox_coords[1], img_h))
                            bbox_coords[2] = max(bbox_coords[0], min(bbox_coords[2], img_w))
                            bbox_coords[3] = max(bbox_coords[1], min(bbox_coords[3], img_h))
                            
                            width = bbox_coords[2] - bbox_coords[0]
                            height = bbox_coords[3] - bbox_coords[1]
                            
                            if (width > 0 and height > 0 and 
                                width <= 100 and height <= 100 and
                                width >= 2 and height >= 2):
                                
                                detections.append({
                                    "bbox": bbox_coords,
                                    "label": "drone",
                                    "frame_id": frame_idx,
                                    "frame_idx": frame_idx,
                                    "temporal_context": True
                                })
                        except:
                            continue
                    
                    if detections:
                        return detections
            
            return []
            
        except Exception as e:
            logger.warning(f"解析时序检测结果失败: {e}")
            return []
    
    def temporal_interpolation(self, key_detections: Dict[int, List[Dict]], all_frame_indices: List[int], frame_interval: int = 10) -> Dict[int, List[Dict]]:
        """时序插值 - 基于微调模型的时序理解能力"""
        all_detections = {}
        
        # 首先复制关键帧检测
        for frame_idx, detections in key_detections.items():
            all_detections[frame_idx] = detections
        
        # 对中间帧进行时序插值
        for frame_idx in all_frame_indices:
            if frame_idx not in all_detections:
                # 找到最近的前后关键帧
                prev_key = None
                next_key = None
                
                for key_frame in sorted(key_detections.keys()):
                    if key_frame < frame_idx:
                        prev_key = key_frame
                    elif key_frame > frame_idx and next_key is None:
                        next_key = key_frame
                        break
                
                interpolated_detections = []
                
                # 基于前后关键帧进行插值
                if prev_key is not None and next_key is not None:
                    prev_detections = key_detections[prev_key]
                    next_detections = key_detections[next_key]
                    
                    # 简单的线性插值
                    alpha = (frame_idx - prev_key) / (next_key - prev_key)
                    
                    for i, prev_det in enumerate(prev_detections):
                        if i < len(next_detections):
                            next_det = next_detections[i]
                            
                            # 线性插值bbox
                            prev_bbox = prev_det['bbox']
                            next_bbox = next_det['bbox']
                            
                            interpolated_bbox = [
                                int(prev_bbox[0] + alpha * (next_bbox[0] - prev_bbox[0])),
                                int(prev_bbox[1] + alpha * (next_bbox[1] - prev_bbox[1])),
                                int(prev_bbox[2] + alpha * (next_bbox[2] - prev_bbox[2])),
                                int(prev_bbox[3] + alpha * (next_bbox[3] - prev_bbox[3]))
                            ]
                            
                            interpolated_detections.append({
                                'bbox': interpolated_bbox,
                                'label': prev_det['label'],
                                'frame_id': frame_idx,
                                'frame_idx': frame_idx,
                                'interpolated': True,
                                'temporal_context': True
                            })
                
                elif prev_key is not None:
                    # 只有前一帧，复制检测结果
                    for det in key_detections[prev_key]:
                        interpolated_detections.append({
                            **det,
                            'frame_id': frame_idx,
                            'frame_idx': frame_idx,
                            'interpolated': True
                        })
                
                all_detections[frame_idx] = interpolated_detections
        
        return all_detections
    
    def load_ground_truth(self, sequence_id: str, frame_indices: List[int]) -> Dict[int, List[Dict]]:
        """加载真实标注"""
        seq_info = self.sequences[sequence_id]
        labels_dir = Path(f"dataset/labels/{sequence_id}")
        ground_truth = {}
        
        seq_dir = Path(f"dataset/images/{sequence_id}")
        image_files = self.get_image_files(seq_dir, seq_info)
        
        for frame_idx in frame_indices:
            if frame_idx < len(image_files):
                img_file = image_files[frame_idx]
                
                if seq_info['naming'] == 'simple':
                    label_file = labels_dir / f"{img_file.stem}.txt"
                else:
                    label_file = labels_dir / f"{img_file.stem}.txt"
                
                annotations = []
                if label_file.exists():
                    try:
                        with open(label_file, 'r') as f:
                            for line in f:
                                parts = line.strip().split()
                                if len(parts) >= 5:
                                    class_id = int(parts[0])
                                    x_center = float(parts[1])
                                    y_center = float(parts[2])
                                    width = float(parts[3])
                                    height = float(parts[4])
                                    
                                    # 转换为绝对坐标
                                    img_width, img_height = seq_info['size']
                                    x1 = int((x_center - width/2) * img_width)
                                    y1 = int((y_center - height/2) * img_height)
                                    x2 = int((x_center + width/2) * img_width)
                                    y2 = int((y_center + height/2) * img_height)
                                    
                                    class_name = self.class_map.get(str(class_id), "unknown")
                                    
                                    annotations.append({
                                        "bbox": [x1, y1, x2, y2],
                                        "label": class_name,
                                        "frame_id": img_file.stem,
                                        "frame_idx": frame_idx
                                    })
                    except Exception as e:
                        logger.warning(f"加载标注文件失败 {label_file}: {e}")
                
                ground_truth[frame_idx] = annotations
            else:
                ground_truth[frame_idx] = []
        
        return ground_truth

    def calculate_metrics(self, predictions: Dict[int, List[Dict]], ground_truth: Dict[int, List[Dict]], iou_threshold: float = 0.5) -> Dict:
        """计算检测指标"""
        def calculate_iou(box1, box2):
            x1 = max(box1[0], box2[0])
            y1 = max(box1[1], box2[1])
            x2 = min(box1[2], box2[2])
            y2 = min(box1[3], box2[3])

            if x2 <= x1 or y2 <= y1:
                return 0.0

            intersection = (x2 - x1) * (y2 - y1)
            area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
            area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
            union = area1 + area2 - intersection

            return intersection / union if union > 0 else 0.0

        total_tp = 0
        total_fp = 0
        total_fn = 0

        for frame_idx in ground_truth:
            gt_boxes = ground_truth[frame_idx]
            pred_boxes = predictions.get(frame_idx, [])

            matched_gt = set()

            for pred in pred_boxes:
                if not isinstance(pred, dict) or 'bbox' not in pred:
                    continue

                best_iou = 0
                best_gt_idx = -1

                for gt_idx, gt in enumerate(gt_boxes):
                    if gt_idx in matched_gt:
                        continue

                    if not isinstance(gt, dict) or 'bbox' not in gt:
                        continue

                    iou = calculate_iou(pred['bbox'], gt['bbox'])
                    if iou > best_iou and iou >= iou_threshold:
                        best_iou = iou
                        best_gt_idx = gt_idx

                if best_gt_idx >= 0:
                    total_tp += 1
                    matched_gt.add(best_gt_idx)
                else:
                    total_fp += 1

            total_fn += len(gt_boxes) - len(matched_gt)

        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        return {
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "tp": total_tp,
            "fp": total_fp,
            "fn": total_fn
        }

    def detect_temporal_sequence(self, sequence_id: str, test_ratio: float = 0.05) -> Dict:
        """时序感知序列检测"""
        logger.info(f"开始时序感知检测序列 {sequence_id}...")

        seq_info = self.sequences[sequence_id]
        seq_dir = Path(f"dataset/images/{sequence_id}")

        if not seq_dir.exists():
            logger.error(f"序列目录不存在: {seq_dir}")
            return {}

        image_files = self.get_image_files(seq_dir, seq_info)
        if not image_files:
            logger.error(f"序列 {sequence_id} 中没有找到图像文件")
            return {}

        # 计算测试帧数
        total_frames = len(image_files)
        test_frames = max(int(total_frames * test_ratio), 50)
        test_frames = min(test_frames, total_frames)

        logger.info(f"序列 {sequence_id}: 总帧数 {total_frames}, 测试帧数 {test_frames}")

        # 每隔10帧抽取关键帧（与微调策略一致）
        frame_interval = 10
        key_frame_indices = list(range(0, test_frames, frame_interval))
        all_frame_indices = list(range(test_frames))

        logger.info(f"关键帧数: {len(key_frame_indices)} (每隔{frame_interval}帧)")

        # 时序感知检测关键帧
        logger.info(f"开始时序感知检测 ({len(key_frame_indices)} 关键帧)...")
        key_frame_detections = {}

        for frame_idx in key_frame_indices:
            if frame_idx < len(image_files):
                frame_file = image_files[frame_idx]

                # 构建时序上下文（与微调数据格式一致）
                context_frames = []
                for offset in [-2*frame_interval, -frame_interval, 0, frame_interval, 2*frame_interval]:
                    context_idx = frame_idx + offset
                    if 0 <= context_idx < len(image_files):
                        context_frames.append({
                            "frame_idx": context_idx,
                            "relative_position": offset // frame_interval,
                            "is_current": offset == 0
                        })

                detections = self.temporal_aware_detection(str(frame_file), frame_idx, sequence_id, context_frames)
                key_frame_detections[frame_idx] = detections

        # 时序插值
        logger.info("进行时序插值...")
        all_detections = self.temporal_interpolation(key_frame_detections, all_frame_indices, frame_interval)

        # 加载真实标注
        logger.info("加载真实标注...")
        ground_truth = self.load_ground_truth(sequence_id, all_frame_indices)

        # 计算指标
        logger.info("计算检测指标...")
        metrics = self.calculate_metrics(all_detections, ground_truth)

        # 统计结果
        total_detections = sum(len(dets) for dets in all_detections.values())
        direct_detections = sum(len(dets) for frame_idx, dets in all_detections.items()
                               if frame_idx in key_frame_detections)
        interpolated_detections = total_detections - direct_detections

        result = {
            "sequence_id": sequence_id,
            "total_frames": test_frames,
            "key_frames": len(key_frame_indices),
            "total_detections": total_detections,
            "direct_detections": direct_detections,
            "interpolated_detections": interpolated_detections,
            "metrics": metrics,
            "detections": all_detections,
            "model_type": "temporal_aware_finetuned"
        }

        logger.info(f"序列 {sequence_id} 时序感知检测完成:")
        logger.info(f"  总检测数: {total_detections}")
        logger.info(f"  直接检测: {direct_detections}")
        logger.info(f"  插值检测: {interpolated_detections}")
        logger.info(f"  精确度: {metrics['precision']:.3f}")
        logger.info(f"  召回率: {metrics['recall']:.3f}")
        logger.info(f"  F1分数: {metrics['f1']:.3f}")

        return result

def main():
    """主函数"""
    print("=== 时序感知弱小目标检测系统 ===")
    print("🎯 特点: 使用微调后的间隔视频模型")
    print("📊 策略: 每隔10帧检测+时序插值")
    print("🔧 技术: 时序上下文+微调模型")
    print("💡 与微调数据格式完全一致")

    # 初始化时序感知检测器
    base_model_path = "qwen2.5-vl-7b"
    lora_model_path = "output/interval_video_lora"

    print(f"\n初始化时序感知检测器...")
    detector = TemporalAwareDetector(base_model_path, lora_model_path)

    # 检测所有序列
    sequences = ['data01', 'data02', 'data04', 'data06', 'data07', 'data19', 'data23', 'data26']
    all_results = {}

    for sequence_id in sequences:
        print(f"\n{'='*60}")
        print(f"时序感知检测序列: {sequence_id}")
        print(f"{'='*60}")

        result = detector.detect_temporal_sequence(sequence_id)
        all_results[sequence_id] = result

    # 保存结果
    os.makedirs("results", exist_ok=True)
    with open("results/temporal_aware_detection_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)

    print("\n🎉 时序感知检测完成！")
    print("📊 检测结果已保存到 results/temporal_aware_detection_results.json")

    # 统计结果
    print("\n📈 时序感知检测统计:")
    total_f1 = 0
    valid_sequences = 0

    for seq_id, result in all_results.items():
        if result and 'metrics' in result:
            metrics = result['metrics']
            f1 = metrics['f1']
            total_f1 += f1
            valid_sequences += 1

            print(f"   {seq_id}: {result['total_detections']} 个检测 "
                  f"(直接: {result['direct_detections']}, 插值: {result['interpolated_detections']}) "
                  f"P: {metrics['precision']:.3f}, R: {metrics['recall']:.3f}, F1: {metrics['f1']:.3f}")

    if valid_sequences > 0:
        avg_f1 = total_f1 / valid_sequences
        print(f"\n🎯 平均F1分数: {avg_f1:.3f}")
        print(f"🚀 这是使用微调后间隔视频模型的时序感知检测结果！")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
