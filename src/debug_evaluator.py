#!/usr/bin/env python3
"""
调试评估器问题
"""

import json
from pathlib import Path

def debug_evaluator():
    print("=== 调试评估器问题 ===")
    
    # 检查data01的检测结果和标注
    print("\n1. 检查data01检测结果:")
    detection_file = Path("results/interval_5frame_detection/data01_detections.json")
    with open(detection_file, 'r') as f:
        detections = json.load(f)
    
    print(f"检测结果数量: {len(detections)}")
    if detections:
        det = detections[0]
        print(f"第一个检测: 帧{det['original_frame_idx']}, 坐标({det['x_center']:.3f}, {det['y_center']:.3f})")
    
    # 检查data01的标注文件
    print("\n2. 检查data01标注文件:")
    label_dir = Path("dataset/labels/data01")
    label_files = list(label_dir.glob("*.txt"))
    print(f"标注文件数量: {len(label_files)}")
    
    # 检查前几个标注文件
    for i, label_file in enumerate(sorted(label_files)[:5]):
        frame_idx = int(label_file.stem)
        with open(label_file, 'r') as f:
            lines = [line.strip() for line in f if line.strip()]
        print(f"帧{frame_idx}: {len(lines)}个标注")
        if lines:
            parts = lines[0].split()
            if len(parts) >= 5:
                print(f"  第一个标注: ({parts[1]}, {parts[2]})")
    
    # 检查帧匹配
    print("\n3. 检查帧匹配:")
    detection_frames = set(det['original_frame_idx'] for det in detections)
    gt_frames = set(int(f.stem) for f in label_files)
    
    print(f"检测帧: {sorted(list(detection_frames))[:10]}...")
    print(f"标注帧: {sorted(list(gt_frames))[:10]}...")
    
    overlap = detection_frames & gt_frames
    print(f"重叠帧数: {len(overlap)}")
    print(f"重叠帧: {sorted(list(overlap))[:10]}...")
    
    # 检查具体匹配
    if overlap:
        frame_idx = min(overlap)
        print(f"\n4. 检查帧{frame_idx}的具体匹配:")
        
        # 检测结果
        det_for_frame = [det for det in detections if det['original_frame_idx'] == frame_idx]
        print(f"该帧检测数: {len(det_for_frame)}")
        if det_for_frame:
            det = det_for_frame[0]
            print(f"检测坐标: ({det['x_center']:.6f}, {det['y_center']:.6f})")
        
        # 标注结果
        label_file = Path(f"dataset/labels/data01/{frame_idx}.txt")
        with open(label_file, 'r') as f:
            lines = [line.strip() for line in f if line.strip()]
        print(f"该帧标注数: {len(lines)}")
        if lines:
            parts = lines[0].split()
            if len(parts) >= 5:
                gt_x, gt_y = float(parts[1]), float(parts[2])
                print(f"标注坐标: ({gt_x:.6f}, {gt_y:.6f})")
                
                # 计算距离
                if det_for_frame:
                    det = det_for_frame[0]
                    distance = ((det['x_center'] - gt_x)**2 + (det['y_center'] - gt_y)**2)**0.5
                    print(f"距离: {distance:.6f}")
                    
                    # 判断各种阈值
                    print(f"距离0.05: {'✅' if distance <= 0.05 else '❌'}")
                    print(f"距离0.1: {'✅' if distance <= 0.1 else '❌'}")
                    print(f"距离0.15: {'✅' if distance <= 0.15 else '❌'}")
                    print(f"距离0.2: {'✅' if distance <= 0.2 else '❌'}")

if __name__ == "__main__":
    debug_evaluator()
