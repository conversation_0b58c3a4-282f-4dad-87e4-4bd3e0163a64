#!/usr/bin/env python3
"""
改进的训练数据生成器 - 解决过拟合和数据质量问题
"""

import json
import os
import random
import numpy as np
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedDataGenerator:
    def __init__(self):
        self.sequences = {
            'data01': {'size': (256, 256), 'format': '.bmp'},
            'data02': {'size': (256, 256), 'format': '.bmp'},
            'data04': {'size': (256, 256), 'format': '.bmp'},
            'data06': {'size': (256, 256), 'format': '.bmp'},
            'data07': {'size': (256, 256), 'format': '.bmp'},
            'data19': {'size': (640, 512), 'format': '.jpg'},
            'data23': {'size': (640, 512), 'format': '.jpg'},
            'data24': {'size': (640, 512), 'format': '.jpg'},
            'data25': {'size': (640, 512), 'format': '.jpg'},
            'data26': {'size': (640, 512), 'format': '.jpg'}
        }
        
    def load_ground_truth_annotations(self, seq_id):
        """加载真实标注数据"""
        label_dir = Path(f"dataset/labels/{seq_id}")
        annotations = {}
        
        for label_file in label_dir.glob("*.txt"):
            try:
                frame_idx = int(label_file.stem)
            except ValueError:
                # 跳过复杂文件名
                continue
            
            with open(label_file, 'r') as f:
                lines = [line.strip() for line in f if line.strip()]
            
            frame_annotations = []
            for line in lines:
                parts = line.split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 映射类别ID到名称
                    class_names = {0: 'drone', 1: 'car', 2: 'ship', 3: 'bus', 4: 'pedestrian', 5: 'cyclist'}
                    class_name = class_names.get(class_id, 'unknown')
                    
                    frame_annotations.append({
                        'class_name': class_name,
                        'x_center': x_center,
                        'y_center': y_center,
                        'width': width,
                        'height': height
                    })
            
            if frame_annotations:
                annotations[frame_idx] = frame_annotations
                
        return annotations
    
    def create_diverse_training_samples(self):
        """创建多样化的训练样本"""
        all_samples = []
        
        for seq_id in self.sequences.keys():
            logger.info(f"处理序列 {seq_id}")
            
            # 加载真实标注
            annotations = self.load_ground_truth_annotations(seq_id)
            if not annotations:
                logger.warning(f"序列 {seq_id} 没有有效标注")
                continue
            
            # 创建多种类型的训练样本
            samples = []
            
            # 1. 单视频样本 (原始方式)
            samples.extend(self.create_single_video_samples(seq_id, annotations))
            
            # 2. 多视频样本 (连续帧)
            samples.extend(self.create_multi_video_samples(seq_id, annotations))
            
            # 3. 负样本 (无目标帧)
            samples.extend(self.create_negative_samples(seq_id, annotations))
            
            # 4. 数据增强样本
            samples.extend(self.create_augmented_samples(seq_id, annotations))
            
            all_samples.extend(samples)
            logger.info(f"序列 {seq_id} 生成 {len(samples)} 个样本")
        
        # 打乱样本顺序
        random.shuffle(all_samples)
        
        logger.info(f"总共生成 {len(all_samples)} 个训练样本")
        return all_samples
    
    def create_single_video_samples(self, seq_id, annotations):
        """创建单视频训练样本"""
        samples = []
        frame_indices = sorted(annotations.keys())
        
        # 每10帧创建一个5帧视频样本
        for start_frame in range(0, max(frame_indices), 50):  # 增大间隔，减少重复
            video_frames = []
            video_annotations = []
            
            for i in range(5):
                frame_idx = start_frame + i * 10
                if frame_idx in annotations:
                    video_frames.append(frame_idx)
                    video_annotations.append(annotations[frame_idx])
                else:
                    video_frames.append(frame_idx)
                    video_annotations.append([])  # 空标注
            
            if len(video_frames) == 5:
                sample = self.create_sample(seq_id, [video_frames], [video_annotations], "single")
                samples.append(sample)
        
        return samples
    
    def create_multi_video_samples(self, seq_id, annotations):
        """创建多视频训练样本"""
        samples = []
        frame_indices = sorted(annotations.keys())
        
        # 创建3个连续的5帧视频
        for start_frame in range(0, max(frame_indices), 150):  # 更大间隔
            all_video_frames = []
            all_video_annotations = []
            
            for video_idx in range(3):
                video_start = start_frame + video_idx * 50
                video_frames = []
                video_annotations = []
                
                for i in range(5):
                    frame_idx = video_start + i * 10
                    if frame_idx in annotations:
                        video_frames.append(frame_idx)
                        video_annotations.append(annotations[frame_idx])
                    else:
                        video_frames.append(frame_idx)
                        video_annotations.append([])
                
                if len(video_frames) == 5:
                    all_video_frames.append(video_frames)
                    all_video_annotations.append(video_annotations)
            
            if len(all_video_frames) == 3:
                sample = self.create_sample(seq_id, all_video_frames, all_video_annotations, "multi")
                samples.append(sample)
        
        return samples
    
    def create_negative_samples(self, seq_id, annotations):
        """创建负样本 (无目标帧)"""
        samples = []
        all_frames = set(range(0, 1500, 10))  # 所有可能的帧
        positive_frames = set(annotations.keys())
        negative_frames = list(all_frames - positive_frames)
        
        # 随机选择一些负样本
        for _ in range(min(5, len(negative_frames) // 50)):
            start_frame = random.choice(negative_frames)
            video_frames = [start_frame + i * 10 for i in range(5)]
            video_annotations = [[] for _ in range(5)]  # 全部为空
            
            sample = self.create_sample(seq_id, [video_frames], [video_annotations], "negative")
            samples.append(sample)
        
        return samples
    
    def create_augmented_samples(self, seq_id, annotations):
        """创建数据增强样本"""
        samples = []
        
        # 随机选择一些有标注的帧进行增强
        annotated_frames = list(annotations.keys())
        for _ in range(min(3, len(annotated_frames) // 20)):
            start_frame = random.choice(annotated_frames)
            video_frames = [start_frame + i * 10 for i in range(5)]
            
            # 对标注进行轻微扰动
            video_annotations = []
            for i, frame_idx in enumerate(video_frames):
                if frame_idx in annotations:
                    augmented_annotations = []
                    for ann in annotations[frame_idx]:
                        # 添加轻微的坐标噪声
                        noise_x = random.uniform(-0.02, 0.02)
                        noise_y = random.uniform(-0.02, 0.02)
                        
                        augmented_ann = ann.copy()
                        augmented_ann['x_center'] = max(0, min(1, ann['x_center'] + noise_x))
                        augmented_ann['y_center'] = max(0, min(1, ann['y_center'] + noise_y))
                        augmented_annotations.append(augmented_ann)
                    
                    video_annotations.append(augmented_annotations)
                else:
                    video_annotations.append([])
            
            sample = self.create_sample(seq_id, [video_frames], [video_annotations], "augmented")
            samples.append(sample)
        
        return samples
    
    def create_sample(self, seq_id, all_video_frames, all_video_annotations, sample_type):
        """创建单个训练样本"""
        # 构建视频路径
        content = []
        for video_frames in all_video_frames:
            video_path = f"dataset/interval_5frame_videos/{seq_id}/{seq_id}_interval10_5frames_{video_frames[0]:04d}_{video_frames[-1]:04d}.mp4"
            content.append({
                "type": "video",
                "video": video_path
            })
        
        # 构建指令文本
        num_videos = len(all_video_frames)
        instruction = f"""你是专业的红外弱小目标视频检测专家。请检测这{num_videos}个5帧视频片段中的弱小目标。

视频信息:
- 序列: {seq_id}
- 视频数量: {num_videos}个
- 每个视频: 5帧，隔10帧采样
- 样本类型: {sample_type}

请检测所有视频中的弱小目标，包括：drone（无人机）、car（汽车）、ship（船只）、bus（公交车）、pedestrian（行人）、cyclist（骑行者）。

输出格式：每行一个检测结果
video_idx frame_idx class_name x_center y_center width height confidence

注意：
1. video_idx为视频索引（0-{num_videos-1}）
2. frame_idx为视频内帧索引（0-4）
3. 坐标为相对坐标（0-1之间）
4. 只检测尺寸在3-80像素之间的弱小目标
5. 如果某帧无目标，不输出该帧的结果
6. confidence为检测置信度（0.5-1.0之间）"""
        
        content.append({
            "type": "text",
            "text": instruction
        })
        
        # 构建标注文本
        annotation_lines = []
        for video_idx, video_annotations in enumerate(all_video_annotations):
            for frame_idx, frame_annotations in enumerate(video_annotations):
                for ann in frame_annotations:
                    # 添加随机置信度
                    confidence = round(random.uniform(0.7, 1.0), 2)
                    line = f"{video_idx} {frame_idx} {ann['class_name']} {ann['x_center']:.6f} {ann['y_center']:.6f} {ann['width']:.6f} {ann['height']:.6f} {confidence}"
                    annotation_lines.append(line)
        
        annotation_text = '\n'.join(annotation_lines) if annotation_lines else '未检测到目标。'
        
        return {
            "messages": [
                {
                    "role": "user",
                    "content": content
                },
                {
                    "role": "assistant",
                    "content": annotation_text
                }
            ]
        }
    
    def generate_improved_data(self, output_file='data/improved_train_data.json'):
        """生成改进的训练数据"""
        logger.info("开始生成改进的训练数据")
        
        samples = self.create_diverse_training_samples()
        
        # 保存数据
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w') as f:
            json.dump(samples, f, indent=2, ensure_ascii=False)
        
        logger.info(f"改进的训练数据已保存到: {output_file}")
        
        # 统计信息
        self.print_statistics(samples)
        
        return samples
    
    def print_statistics(self, samples):
        """打印统计信息"""
        print("\n=== 改进训练数据统计 ===")
        print(f"总样本数: {len(samples)}")
        
        # 按类型统计
        type_counts = {}
        seq_counts = {}
        
        for sample in samples:
            # 从指令中提取类型和序列
            instruction = sample['messages'][0]['content'][-1]['text']
            
            if 'single' in instruction:
                sample_type = 'single'
            elif 'multi' in instruction:
                sample_type = 'multi'
            elif 'negative' in instruction:
                sample_type = 'negative'
            elif 'augmented' in instruction:
                sample_type = 'augmented'
            else:
                sample_type = 'unknown'
            
            type_counts[sample_type] = type_counts.get(sample_type, 0) + 1
            
            # 提取序列ID
            for line in instruction.split('\n'):
                if '序列:' in line:
                    seq_id = line.split('序列:')[1].strip()
                    seq_counts[seq_id] = seq_counts.get(seq_id, 0) + 1
                    break
        
        print("\n按类型统计:")
        for sample_type, count in sorted(type_counts.items()):
            print(f"  {sample_type}: {count}个")
        
        print("\n按序列统计:")
        for seq_id, count in sorted(seq_counts.items()):
            print(f"  {seq_id}: {count}个")

def main():
    """主函数"""
    print("=== 改进的训练数据生成器 ===")
    
    generator = ImprovedDataGenerator()
    samples = generator.generate_improved_data()
    
    print(f"\n✅ 生成完成! 共 {len(samples)} 个改进的训练样本")

if __name__ == "__main__":
    main()
