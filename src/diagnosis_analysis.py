#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断分析脚本
分析为什么用答案微调却得不出答案的问题
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_training_vs_detection():
    """分析训练数据与检测结果的差异"""
    print("=== 训练数据 vs 检测结果诊断分析 ===")
    
    # 加载训练数据
    with open("data/interval_5frame_video_train_data.json", 'r') as f:
        train_data = json.load(f)
    
    # 分析第一个训练样本
    sample = train_data[0]
    video_path = sample['messages'][0]['content'][0]['video']
    expected_output = sample['messages'][1]['content']
    
    print(f"\n📹 训练样本分析:")
    print(f"视频路径: {video_path}")
    print(f"期望输出: {expected_output[:100]}...")
    
    # 解析期望输出
    expected_detections = []
    for line in expected_output.strip().split('\n'):
        parts = line.split()
        if len(parts) >= 6:
            expected_detections.append({
                'video_frame_idx': int(parts[0]),
                'class_name': parts[1],
                'x_center': float(parts[2]),
                'y_center': float(parts[3]),
                'width': float(parts[4]),
                'height': float(parts[5])
            })
    
    print(f"期望检测数量: {len(expected_detections)}")
    
    # 检查对应的检测结果
    seq_id = video_path.split('/')[-2]  # 从路径提取序列ID
    detection_file = f"results/interval_5frame_detection/{seq_id}_detections.json"
    
    if os.path.exists(detection_file):
        with open(detection_file, 'r') as f:
            actual_detections = json.load(f)
        
        print(f"\n🎯 实际检测结果:")
        print(f"检测数量: {len(actual_detections)}")
        
        # 分析前几个检测结果
        for i, det in enumerate(actual_detections[:5]):
            print(f"检测{i+1}: frame_idx={det.get('original_frame_idx', 'N/A')}, "
                  f"class={det.get('class_name', 'N/A')}, "
                  f"pos=({det.get('x_center', 0):.3f}, {det.get('y_center', 0):.3f})")
    
    return expected_detections, actual_detections if os.path.exists(detection_file) else []

def analyze_coordinate_precision():
    """分析坐标精度问题"""
    print(f"\n=== 坐标精度分析 ===")
    
    # 检查训练数据中的坐标精度
    with open("data/interval_5frame_video_train_data.json", 'r') as f:
        train_data = json.load(f)
    
    coord_precisions = []
    for sample in train_data[:10]:  # 分析前10个样本
        content = sample['messages'][1]['content']
        for line in content.strip().split('\n'):
            parts = line.split()
            if len(parts) >= 6:
                x_center = float(parts[2])
                y_center = float(parts[3])
                # 计算小数位数
                x_decimals = len(str(x_center).split('.')[-1]) if '.' in str(x_center) else 0
                y_decimals = len(str(y_center).split('.')[-1]) if '.' in str(y_center) else 0
                coord_precisions.append((x_decimals, y_decimals))
    
    if coord_precisions:
        avg_x_precision = sum(p[0] for p in coord_precisions) / len(coord_precisions)
        avg_y_precision = sum(p[1] for p in coord_precisions) / len(coord_precisions)
        print(f"训练数据平均坐标精度: X={avg_x_precision:.1f}位, Y={avg_y_precision:.1f}位")

def analyze_iou_threshold_sensitivity():
    """分析IoU阈值敏感性"""
    print(f"\n=== IoU阈值敏感性分析 ===")
    
    # 模拟不同IoU阈值下的匹配情况
    # 这里简化分析，实际需要完整的IoU计算
    thresholds = [0.1, 0.3, 0.5, 0.7, 0.9]
    print("建议测试不同IoU阈值:")
    for thresh in thresholds:
        print(f"  - IoU阈值 {thresh}: 可能提高匹配率")

def generate_optimization_recommendations():
    """生成优化建议"""
    print(f"\n=== 🔧 优化建议 ===")
    
    recommendations = [
        {
            "问题": "帧索引映射错误",
            "原因": "训练时使用video_frame_idx(0-4)，检测时使用original_frame_idx，评估时期望真实帧号",
            "解决方案": "修复帧索引映射，确保训练、检测、评估使用一致的帧标识",
            "优先级": "🔥 紧急"
        },
        {
            "问题": "IoU阈值过高",
            "原因": "当前使用0.5的IoU阈值，对于弱小目标可能过于严格",
            "解决方案": "降低IoU阈值到0.1-0.3，或使用多个阈值评估",
            "优先级": "🔥 紧急"
        },
        {
            "问题": "坐标精度损失",
            "原因": "模型输出的坐标精度可能不如训练数据",
            "解决方案": "增加坐标回归的权重，或使用更精确的坐标表示",
            "优先级": "⚠️ 重要"
        },
        {
            "问题": "类别名称不匹配",
            "原因": "模型输出的类别名称可能与真实标注不完全一致",
            "解决方案": "标准化类别名称，增加类别名称的容错匹配",
            "优先级": "⚠️ 重要"
        },
        {
            "问题": "训练数据不足",
            "原因": "110个训练样本可能不足以让模型充分学习",
            "解决方案": "增加数据增强，或生成更多训练样本",
            "优先级": "💡 建议"
        },
        {
            "问题": "模型容量问题",
            "原因": "LoRA可能限制了模型的学习能力",
            "解决方案": "增加LoRA的rank，或尝试全参数微调",
            "优先级": "💡 建议"
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n{i}. {rec['优先级']} {rec['问题']}")
        print(f"   原因: {rec['原因']}")
        print(f"   解决方案: {rec['解决方案']}")

def main():
    """主函数"""
    print("🔍 开始诊断分析...")
    
    # 分析训练vs检测
    expected, actual = analyze_training_vs_detection()
    
    # 分析坐标精度
    analyze_coordinate_precision()
    
    # 分析IoU敏感性
    analyze_iou_threshold_sensitivity()
    
    # 生成优化建议
    generate_optimization_recommendations()
    
    print(f"\n📋 诊断完成！")
    print(f"💡 关键发现: 这不是模型能力问题，而是评估匹配问题！")
    print(f"🎯 优先修复帧索引映射和IoU阈值，应该能立即看到显著改善！")

if __name__ == "__main__":
    main()
