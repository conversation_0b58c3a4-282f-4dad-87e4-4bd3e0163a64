#!/usr/bin/env python3
"""
改进的训练器 - 解决过拟合问题
"""

import torch
import json
import os
from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
from transformers import TrainingArguments, Trainer
from peft import LoraConfig, get_peft_model, TaskType
from qwen_vl_utils import process_vision_info
import logging
from torch.utils.data import Dataset
import random

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedVideoDataset(Dataset):
    def __init__(self, data, processor, max_length=2048):
        self.data = data
        self.processor = processor
        self.max_length = max_length
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        messages = sample['messages']
        
        # 应用聊天模板
        text = self.processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        
        # 确保video_inputs是列表格式
        if video_inputs is not None and not isinstance(video_inputs, list):
            video_inputs = [video_inputs]
        
        # 处理输入
        inputs = self.processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt"
        )
        
        # 准备标签
        input_ids = inputs['input_ids'][0]
        labels = input_ids.clone()
        
        # 找到assistant回复的开始位置
        assistant_start = None
        for i in range(len(input_ids) - 1):
            if input_ids[i] == self.processor.tokenizer.convert_tokens_to_ids('<|im_start|>') and \
               input_ids[i + 1] == self.processor.tokenizer.convert_tokens_to_ids('assistant'):
                assistant_start = i + 2
                break
        
        if assistant_start is not None:
            # 只计算assistant回复部分的损失
            labels[:assistant_start] = -100
        
        return {
            'input_ids': input_ids,
            'attention_mask': inputs['attention_mask'][0],
            'pixel_values': inputs.get('pixel_values', [None])[0] if inputs.get('pixel_values') else None,
            'image_grid_thw': inputs.get('image_grid_thw', [None])[0] if inputs.get('image_grid_thw') else None,
            'video_grid_thw': inputs.get('video_grid_thw', [None])[0] if inputs.get('video_grid_thw') else None,
            'labels': labels
        }

class ImprovedTrainer:
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.base_model = "qwen2.5-vl-7b"
        self.output_dir = "output/improved_video_lora"
        
    def setup_model_and_processor(self):
        """设置模型和处理器"""
        logger.info(f"加载基础模型: {self.base_model}")
        
        # 加载模型
        self.model = Qwen2VLForConditionalGeneration.from_pretrained(
            self.base_model,
            torch_dtype=torch.bfloat16,
            device_map="auto"
        )
        
        # 加载处理器
        self.processor = AutoProcessor.from_pretrained(self.base_model)
        
        # 配置LoRA - 更保守的参数
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=16,  # 降低rank，减少过拟合
            lora_alpha=32,  # 降低alpha
            lora_dropout=0.2,  # 增加dropout
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
            bias="none",
        )
        
        # 应用LoRA
        self.model = get_peft_model(self.model, lora_config)
        self.model.print_trainable_parameters()
        
    def load_and_prepare_data(self):
        """加载和准备训练数据"""
        logger.info("加载改进的训练数据...")
        
        # 检查数据文件
        train_file = "data/improved_train_data.json"
        if not os.path.exists(train_file):
            logger.error("改进的训练数据不存在，请先运行数据生成器")
            return None, None
        
        with open(train_file, 'r', encoding='utf-8') as f:
            all_data = json.load(f)
        
        # 打乱数据
        random.shuffle(all_data)
        
        # 分割训练和验证数据 (90:10)
        split_idx = int(len(all_data) * 0.9)
        train_data = all_data[:split_idx]
        val_data = all_data[split_idx:]
        
        logger.info(f"训练样本: {len(train_data)}, 验证样本: {len(val_data)}")
        
        # 创建数据集
        train_dataset = ImprovedVideoDataset(train_data, self.processor)
        val_dataset = ImprovedVideoDataset(val_data, self.processor)
        
        return train_dataset, val_dataset
    
    def setup_training_args(self):
        """设置训练参数"""
        return TrainingArguments(
            output_dir=self.output_dir,
            num_train_epochs=3,  # 减少训练轮数
            per_device_train_batch_size=1,
            per_device_eval_batch_size=1,
            gradient_accumulation_steps=8,
            learning_rate=1e-5,  # 降低学习率
            weight_decay=0.01,  # 增加权重衰减
            warmup_steps=100,
            logging_steps=10,
            eval_steps=50,
            save_steps=100,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            remove_unused_columns=False,
            dataloader_pin_memory=False,
            gradient_checkpointing=True,
            fp16=False,
            bf16=True,
            max_grad_norm=1.0,  # 梯度裁剪
            label_smoothing_factor=0.1,  # 标签平滑
        )
    
    def data_collator(self, features):
        """数据整理器"""
        batch = {}
        
        # 处理文本输入
        input_ids = [f['input_ids'] for f in features]
        attention_mask = [f['attention_mask'] for f in features]
        labels = [f['labels'] for f in features]
        
        # 填充到相同长度
        max_length = max(len(ids) for ids in input_ids)
        
        padded_input_ids = []
        padded_attention_mask = []
        padded_labels = []
        
        for i in range(len(input_ids)):
            pad_length = max_length - len(input_ids[i])
            
            padded_input_ids.append(torch.cat([
                input_ids[i], 
                torch.full((pad_length,), self.processor.tokenizer.pad_token_id)
            ]))
            
            padded_attention_mask.append(torch.cat([
                attention_mask[i],
                torch.zeros(pad_length)
            ]))
            
            padded_labels.append(torch.cat([
                labels[i],
                torch.full((pad_length,), -100)
            ]))
        
        batch['input_ids'] = torch.stack(padded_input_ids)
        batch['attention_mask'] = torch.stack(padded_attention_mask)
        batch['labels'] = torch.stack(padded_labels)
        
        # 处理视觉输入
        pixel_values = [f['pixel_values'] for f in features if f['pixel_values'] is not None]
        if pixel_values:
            batch['pixel_values'] = torch.stack(pixel_values)
        
        image_grid_thw = [f['image_grid_thw'] for f in features if f['image_grid_thw'] is not None]
        if image_grid_thw:
            batch['image_grid_thw'] = torch.stack(image_grid_thw)
        
        video_grid_thw = [f['video_grid_thw'] for f in features if f['video_grid_thw'] is not None]
        if video_grid_thw:
            batch['video_grid_thw'] = torch.stack(video_grid_thw)
        
        return batch
    
    def train(self):
        """执行训练"""
        logger.info("开始改进的视频检测训练...")
        
        # 设置模型和处理器
        self.setup_model_and_processor()
        
        # 加载数据
        train_dataset, val_dataset = self.load_and_prepare_data()
        if train_dataset is None:
            return
        
        # 设置训练参数
        training_args = self.setup_training_args()
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            data_collator=self.data_collator,
        )
        
        # 开始训练
        logger.info("开始训练...")
        trainer.train()
        
        # 保存模型
        logger.info("保存模型...")
        trainer.save_model()
        
        logger.info(f"训练完成！模型保存到: {self.output_dir}")

def main():
    """主函数"""
    print("=== 改进的视频检测训练器 ===")
    
    trainer = ImprovedTrainer()
    trainer.train()

if __name__ == "__main__":
    main()
