#!/usr/bin/env python3
"""
快速调试 - 分析检测结果与真实标注的差异
"""

import json
import numpy as np
from pathlib import Path

def load_detection_results():
    """加载检测结果"""
    detection_file = Path("results/interval_5frame_detection/data01_detections.json")
    
    if not detection_file.exists():
        print("检测结果文件不存在")
        return []
        
    with open(detection_file, 'r') as f:
        detections = json.load(f)
        
    return detections

def load_ground_truth(frame_idx):
    """加载真实标注"""
    label_file = Path(f"dataset/labels/data01/{frame_idx}.txt")
    
    if not label_file.exists():
        return []
        
    annotations = []
    with open(label_file, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            parts = line.split()
            if len(parts) >= 5:
                annotations.append({
                    'class_id': int(parts[0]),
                    'x_center': float(parts[1]),
                    'y_center': float(parts[2]),
                    'width': float(parts[3]),
                    'height': float(parts[4])
                })
                
    return annotations

def analyze_detection_pattern():
    """分析检测模式"""
    print("=== 检测模式分析 ===")
    
    detections = load_detection_results()
    
    if not detections:
        print("没有检测结果")
        return
        
    print(f"总检测数量: {len(detections)}")
    
    # 分析前10个检测结果
    print("\n前10个检测结果:")
    for i, det in enumerate(detections[:10]):
        frame_idx = det['original_frame_idx']
        print(f"  {i+1}. 帧{frame_idx}: ({det['x_center']:.6f}, {det['y_center']:.6f}) 尺寸({det['width']:.6f}, {det['height']:.6f})")
        
        # 加载对应的真实标注
        gt_annotations = load_ground_truth(frame_idx)
        if gt_annotations:
            for j, gt in enumerate(gt_annotations):
                distance = np.sqrt((det['x_center'] - gt['x_center'])**2 + (det['y_center'] - gt['y_center'])**2)
                print(f"      GT{j+1}: ({gt['x_center']:.6f}, {gt['y_center']:.6f}) 距离={distance:.6f}")
        else:
            print(f"      无真实标注")
            
    # 分析坐标分布
    print(f"\n坐标分布分析:")
    x_coords = [det['x_center'] for det in detections]
    y_coords = [det['y_center'] for det in detections]
    
    print(f"X坐标: 最小={min(x_coords):.6f}, 最大={max(x_coords):.6f}, 平均={np.mean(x_coords):.6f}")
    print(f"Y坐标: 最小={min(y_coords):.6f}, 最大={max(y_coords):.6f}, 平均={np.mean(y_coords):.6f}")
    
    # 检查是否有固定模式
    unique_x = len(set([round(x, 6) for x in x_coords]))
    unique_y = len(set([round(y, 6) for y in y_coords]))
    
    print(f"唯一X坐标数量: {unique_x}")
    print(f"唯一Y坐标数量: {unique_y}")
    
    if unique_x < 10:
        print("⚠️  X坐标变化很少，可能存在固定模式")
    if unique_y < 10:
        print("⚠️  Y坐标变化很少，可能存在固定模式")

def analyze_ground_truth_pattern():
    """分析真实标注模式"""
    print("\n=== 真实标注模式分析 ===")
    
    all_gt = []
    
    # 检查前100帧的真实标注
    for frame_idx in range(100):
        gt_annotations = load_ground_truth(frame_idx)
        for gt in gt_annotations:
            gt['frame_idx'] = frame_idx
            all_gt.append(gt)
            
    if not all_gt:
        print("没有找到真实标注")
        return
        
    print(f"前100帧中找到 {len(all_gt)} 个真实目标")
    
    # 分析前10个真实标注
    print("\n前10个真实标注:")
    for i, gt in enumerate(all_gt[:10]):
        print(f"  {i+1}. 帧{gt['frame_idx']}: ({gt['x_center']:.6f}, {gt['y_center']:.6f}) 尺寸({gt['width']:.6f}, {gt['height']:.6f})")
        
    # 分析坐标分布
    x_coords = [gt['x_center'] for gt in all_gt]
    y_coords = [gt['y_center'] for gt in all_gt]
    
    print(f"\n真实标注坐标分布:")
    print(f"X坐标: 最小={min(x_coords):.6f}, 最大={max(x_coords):.6f}, 平均={np.mean(x_coords):.6f}")
    print(f"Y坐标: 最小={min(y_coords):.6f}, 最大={max(y_coords):.6f}, 平均={np.mean(y_coords):.6f}")

def compare_patterns():
    """对比检测和真实标注的模式"""
    print("\n=== 模式对比 ===")
    
    detections = load_detection_results()
    
    # 统计检测结果的坐标范围
    if detections:
        det_x = [det['x_center'] for det in detections]
        det_y = [det['y_center'] for det in detections]
        
        print(f"检测结果坐标范围:")
        print(f"  X: [{min(det_x):.3f}, {max(det_x):.3f}]")
        print(f"  Y: [{min(det_y):.3f}, {max(det_y):.3f}]")
        
    # 统计真实标注的坐标范围
    all_gt = []
    for frame_idx in range(100):
        gt_annotations = load_ground_truth(frame_idx)
        all_gt.extend(gt_annotations)
        
    if all_gt:
        gt_x = [gt['x_center'] for gt in all_gt]
        gt_y = [gt['y_center'] for gt in all_gt]
        
        print(f"真实标注坐标范围:")
        print(f"  X: [{min(gt_x):.3f}, {max(gt_x):.3f}]")
        print(f"  Y: [{min(gt_y):.3f}, {max(gt_y):.3f}]")
        
        # 计算偏移
        if detections:
            det_x_mean = np.mean(det_x)
            det_y_mean = np.mean(det_y)
            gt_x_mean = np.mean(gt_x)
            gt_y_mean = np.mean(gt_y)
            
            x_offset = det_x_mean - gt_x_mean
            y_offset = det_y_mean - gt_y_mean
            
            print(f"\n平均偏移:")
            print(f"  X偏移: {x_offset:.6f}")
            print(f"  Y偏移: {y_offset:.6f}")
            print(f"  总偏移距离: {np.sqrt(x_offset**2 + y_offset**2):.6f}")

def suggest_improvements():
    """建议改进方案"""
    print("\n=== 改进建议 ===")
    
    detections = load_detection_results()
    
    if not detections:
        print("1. 模型没有产生检测结果，需要检查模型训练")
        return
        
    # 检查检测结果的多样性
    x_coords = [det['x_center'] for det in detections]
    y_coords = [det['y_center'] for det in detections]
    
    unique_x = len(set([round(x, 4) for x in x_coords]))
    unique_y = len(set([round(y, 4) for y in y_coords]))
    
    if unique_x < 5 or unique_y < 5:
        print("1. 检测结果缺乏多样性，建议:")
        print("   - 增加训练数据的多样性")
        print("   - 调整训练参数，增加学习率")
        print("   - 检查数据增强策略")
        
    # 检查坐标偏移
    all_gt = []
    for frame_idx in range(50):
        gt_annotations = load_ground_truth(frame_idx)
        all_gt.extend(gt_annotations)
        
    if all_gt:
        det_x_mean = np.mean(x_coords)
        det_y_mean = np.mean(y_coords)
        gt_x_mean = np.mean([gt['x_center'] for gt in all_gt])
        gt_y_mean = np.mean([gt['y_center'] for gt in all_gt])
        
        offset_distance = np.sqrt((det_x_mean - gt_x_mean)**2 + (det_y_mean - gt_y_mean)**2)
        
        if offset_distance > 0.1:
            print("2. 存在系统性坐标偏移，建议:")
            print("   - 检查训练数据的坐标处理")
            print("   - 验证数据预处理的一致性")
            print("   - 考虑添加坐标校正机制")
            
    print("3. 通用改进建议:")
    print("   - 增加训练轮数")
    print("   - 使用更大的学习率")
    print("   - 增加训练数据量")
    print("   - 考虑使用数据增强")

def main():
    """主函数"""
    print("=== 快速调试分析 ===")
    
    analyze_detection_pattern()
    analyze_ground_truth_pattern()
    compare_patterns()
    suggest_improvements()

if __name__ == "__main__":
    main()
