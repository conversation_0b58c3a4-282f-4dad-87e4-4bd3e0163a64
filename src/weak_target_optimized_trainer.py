#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
弱小目标优化训练器
专门针对弱小目标检测的根本性优化
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any
from PIL import Image, ImageEnhance
from transformers import (
    Qwen2_5_VLForConditionalGeneration,
    AutoProcessor,
)
from peft import LoraConfig, get_peft_model, TaskType
import gc
import re
import random
import numpy as np

# 强制使用GPU卡2
os.environ['CUDA_VISIBLE_DEVICES'] = '2'
torch.cuda.set_device(0)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """清理显存"""
    gc.collect()
    torch.cuda.empty_cache()

def get_image_files(seq_dir: Path, seq_info: Dict) -> List[Path]:
    """获取图像文件列表"""
    if seq_info['naming'] == 'simple':
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        return sorted(image_files, key=lambda x: int(x.stem))
    else:
        image_files = list(seq_dir.glob(f"*{seq_info['format']}"))
        def complex_sort_key(path):
            numbers = re.findall(r'(\d+)', path.name)
            return int(numbers[-1]) if numbers else 0
        return sorted(image_files, key=complex_sort_key)

def load_frame_annotation(seq_id, frame_id, size):
    """加载帧标注并验证"""
    labels_dir = Path(f"dataset/labels/{seq_id}")
    label_file = labels_dir / f"{frame_id}.txt"
    
    if not label_file.exists():
        return []
    
    with open('dataset/class.json', 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    
    annotations = []
    try:
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 转换为绝对坐标
                    img_width, img_height = size
                    x1 = int((x_center - width/2) * img_width)
                    y1 = int((y_center - height/2) * img_height)
                    x2 = int((x_center + width/2) * img_width)
                    y2 = int((y_center + height/2) * img_height)
                    
                    # 严格验证bbox
                    x1 = max(0, min(x1, img_width-1))
                    y1 = max(0, min(y1, img_height-1))
                    x2 = max(x1+1, min(x2, img_width))
                    y2 = max(y1+1, min(y2, img_height))
                    
                    # 验证是否为弱小目标
                    w = x2 - x1
                    h = y2 - y1
                    if 3 <= w <= 50 and 3 <= h <= 50:  # 弱小目标尺寸范围
                        class_name = class_map.get(str(class_id), "unknown")
                        annotations.append({
                            "bbox": [x1, y1, x2, y2],
                            "label": class_name,
                            "frame_id": frame_id,
                            "size": w * h
                        })
    except Exception as e:
        logger.warning(f"加载标注文件失败 {label_file}: {e}")
    
    return annotations

def enhance_image_for_weak_targets(image_path: str) -> Image.Image:
    """增强图像以突出弱小目标"""
    img = Image.open(image_path).convert('RGB')
    
    # 对比度增强
    enhancer = ImageEnhance.Contrast(img)
    img = enhancer.enhance(1.5)
    
    # 锐化
    enhancer = ImageEnhance.Sharpness(img)
    img = enhancer.enhance(1.3)
    
    return img

def generate_weak_target_optimized_data():
    """生成专门针对弱小目标优化的训练数据"""
    logger.info("生成弱小目标优化训练数据...")
    
    sequences = {
        'data01': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data02': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data04': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data06': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data07': {'size': (256, 256), 'format': '.bmp', 'naming': 'simple'},
        'data19': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data23': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'},
        'data26': {'size': (640, 512), 'format': '.jpg', 'naming': 'complex'}
    }
    
    all_training_samples = []
    
    for seq_id, seq_info in sequences.items():
        print(f"处理序列 {seq_id}...")
        
        images_dir = Path(f"dataset/images/{seq_id}")
        image_files = get_image_files(images_dir, seq_info)
        
        # 高质量单帧样本 - 专注弱小目标
        annotated_frames = []
        for i in range(0, min(len(image_files), 200), 3):  # 每3帧检查一次
            frame_file = image_files[i]
            frame_id = frame_file.stem
            
            annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
            if annotations:  # 只保留有弱小目标标注的帧
                annotated_frames.append((frame_file, frame_id, annotations))
        
        print(f"  序列 {seq_id} 找到 {len(annotated_frames)} 个有弱小目标的帧")
        
        # 生成单帧弱小目标检测样本
        for frame_file, frame_id, annotations in annotated_frames:
            # 专门针对弱小目标的提示词
            training_sample = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "image": str(frame_file)
                            },
                            {
                                "type": "text",
                                "text": f"""你是专业的红外弱小目标检测专家。请精确检测这张红外图像中的所有弱小目标。

重要提示：
- 这是红外图像，弱小目标通常表现为小亮点或小暗点
- 目标非常小，通常只有3-50像素
- 需要仔细观察每个可疑的小点
- 图像尺寸：{seq_info['size']}

弱小目标特征：
- 尺寸：3-50像素（非常小）
- 外观：红外图像中的亮点、暗点或小块
- 形状：点状、小矩形、小椭圆
- 类别：drone, car, ship, bus, pedestrian, cyclist

检测要求：
1. 仔细扫描整个图像，特别关注小亮点
2. 边界框要紧贴目标，不要过大
3. 坐标必须精确，格式[x1,y1,x2,y2]
4. 如果没有目标，输出[]

输出格式（严格JSON）：
[{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]

开始检测："""
                            }
                        ]
                    },
                    {
                        "role": "assistant",
                        "content": json.dumps(annotations, ensure_ascii=False)
                    }
                ]
            }
            
            all_training_samples.append(training_sample)
            print(f"  ✅ 生成弱小目标样本: 帧{frame_id}, {len(annotations)}个目标")
        
        # 负样本 - 没有目标的帧
        negative_samples = 0
        for i in range(5, min(len(image_files), 100), 15):
            frame_file = image_files[i]
            frame_id = frame_file.stem
            
            annotations = load_frame_annotation(seq_id, frame_id, seq_info['size'])
            if not annotations and negative_samples < 5:  # 限制负样本数量
                negative_sample = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image",
                                    "image": str(frame_file)
                                },
                                {
                                    "type": "text",
                                    "text": f"""你是专业的红外弱小目标检测专家。请精确检测这张红外图像中的所有弱小目标。

重要提示：
- 这是红外图像，弱小目标通常表现为小亮点或小暗点
- 目标非常小，通常只有3-50像素
- 需要仔细观察每个可疑的小点
- 图像尺寸：{seq_info['size']}

弱小目标特征：
- 尺寸：3-50像素（非常小）
- 外观：红外图像中的亮点、暗点或小块
- 形状：点状、小矩形、小椭圆
- 类别：drone, car, ship, bus, pedestrian, cyclist

检测要求：
1. 仔细扫描整个图像，特别关注小亮点
2. 边界框要紧贴目标，不要过大
3. 坐标必须精确，格式[x1,y1,x2,y2]
4. 如果没有目标，输出[]

输出格式（严格JSON）：
[{{"bbox":[x1,y1,x2,y2],"label":"类别"}}]

开始检测："""
                                }
                            ]
                        },
                        {
                            "role": "assistant",
                            "content": "[]"
                        }
                    ]
                }
                
                all_training_samples.append(negative_sample)
                negative_samples += 1
                print(f"  ✅ 生成负样本: 帧{frame_id}")
        
        # 时序样本 - 每隔10帧抽取
        if len(annotated_frames) >= 3:
            for start_idx in range(0, len(annotated_frames)-2, 2):
                sequence_frames = annotated_frames[start_idx:start_idx+3]
                
                sequence_annotations = []
                sequence_frame_paths = []
                sequence_frame_ids = []
                
                for frame_file, frame_id, annotations in sequence_frames:
                    sequence_frame_paths.append(str(frame_file))
                    sequence_frame_ids.append(frame_id)
                    for ann in annotations:
                        ann_copy = ann.copy()
                        ann_copy['frame_id'] = frame_id
                        sequence_annotations.append(ann_copy)
                
                if sequence_annotations:
                    content = []
                    for frame_path in sequence_frame_paths:
                        content.append({
                            "type": "image",
                            "image": frame_path
                        })
                    
                    content.append({
                        "type": "text",
                        "text": f"""你是专业的红外弱小目标时序检测专家。请检测这个视频序列中的弱小目标。

序列信息：
- 序列：{seq_id}
- 帧数：{len(sequence_frame_paths)}
- 帧编号：{sequence_frame_ids}
- 图像尺寸：{seq_info['size']}

弱小目标特征：
- 尺寸：3-50像素（非常小）
- 外观：红外图像中的亮点、暗点
- 时序性：目标可能在帧间移动
- 类别：drone, car, ship, bus, pedestrian, cyclist

检测要求：
1. 检测每帧中的所有弱小目标
2. 考虑目标的时序连续性
3. 为每个检测标注frame_id
4. 边界框要小而精确

输出格式（严格JSON）：
[{{"bbox":[x1,y1,x2,y2],"label":"类别","frame_id":"帧号"}}]

开始检测："""
                    })
                    
                    sequence_sample = {
                        "messages": [
                            {
                                "role": "user",
                                "content": content
                            },
                            {
                                "role": "assistant",
                                "content": json.dumps(sequence_annotations, ensure_ascii=False)
                            }
                        ]
                    }
                    
                    all_training_samples.append(sequence_sample)
                    print(f"  ✅ 生成时序样本: {len(sequence_frame_paths)}帧, {len(sequence_annotations)}个目标")
    
    # 随机打乱样本
    random.shuffle(all_training_samples)
    
    # 保存优化训练数据
    output_path = "data/weak_target_optimized_data.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_training_samples, f, ensure_ascii=False, indent=2)
    
    logger.info(f"弱小目标优化训练数据保存到: {output_path}")
    logger.info(f"总训练样本数: {len(all_training_samples)}")
    
    return True

class WeakTargetOptimizedTrainer:
    """弱小目标优化训练器"""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = "cuda"
        
        clear_memory()
        
        logger.info("加载弱小目标优化模型...")
        
        self.processor = AutoProcessor.from_pretrained(model_path)
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True,
            use_cache=False,
        )
        
        logger.info("弱小目标优化模型加载完成")
    
    def setup_lora(self):
        """设置专门针对弱小目标的LoRA"""
        config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=24,  # 增加表达能力
            lora_alpha=24,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj"],  # 更多模块
            lora_dropout=0.05,  # 降低dropout
        )
        
        self.model = get_peft_model(self.model, config)
        self.model.print_trainable_parameters()
        logger.info("弱小目标优化LoRA配置完成")
    
    def weak_target_optimized_train(self, train_data_path: str, output_dir: str):
        """弱小目标优化训练"""
        logger.info("开始弱小目标优化微调...")
        
        clear_memory()
        
        # 设置LoRA
        self.setup_lora()
        
        # 加载训练数据
        with open(train_data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 稳定的训练参数
        optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=2e-5,  # 降低学习率避免NaN
            weight_decay=0.01,
            betas=(0.9, 0.999),
            eps=1e-8
        )

        # 使用稳定的学习率调度
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=len(data)*8
        )
        
        # 训练循环
        self.model.train()
        
        for epoch in range(8):  # 稳定的训练轮数
            logger.info(f"弱小目标优化训练轮次 {epoch + 1}/8")
            
            epoch_loss = 0
            valid_samples = 0
            
            for i, sample in enumerate(data):
                messages = sample["messages"]
                
                try:
                    # 处理输入
                    text = self.processor.apply_chat_template(
                        messages, tokenize=False, add_generation_prompt=True
                    )
                    
                    # 处理图像 - 使用增强
                    images = []
                    for content in messages[0]["content"]:
                        if content["type"] == "image":
                            # 使用图像增强
                            img = enhance_image_for_weak_targets(content["image"])
                            img.thumbnail((448, 448), Image.Resampling.LANCZOS)  # 更高分辨率
                            images.append(img)
                    
                    if not images:
                        continue
                    
                    # 处理输入
                    inputs = self.processor(
                        text=[text],
                        images=images,
                        padding=True,
                        return_tensors="pt"
                    )
                    
                    # 移动到设备
                    inputs = {k: v.to(self.device) for k, v in inputs.items()}
                    inputs["labels"] = inputs["input_ids"].clone()
                    
                    # 前向传播
                    optimizer.zero_grad()
                    outputs = self.model(**inputs)
                    loss = outputs.loss
                    
                    # 反向传播 - 更强的梯度裁剪
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)  # 更强的裁剪
                    optimizer.step()
                    scheduler.step()
                    
                    epoch_loss += loss.item()
                    valid_samples += 1
                    
                    if (i + 1) % 5 == 0:
                        logger.info(f"  样本 {i+1}/{len(data)}, 损失: {loss.item():.4f}, LR: {scheduler.get_last_lr()[0]:.6f}")
                    
                    clear_memory()
                    
                except Exception as e:
                    logger.warning(f"处理样本 {i} 时出错: {e}")
                    continue
            
            if valid_samples > 0:
                avg_loss = epoch_loss / valid_samples
                logger.info(f"轮次 {epoch + 1} 平均损失: {avg_loss:.4f}")
        
        # 保存模型
        os.makedirs(output_dir, exist_ok=True)
        self.model.save_pretrained(output_dir)
        self.processor.save_pretrained(output_dir)
        
        logger.info(f"弱小目标优化微调完成，模型保存到: {output_dir}")
        return True

def main():
    """主函数"""
    print("=== 弱小目标优化Qwen2.5-VL微调训练系统 ===")
    print("🎯 目标: 专门针对弱小目标检测的根本性优化")
    print("📊 策略: 高质量标注+图像增强+负样本+优化训练")
    print("🎬 训练方式: 弱小目标专用LoRA微调")
    print("🔧 技术: 图像增强 + 大LoRA + 高学习率 + 负样本")
    print("⏱️  预计时间: 30-40分钟")
    
    # 第1步：生成训练数据
    print("\n第1步：生成弱小目标优化训练数据...")
    if not generate_weak_target_optimized_data():
        print("❌ 训练数据生成失败")
        return False
    
    # 第2步：初始化训练器
    print("\n第2步：初始化弱小目标优化训练器...")
    model_path = "qwen2.5-vl-7b"
    trainer = WeakTargetOptimizedTrainer(model_path)
    
    # 第3步：开始训练
    print("\n第3步：开始弱小目标优化微调...")
    output_dir = "output/weak_target_optimized_qwen_finetuned"
    success = trainer.weak_target_optimized_train("data/weak_target_optimized_data.json", output_dir)
    
    if success:
        print("\n🎉 弱小目标优化微调完成！")
        print(f"📁 模型保存到: {output_dir}")
        print("📋 这是专门针对弱小目标的优化版本")
        print("💡 应该有显著更好的弱小目标检测效果")
    else:
        print("\n❌ 弱小目标优化微调失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
