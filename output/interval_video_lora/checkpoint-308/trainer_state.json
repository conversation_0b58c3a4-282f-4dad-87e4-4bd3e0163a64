{"best_global_step": 200, "best_metric": 1.6213817596435547, "best_model_checkpoint": "output/interval_video_lora/checkpoint-200", "epoch": 4.0, "eval_steps": 100, "global_step": 308, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.26058631921824105, "grad_norm": 48.22927474975586, "learning_rate": 3.3e-06, "loss": 64.8494, "step": 20}, {"epoch": 0.5211726384364821, "grad_norm": 33.44448471069336, "learning_rate": 9e-06, "loss": 62.6736, "step": 40}, {"epoch": 0.7817589576547231, "grad_norm": 141.82627868652344, "learning_rate": 1.5e-05, "loss": 53.8959, "step": 60}, {"epoch": 1.0390879478827362, "grad_norm": 18.07746696472168, "learning_rate": 2.1e-05, "loss": 41.6749, "step": 80}, {"epoch": 1.2996742671009773, "grad_norm": 6.5717644691467285, "learning_rate": 2.7000000000000002e-05, "loss": 28.1478, "step": 100}, {"epoch": 1.2996742671009773, "eval_loss": 2.074031352996826, "eval_runtime": 73.6155, "eval_samples_per_second": 1.087, "eval_steps_per_second": 1.087, "step": 100}, {"epoch": 1.5602605863192183, "grad_norm": 1.0217132568359375, "learning_rate": 2.9829231183113013e-05, "loss": 20.6045, "step": 120}, {"epoch": 1.8208469055374592, "grad_norm": 1.624738097190857, "learning_rate": 2.848632170648139e-05, "loss": 18.9238, "step": 140}, {"epoch": 2.0781758957654723, "grad_norm": 0.5472968816757202, "learning_rate": 2.5922114308136826e-05, "loss": 18.7071, "step": 160}, {"epoch": 2.3387622149837135, "grad_norm": 0.42682546377182007, "learning_rate": 2.236881916625816e-05, "loss": 19.0823, "step": 180}, {"epoch": 2.5993485342019547, "grad_norm": 0.7417168021202087, "learning_rate": 1.8148216530972714e-05, "loss": 18.5999, "step": 200}, {"epoch": 2.5993485342019547, "eval_loss": 1.6213817596435547, "eval_runtime": 84.7697, "eval_samples_per_second": 0.944, "eval_steps_per_second": 0.944, "step": 200}, {"epoch": 2.8599348534201954, "grad_norm": 1.7450122833251953, "learning_rate": 1.3642516862555433e-05, "loss": 18.5271, "step": 220}, {"epoch": 3.1172638436482085, "grad_norm": 0.6816090941429138, "learning_rate": 9.259748514523654e-06, "loss": 19.018, "step": 240}, {"epoch": 3.3778501628664497, "grad_norm": 0.9041086435317993, "learning_rate": 5.396807393062681e-06, "loss": 18.8654, "step": 260}, {"epoch": 3.6384364820846904, "grad_norm": 1.2223176956176758, "learning_rate": 2.403514751589032e-06, "loss": 18.1755, "step": 280}, {"epoch": 3.8990228013029316, "grad_norm": 0.7049526572227478, "learning_rate": 5.509379842872558e-07, "loss": 18.9555, "step": 300}, {"epoch": 3.8990228013029316, "eval_loss": 1.63251531124115, "eval_runtime": 89.7373, "eval_samples_per_second": 0.891, "eval_steps_per_second": 0.891, "step": 300}], "logging_steps": 20, "max_steps": 308, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 200, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 5.786361213670195e+16, "train_batch_size": 1, "trial_name": null, "trial_params": null}