{"best_global_step": 25, "best_metric": 9.742559432983398, "best_model_checkpoint": null, "epoch": 6.0, "eval_steps": 25, "global_step": 42, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.7272727272727273, "grad_norm": NaN, "learning_rate": 0.0, "loss": 190.9841, "step": 5}, {"epoch": 1.4363636363636363, "grad_norm": 372.96038818359375, "learning_rate": 8.000000000000001e-07, "loss": 187.2103, "step": 10}, {"epoch": 2.1454545454545455, "grad_norm": 309.99468994140625, "learning_rate": 4.000000000000001e-06, "loss": 184.8106, "step": 15}, {"epoch": 2.8727272727272726, "grad_norm": 460.9351806640625, "learning_rate": 7.2000000000000005e-06, "loss": 185.4273, "step": 20}, {"epoch": 3.581818181818182, "grad_norm": 231.0150909423828, "learning_rate": 1.1200000000000001e-05, "loss": 176.5972, "step": 25}, {"epoch": 3.581818181818182, "eval_loss": 9.742559432983398, "eval_runtime": 193.911, "eval_samples_per_second": 0.144, "eval_steps_per_second": 0.144, "step": 25}, {"epoch": 4.290909090909091, "grad_norm": 253.07791137695312, "learning_rate": 1.5200000000000002e-05, "loss": 153.1986, "step": 30}, {"epoch": 5.0, "grad_norm": 275.6278076171875, "learning_rate": 1.9200000000000003e-05, "loss": 149.3241, "step": 35}, {"epoch": 5.7272727272727275, "grad_norm": 182.0287322998047, "learning_rate": 1.7390089172206594e-05, "loss": 129.9971, "step": 40}], "logging_steps": 5, "max_steps": 42, "num_input_tokens_seen": 0, "num_train_epochs": 6, "save_steps": 50, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 3.1910576880992256e+16, "train_batch_size": 1, "trial_name": null, "trial_params": null}