{"best_global_step": 150, "best_metric": 3.998192071914673, "best_model_checkpoint": "output/interval_5frame_video_lora/checkpoint-100", "epoch": 12.0, "eval_steps": 50, "global_step": 180, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.6837606837606838, "grad_norm": 338.2708435058594, "learning_rate": 8.000000000000001e-07, "loss": 98.5594, "step": 10}, {"epoch": 1.341880341880342, "grad_norm": 247.86351013183594, "learning_rate": 8.8e-06, "loss": 85.7416, "step": 20}, {"epoch": 2.0, "grad_norm": 166.4970703125, "learning_rate": 1.5200000000000002e-05, "loss": 76.8521, "step": 30}, {"epoch": 2.683760683760684, "grad_norm": 114.16010284423828, "learning_rate": 2.32e-05, "loss": 57.1581, "step": 40}, {"epoch": 3.341880341880342, "grad_norm": 43.67599868774414, "learning_rate": 3.1200000000000006e-05, "loss": 43.1937, "step": 50}, {"epoch": 3.341880341880342, "eval_loss": 4.703216552734375, "eval_runtime": 118.9009, "eval_samples_per_second": 0.252, "eval_steps_per_second": 0.252, "step": 50}, {"epoch": 4.0, "grad_norm": 1.727854609489441, "learning_rate": 3.9200000000000004e-05, "loss": 35.6463, "step": 60}, {"epoch": 4.683760683760684, "grad_norm": 2.001023530960083, "learning_rate": 4.72e-05, "loss": 33.462, "step": 70}, {"epoch": 5.3418803418803416, "grad_norm": 1.9471752643585205, "learning_rate": 5.52e-05, "loss": 33.5448, "step": 80}, {"epoch": 6.0, "grad_norm": 0.6713661551475525, "learning_rate": 6.32e-05, "loss": 33.0502, "step": 90}, {"epoch": 6.683760683760684, "grad_norm": 1.2093250751495361, "learning_rate": 7.120000000000001e-05, "loss": 33.4705, "step": 100}, {"epoch": 6.683760683760684, "eval_loss": 4.000014305114746, "eval_runtime": 112.7673, "eval_samples_per_second": 0.266, "eval_steps_per_second": 0.266, "step": 100}, {"epoch": 7.3418803418803416, "grad_norm": 1.9057722091674805, "learning_rate": 7.92e-05, "loss": 32.6347, "step": 110}, {"epoch": 8.0, "grad_norm": 1.0933998823165894, "learning_rate": 7.752765343689938e-05, "loss": 33.2573, "step": 120}, {"epoch": 8.683760683760683, "grad_norm": 1.4274835586547852, "learning_rate": 6.937290037742743e-05, "loss": 33.7891, "step": 130}, {"epoch": 9.341880341880342, "grad_norm": 1.5930325984954834, "learning_rate": 5.674638950149713e-05, "loss": 32.8111, "step": 140}, {"epoch": 10.0, "grad_norm": 1.8086457252502441, "learning_rate": 4.157039263036275e-05, "loss": 32.5246, "step": 150}, {"epoch": 10.0, "eval_loss": 3.998192071914673, "eval_runtime": 115.723, "eval_samples_per_second": 0.259, "eval_steps_per_second": 0.259, "step": 150}, {"epoch": 10.683760683760683, "grad_norm": 1.2454215288162231, "learning_rate": 2.6155317716900274e-05, "loss": 33.5843, "step": 160}, {"epoch": 11.341880341880342, "grad_norm": 1.1731630563735962, "learning_rate": 1.284797017868233e-05, "loss": 32.6058, "step": 170}, {"epoch": 12.0, "grad_norm": 1.241673469543457, "learning_rate": 3.674273046996746e-06, "loss": 32.6826, "step": 180}], "logging_steps": 10, "max_steps": 180, "num_input_tokens_seen": 0, "num_train_epochs": 12, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 6.945694118210765e+16, "train_batch_size": 1, "trial_name": null, "trial_params": null}