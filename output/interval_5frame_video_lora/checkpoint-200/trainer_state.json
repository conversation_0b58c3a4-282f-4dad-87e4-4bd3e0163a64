{"best_global_step": 200, "best_metric": 4.827417373657227, "best_model_checkpoint": "output/interval_5frame_video_lora/checkpoint-200", "epoch": 6.666666666666667, "eval_steps": 50, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.3333333333333333, "grad_norm": NaN, "learning_rate": 3.0000000000000004e-07, "loss": 103.0658, "step": 10}, {"epoch": 0.6666666666666666, "grad_norm": NaN, "learning_rate": 2.7e-06, "loss": 104.7147, "step": 20}, {"epoch": 1.0, "grad_norm": 868.709228515625, "learning_rate": 5.4e-06, "loss": 98.2075, "step": 30}, {"epoch": 1.3333333333333333, "grad_norm": 1014.7213134765625, "learning_rate": 8.400000000000001e-06, "loss": 85.1698, "step": 40}, {"epoch": 1.6666666666666665, "grad_norm": 256.23583984375, "learning_rate": 1.11e-05, "loss": 73.2608, "step": 50}, {"epoch": 1.6666666666666665, "eval_loss": 8.323232650756836, "eval_runtime": 278.5524, "eval_samples_per_second": 0.215, "eval_steps_per_second": 0.215, "step": 50}, {"epoch": 2.0, "grad_norm": 130.4470672607422, "learning_rate": 1.4099999999999999e-05, "loss": 60.3216, "step": 60}, {"epoch": 2.3333333333333335, "grad_norm": 98.13468170166016, "learning_rate": 1.71e-05, "loss": 51.0111, "step": 70}, {"epoch": 2.6666666666666665, "grad_norm": 8.862675666809082, "learning_rate": 2.01e-05, "loss": 42.3351, "step": 80}, {"epoch": 3.0, "grad_norm": 5.463959693908691, "learning_rate": 2.3100000000000002e-05, "loss": 38.7006, "step": 90}, {"epoch": 3.3333333333333335, "grad_norm": 2.909747362136841, "learning_rate": 2.61e-05, "loss": 38.0479, "step": 100}, {"epoch": 3.3333333333333335, "eval_loss": 4.869036674499512, "eval_runtime": 329.4181, "eval_samples_per_second": 0.182, "eval_steps_per_second": 0.182, "step": 100}, {"epoch": 3.6666666666666665, "grad_norm": 5.2604241371154785, "learning_rate": 2.91e-05, "loss": 38.3883, "step": 110}, {"epoch": 4.0, "grad_norm": 4.824235916137695, "learning_rate": 2.981532510892707e-05, "loss": 37.6292, "step": 120}, {"epoch": 4.333333333333333, "grad_norm": 12.701542854309082, "learning_rate": 2.8921724827785584e-05, "loss": 38.0624, "step": 130}, {"epoch": 4.666666666666667, "grad_norm": 3.1558682918548584, "learning_rate": 2.733003113171865e-05, "loss": 37.4385, "step": 140}, {"epoch": 5.0, "grad_norm": 7.534368515014648, "learning_rate": 2.512005818898112e-05, "loss": 37.9388, "step": 150}, {"epoch": 5.0, "eval_loss": 4.831944465637207, "eval_runtime": 314.1018, "eval_samples_per_second": 0.191, "eval_steps_per_second": 0.191, "step": 150}, {"epoch": 5.333333333333333, "grad_norm": 8.281946182250977, "learning_rate": 2.2402623270965204e-05, "loss": 38.1191, "step": 160}, {"epoch": 5.666666666666667, "grad_norm": 3.504448175430298, "learning_rate": 1.9313989911480257e-05, "loss": 38.7701, "step": 170}, {"epoch": 6.0, "grad_norm": 3.096174955368042, "learning_rate": 1.6009035084180595e-05, "loss": 36.2485, "step": 180}, {"epoch": 6.333333333333333, "grad_norm": 4.2694807052612305, "learning_rate": 1.2653483024396535e-05, "loss": 37.5971, "step": 190}, {"epoch": 6.666666666666667, "grad_norm": 3.1256988048553467, "learning_rate": 9.415595123970822e-06, "loss": 38.2864, "step": 200}, {"epoch": 6.666666666666667, "eval_loss": 4.827417373657227, "eval_runtime": 293.3187, "eval_samples_per_second": 0.205, "eval_steps_per_second": 0.205, "step": 200}], "logging_steps": 10, "max_steps": 240, "num_input_tokens_seen": 0, "num_train_epochs": 8, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 8.339649673400525e+16, "train_batch_size": 1, "trial_name": null, "trial_params": null}