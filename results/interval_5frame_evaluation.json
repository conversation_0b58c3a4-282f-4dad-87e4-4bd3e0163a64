{"method": "Interval 5-Frame Video Detection + Linear Interpolation", "description": "隔10取1，每5帧检测 + 线性推理", "sequences": ["data02", "data19"], "individual_results": [{"sequence": "data02", "total_gt": 763, "total_pred": 74, "direct_detections": 74, "interpolated_detections": 0, "true_positives": 0, "precision": 0.0, "recall": 0.0, "f1": 0, "iou_threshold": 0.5}, {"sequence": "data19", "total_gt": 1500, "total_pred": 144, "direct_detections": 144, "interpolated_detections": 0, "true_positives": 0, "precision": 0.0, "recall": 0.0, "f1": 0, "iou_threshold": 0.5}], "overall": {"total_gt": 2263, "total_pred": 218, "total_tp": 0, "precision": 0.0, "recall": 0.0, "f1": 0}}