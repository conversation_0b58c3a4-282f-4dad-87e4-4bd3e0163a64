{"method": "Interval 5-Frame Video Detection + Linear Interpolation", "description": "隔10取1，每5帧检测 + 线性推理", "sequences": ["data02", "data19"], "individual_results": [{"sequence": "data02", "total_gt": 763, "total_pred": 615, "direct_detections": 75, "interpolated_detections": 540, "true_positives": 0, "precision": 0.0, "recall": 0.0, "f1": 0, "iou_threshold": 0.5}, {"sequence": "data19", "total_gt": 1500, "total_pred": 1230, "direct_detections": 150, "interpolated_detections": 1080, "true_positives": 205, "precision": 0.16666666666666666, "recall": 0.13666666666666666, "f1": 0.15018315018315015, "iou_threshold": 0.5}], "overall": {"total_gt": 2263, "total_pred": 1845, "total_tp": 205, "precision": 0.1111111111111111, "recall": 0.0905877154220062, "f1": 0.09980525803310612}}