{"comparison_results": {"interval_frame": {"name": "抽帧序列方式 (Interval Frame)", "description": "每隔10帧取1帧组成训练数据，时序插值检测", "overall_performance": {}, "individual_results": []}, "true_video": {"name": "真正视频方式 (True Video)", "description": "使用Qwen2.5-VL原生视频处理能力", "overall_performance": {"total_gt": 2263, "total_pred": 42, "total_tp": 0, "precision": 0.0, "recall": 0.0, "f1": 0}, "individual_results": [{"sequence": "data02", "total_gt": 763, "total_pred": 1, "true_positives": 0, "precision": 0.0, "recall": 0.0, "f1": 0, "iou_threshold": 0.5}, {"sequence": "data19", "total_gt": 1500, "total_pred": 41, "true_positives": 0, "precision": 0.0, "recall": 0.0, "f1": 0, "iou_threshold": 0.5}]}}, "analysis": {"summary": {"best_method": null, "best_f1": 0, "methods_performance": {"interval_frame": {"name": "抽帧序列方式 (Interval Frame)", "f1": 0, "precision": 0, "recall": 0, "total_gt": 0, "total_pred": 0, "total_tp": 0}, "true_video": {"name": "真正视频方式 (True Video)", "f1": 0, "precision": 0.0, "recall": 0.0, "total_gt": 2263, "total_pred": 42, "total_tp": 0}}}, "detailed_analysis": {"f1_difference": 0, "precision_difference": 0.0, "recall_difference": 0.0, "detection_count_difference": -42}, "recommendations": ["真正视频方式表现更好，建议深入优化视频处理流程"]}, "timestamp": "/home/<USER>/weak_target_finetune"}